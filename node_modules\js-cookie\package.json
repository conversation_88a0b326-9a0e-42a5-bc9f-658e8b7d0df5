{"_from": "js-cookie@3.0.5", "_id": "js-cookie@3.0.5", "_inBundle": false, "_integrity": "sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==", "_location": "/js-cookie", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "js-cookie@3.0.5", "name": "js-cookie", "escapedName": "js-cookie", "rawSpec": "3.0.5", "saveSpec": null, "fetchSpec": "3.0.5"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/js-cookie/-/js-cookie-3.0.5.tgz", "_shasum": "0b7e2fd0c01552c58ba86e0841f94dc2557dcdbc", "_spec": "js-cookie@3.0.5", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>"}, "browser": "dist/js.cookie.js", "bugs": {"url": "https://github.com/js-cookie/js-cookie/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A simple, lightweight JavaScript API for handling cookies", "devDependencies": {"@rollup/plugin-terser": "^0.4.0", "browserstack-runner": "github:browserstack/browserstack-runner#1e85e559951bdf97ffe2a7c744ee67ca83589fde", "eslint": "^7.31.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-html": "^7.0.0", "eslint-plugin-markdown": "^3.0.0", "grunt": "^1.0.4", "grunt-compare-size": "^0.4.2", "grunt-contrib-connect": "^3.0.0", "grunt-contrib-nodeunit": "^5.0.0", "grunt-contrib-qunit": "^7.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-exec": "^3.0.0", "gzip-js": "^0.3.2", "prettier": "^2.3.2", "qunit": "^2.9.3", "release-it": "^15.0.0", "rollup": "^3.17.2", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-license": "^3.0.0", "standard": "^17.0.0"}, "directories": {"test": "test"}, "engines": {"node": ">=14"}, "exports": {".": {"import": "./dist/js.cookie.mjs", "require": "./dist/js.cookie.js"}, "./package.json": "./package.json"}, "files": ["index.js", "dist/**/*"], "homepage": "https://github.com/js-cookie/js-cookie#readme", "jsdelivr": "dist/js.cookie.min.js", "keywords": ["cookie", "cookies", "browser", "amd", "commonjs", "client", "js-cookie", "browserify"], "license": "MIT", "module": "dist/js.cookie.mjs", "name": "js-cookie", "repository": {"type": "git", "url": "git://github.com/js-cookie/js-cookie.git"}, "scripts": {"dist": "rm -rf dist/* && rollup -c", "format": "grunt exec:format", "release": "release-it", "test": "grunt test"}, "unpkg": "dist/js.cookie.min.js", "version": "3.0.5"}