<template>
  <view class="mx-20" style="height:100%">
    <view class="grace-box-banner gui-flex pb-20 l-timeline-b ">
      <scroll-view scroll-x class="navscroll">
        <view class="garce-items  fs-32" v-for="(item,index) in medicalListTab" @tap="changInfo(item.id)">
          <view class="line2 " :class="infokey==item.id?'active':''">{{item.name}}</view>
        </view>
      </scroll-view>
    </view>
    <!-- 使用滚动区域来实现主体内容区域 -->
    <scroll-view :scroll-y="true" style="height:calc(100% - 45px);padding-top: 20rpx;">
      <!-- #ifdef H5 -->
      <view v-if="infokey == 2" class="">
        <view class="l-timeline-l" v-for="(item,index) in medicalList">
          <view class="time-line">
            <view class="l-icon"></view>
            <view class="time-line-i"></view>
          </view>
          <view style="flex: 1;padding-left: 20rpx;">
            <view class="visitDate-row l-time fs-26">
              <view>{{item.visitDate}}</view>
              <!-- 标题 -->
              <view class="gui-accordion-title gui-flex gui-rows gui-nowrap gui-align-items-center"
                    @tap="changeTo(index)">
                <text class=" gui-color-blue fs-26">详情</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
                      v-if="currentIndex != index">&#xe603;</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
                      v-else>&#xe654;</text>
              </view>
            </view>

            <view class="l-content">
              <view v-if="currentIndex == index">
                <view v-if="item.deptName && item.doctorName">
                  {{item.deptName}} / {{item.doctorName}}
                </view>
                <view class="gui-accordion" v-if="currentIndex == index">
                  <!-- 主体 -->
                  <view class="fs-26">
                    <view class="gui-flex gui-rows list-item" v-if="!item.extraData">
                      <view class="list-item-name">主诉：</view>
                      <view class="gui-rows-title">{{item.complaints||"无"}}</view>
                    </view>
                    <view class="gui-flex  gui-rows list-item" v-if="!item.extraData">
                      <view class="list-item-name">现病史：</view>
                      <view class="gui-rows-title">{{item.medHist||"无"}}</view>
                    </view>
                    <view class="gui-flex  gui-rows list-item" v-if="!item.extraData">
                      <view class="list-item-name">治疗方案：</view>
                      <view class="gui-rows-title">{{item.treatmentRec||"无"}}</view>
                    </view>
                    <view class="gui-flex  gui-rows list-item" v-if="!item.extraData">
                      <view class="list-item-name">病历内容：</view>
                      <view class="gui-rows-title">{{item.dischargeRec||"无"}}</view>
                    </view>
                    <view class="gui-flex  gui-rows list-item" v-if="!item.extraData">
                      <view class="list-item-name">查体：</view>
                      <view class="gui-rows-title">{{item.phyExamination||"无"}}</view>
                    </view>
									</view>
                  <!-- 显示上传的图片 -->
                  <view class="gui-flex gui-rows list-item" v-if="item.source === '1'">
                    <view class="gui-flex gui-space-between w-100">
                      <view class="" style="width: 75%;">
                        <view class="uploaded-images">
                          <image
                              v-for="(imgUrl, imgIndex) in getImageUrls(item.extraData)"
                              :key="imgIndex"
                              :src="imgUrl"
                              class="uploaded-image"
                              mode="aspectFill"
                              @tap="previewImage(imgIndex, getImageUrls(item.extraData))"
                          />
                        </view>
                      </view>
                      <view class="gui-flex fs-28" style="width: 25%;">
                        <view @tap="editMedicalRecord(item, '1')" style="color: #409eff;">修改</view>
                        <view @tap="deleteMedicalRecord(item, '1')" style="margin-left: 20rpx;color: #f56c6c;">删除</view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <view v-if="infokey == 1" class="">
        <view class="l-timeline-l" v-for="(item,index) in medicationList">
          <view class="time-line">
            <view class="l-icon"></view>
            <view class="time-line-i"></view>
          </view>
          <view style="flex: 1;padding-left: 20rpx;">

            <view class="l-time fs-26 visitDate-row">
              <view>
                {{item[0].giverTime}}
              </view>
              <view class="gui-accordion-title gui-flex gui-rows gui-nowrap gui-align-items-center"
                    @tap="changeTo(index)">
                <text class=" gui-color-blue fs-26">详情</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
                      v-if="currentIndex != index">&#xe603;</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
                      v-else>&#xe654;</text>
              </view>
            </view>
            <view class="l-content">
              <!-- 科室名称和医生姓名 - 单独显示但受展开状态控制 -->
              <view v-if="item[0].deptName && item[0].giverName && currentIndex == index" class="mb-10">
                {{item[0].deptName}} / {{item[0].giverName}}
              </view>

              <view class="gui-accordion"  v-if="currentIndex == index">
                <!-- 标题 -->
                <view style="color: #e69972;" @tap="changeTo(index)" v-if="!item[0].extraData">
                  ({{item[0].frequency}}{{domainType == 2?',共'+(item[0].packValue||0)+item[0].packUnit:''}})
                </view>
                <!-- 主体 -->
                <!-- TODO: 修改 -->
                <view class="fs-24 gui-flex gui-rows gui-wrap w-100" v-if="currentIndex == index">
                  <view v-for="(items,index) in item" class="gui-flex gui-rows list-item w-100">
                    <view class=" w-90 mr-10 fs-28 gui-flex1" v-if="!items.extraData">
                      {{items.medicationName}}
                      {{items.doseValue}} {{domainType == 2?items.doseUnit:''}}
                    </view>
                    <view class="gui-flex gui-space-between w-100" v-if="items.source === '1'">
                      <view class="" style="width: 75%;">
                        <view class="uploaded-images">
                          <image
                              v-for="(imgUrl, imgIndex) in getImageUrls(items.extraData)"
                              :key="imgIndex"
                              :src="imgUrl"
                              class="uploaded-image"
                              mode="aspectFill"
                              @tap="previewImage(imgIndex, getImageUrls(items.extraData))"
                          />
                        </view>
                      </view>
                      <view class="gui-flex fs-28" style="width: 25%;">
                        <view @tap="editMedicalRecord(items, '2')" style="color: #409eff;">修改</view>
                        <view @tap="deleteMedicalRecord(items, '2')" style="margin-left: 20rpx;color: #f56c6c;">删除</view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <view v-if="infokey == 3" class="">
        <view class="l-timeline-l" v-for="(item,index) in monitorList">
          <view class="time-line">
            <view class="l-icon"></view>
            <view class="time-line-i"></view>
          </view>
          <view style="flex: 1;padding-left: 20rpx;">
            <view class="l-time fs-26 visitDate-row">
              <view>
                {{item[0].time}}
                <!-- <span class="ml-20 fs-26">治疗</span> -->
              </view>
              <view class="gui-accordion-title gui-flex gui-rows gui-nowrap gui-align-items-center"
                    @tap="changeTo(index)">
                <text class="gui-accordion-title-text gui-color-blue fs-26">详情</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray  gui-text-right"
                      v-if="currentIndex != index">&#xe603;</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
                      v-else>&#xe654;</text>
              </view>
            </view>
            <!-- <view class="l-icon"></view> -->
            <view class="pb-30 l-content">
              <view class="gui-accordion" v-if="currentIndex == index">
                <!-- 标题 -->
                <view v-if="item[0].deptName && item[0].doctorName">
                  {{item[0].deptName}} / {{item[0].doctorName}}
                </view>
                <!-- 主体 -->
                <view class="fs-24">
                  <view v-if="items.type == 2" v-for="(items,index) in item"
                        class="gui-flex gui-rows">
                    <view class=" w-90 mr-10 fs-26" style="line-height: 24px;">{{items.monitorName}}
                      执行时间：{{dateFormat(items.startDate)}}至{{dateFormat(items.startDate)}}
                    </view>
                  </view>
                  <view v-if="items.type == 1" v-for="(items,index) in item"
                        class="gui-flex gui-rows">
                    <view style="line-height: 24px;" class=" w-90 mr-10 fs-26">{{items.monitorName}}
                      {{items.remark}}
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <view v-if="infokey == 4" class="">
        <view class="l-timeline-l" v-for="(item,index) in inspectList">
          <view class="time-line">
            <view class="l-icon"></view>
            <view class="time-line-i"></view>
          </view>
          <view style="flex: 1;padding-left: 20rpx;">
            <view class="visitDate-row  l-time fs-26">
              <view>
                {{item[0].time}}
              </view>
              <!-- 标题 -->
              <view class="gui-accordion-title gui-flex gui-rows gui-nowrap gui-align-items-center"
                    @tap="changeTo(index)">
                <text class=" gui-color-blue fs-26">详情</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
                      v-if="currentIndex != index">&#xe603;</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
                      v-else>&#xe654;</text>
              </view>
            </view>
            <!-- <view class="l-icon"></view> -->
            <view class="l-content">
              <view class="gui-accordion" v-if="currentIndex == index">
                <!-- 主体 -->
                <view class="fs-24">
                  <view v-for="(items,index) in item" class="gui-flex gui-rows list-item">
                    <view v-if="!items.extraData" class="gui-flex  gui-align-items-center item-row">
                      <view class="w-70 fs-26">
                        {{items.applyItems}}
                      </view>
                      <button class="fs-26  bcolor gui-color-white" @click="report(items)">
                        查看报告
                      </button>
                    </view>

                    <!-- 本系统上传的图片数据显示 -->
                    <view v-if="items.source === '1'" class="gui-flex gui-space-between w-100">
                      <view class="" style="width: 75%;">
                        <view class="uploaded-images">
                          <image
                              v-for="(imgUrl, imgIndex) in getImageUrls(items.extraData)"
                              :key="imgIndex"
                              :src="imgUrl"
                              class="uploaded-image"
                              mode="aspectFill"
                              @tap="previewImage(imgIndex, getImageUrls(items.extraData))"
                          />
                        </view>
                      </view>
                      <view class="gui-flex fs-28" style="width: 25%;">
                        <view @tap="editMedicalRecord(items, '3')" style="color: #409eff;">修改</view>
                        <view @tap="deleteMedicalRecord(items, '3')" style="margin-left: 20rpx;color: #f56c6c;">删除</view>
                      </view>
                    </view>
                  </view>
                </view>
                <!-- 弹窗 -->
                <gui-popup ref="guipopup1">
                  <view class="gui-relative gui-box-shadow gui-img-in">
                    <gui-image src="https://cmsuse.oss-cn-beijing.aliyuncs.com/g5/16.png"
                               :width="580"></gui-image>
                    <!-- 	关闭按钮 -->
                    <text
                        class="gui-block-text demo-close gui-icons gui-color-white gui-absolute-rt"
                        @tap.stop="close1">&#xe78a;</text>
                  </view>
                </gui-popup>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <view v-if="infokey == 5" class="">
        <view class="l-timeline-l" v-for="(item,index) in examinationList">
          <view class="time-line">
            <view class="l-icon"></view>
            <view class="time-line-i"></view>
          </view>
          <view style="flex: 1;padding-left: 20rpx;">
            <view class="l-time fs-26 visitDate-row">
              <view>
                {{item[0].time}}
              </view>
              <view class="gui-accordion-title gui-flex gui-rows gui-nowrap gui-align-items-center"
                    @tap="changeTo(index)">
                <text class="gui-accordion-title-text gui-block-text gui-color-blue fs-26">详情</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
                      v-if="currentIndex != index">&#xe603;</text>
                <text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
                      v-else>&#xe654;</text>
              </view>
            </view>
            <view class="l-content">
              <view class="gui-accordion" v-if="currentIndex == index">
                <!-- 标题 -->

                <!-- 主体 -->
                <view class="fs-24">
                  <view v-for="(items,index) in item" class="gui-flex gui-rows">
                    <!-- HIS系统数据显示 -->
                    <view v-if="!items.extraData" class="gui-flex  gui-align-items-center item-row">
                      <view class="w-70 fs-26" style="line-height: 60rpx;">
                        {{items.applyItems}}
                      </view>
                      <button class="fs-26  bcolor gui-color-white" @click="jyreport(items)">
                        查看报告
                      </button>
                    </view>

                    <!-- 本系统上传的图片数据显示 -->
                    <view v-if="items.source === '1'" class="gui-flex gui-space-between w-100">
                      <view class="" style="width: 75%;">
                        <view class="uploaded-images">
                          <image
                              v-for="(imgUrl, imgIndex) in getImageUrls(items.extraData)"
                              :key="imgIndex"
                              :src="imgUrl"
                              class="uploaded-image"
                              mode="aspectFill"
                              @tap="previewImage(imgIndex, getImageUrls(items.extraData))"
                          />
                        </view>
                      </view>
                      <view class="gui-flex fs-28" style="width: 25%;">
                        <view @tap="editMedicalRecord(items, '4')" style="color: #409eff;">修改</view>
                        <view @tap="deleteMedicalRecord(items, '4')" style="margin-left: 20rpx;color: #f56c6c;">删除</view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- #endif -->
      <view v-if="infokey == 6">
        <view class="">
          <view class="p-15">
            <image class="resuimg" src="@/static/log/bookmark.svg"/>
            <text class="resutext"> 测评结果：</text>
          </view>
          <!-- 结果标题 -->
          <view style="margin:10px;text-indent:2em" v-if="questionResult.typePc" >
            根据测评您的体质
            <template v-if="questionResult.typePc[3].length">
              <template
                  v-if="questionResult.typePc && questionResult.typePc[0].length && !questionResult.typePc[2].length">
                是{{ questionResult.typePc[0].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}，有{{ questionResult.typePc[3].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}倾向。
              </template>
              <template
                  v-if="questionResult.typePc && !questionResult.typePc[0].length && questionResult.typePc[1].length">
                基本是{{ questionResult.typePc[1].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}，有{{ questionResult.typePc[3].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}倾向。
              </template>
              <template
                  v-if="questionResult.typePc && !questionResult.typePc[0].length && questionResult.typePc[2].length">
                是{{ questionResult.typePc[2].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}，有{{ questionResult.typePc[3].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}倾向。
              </template>
            </template>
            <template v-if="!questionResult.typePc[3].length">
              <template
                  v-if="questionResult.typePc && questionResult.typePc[0].length && !questionResult.typePc[2].length">
                是{{ questionResult.typePc[0].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}。
              </template>
              <template
                  v-if="questionResult.typePc && !questionResult.typePc[0].length && questionResult.typePc[1].length">
                基本是{{ questionResult.typePc[1].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}。
              </template>
              <template
                  v-if="questionResult.typePc && !questionResult.typePc[0].length && questionResult.typePc[2].length">
                是{{ questionResult.typePc[2].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}。
              </template>
            </template>
            <template
                v-if="questionResult.typePc && (!questionResult.typePc[0].length && !questionResult.typePc[1].length && !questionResult.typePc[2].length) && questionResult.typePc[3].length">
              有{{ questionResult.typePc[3].join(',').replace(/评估问卷/g,"").replace(/,/g,"、") }}倾向。
            </template>
          </view>
        </view>
      </view>
      <!-- 版本升级 -->
      <gui-empty
          v-if="loadingText == '暂无数据'">
        <view slot="img" class="gui-flex gui-rows gui-justify-content-center">
          <!-- 请根据您的项目要求制作并更换为空图片 -->
          <image class="gui-empty-img"
                 src="https://images.weserv.nl/?url=https://upload-images.jianshu.io/upload_images/15372054-1f849183cebb80b1.png">
          </image>
        </view>
        <text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top"
              style="color:#9DABFF;">{{loadingText}}......</text>
      </gui-empty>
    </scroll-view>

    <!-- 悬浮上传按钮 -->
    <view class="upload-btn" @tap="goToUploadPage">
      <text class="upload-btn-text">+上传</text>
    </view>

    <!-- 删除原来的弹出窗口 -->
  </view>
</template>

<script>
import { addMedicalReport, delMedicalReport } from '@/api/patient.js'
import guiUploadImages from '@/GraceUI5/components/gui-upload-images.vue'

export default {
  components: {
    guiUploadImages
  },
  props: {
    info: {
      type: Object,
      default: {}
    },
    mainHeight: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      /* 测评结果 */
      questionResult: {
        type: undefined,
        proposalList: []
      },
      titleArray: ["一", "二", "三", "四", "五"],
      domainType: this.$common.domainType,
      medicalListTab: [{
        id: 2,
        name: '门诊病历'
      },
        {
          id: 1,
          name: '处方'
        },
        {
          id: 3,
          name: '治疗'
        },
        {
          id: 4,
          name: '检查'
        },
        {
          id: 5,
          name: '检验'
        },
        {
          id: 6,
          name: '体质测评'
        },
      ],
      infokey: 2,
      // 门诊病历列表
      medicalList: [],
      // 处方列表
      medicationList: [],
      // 康养列表
      monitorList: [],
      // 检查列表
      inspectList: [],
      // 检验列表
      examinationList: [],
      currentIndex: 0,
      loadingText:'数据加载中'
    }
  },
  computed: {
    canSubmit() {
      return this.currentCategory && this.uploadImages.length > 0 && this.visitDate.date && this.visitDate.time;
    }
  },
  watch: {
    info:{
      immediate: true,
      deep: true,
      handler(newValue, oldValue) {
        console.log('监听患者信息',newValue)
        this.changInfo(this.infokey)
      }
    }
  },
  mounted() {
    // 监听上传成功事件
    uni.$on('refreshMedicalData', this.handleRefreshData);
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    uni.$off('refreshMedicalData', this.handleRefreshData);
  },
  methods: {
    // 体质测评
    tzcontent() {
      this.$common.RequestData({
        url: this.$common.fillContent + this.info.patientId,
        data: {},
        method: 'get',
      }, res => {
        if (res.data) {
          this.questionResult = res.data?.questionResult || {
            type: undefined,
            proposalList: []
          }
        }
        if (this.questionResult.length <=0) {
          this.loadingText = '暂无数据'
        }
      })
    },
    report(e) {
      this.$emit('report', e)
    },
    jyreport(e) {
      this.$emit('jyreport', e)
    },
    changeTo: function(idx) {
      if (this.currentIndex == idx) {
        this.currentIndex = -1;
      } else {
        this.currentIndex = idx;
      }
    },
    dateFormat(val) {
      return this.$common.parseTime(val, '{y}-{m}-{d}')
    },
    changInfo(e) {
      console.log('55555',e)
      this.infokey = e;
      this.currentIndex = 0
      this.getMedicalList()
    },
    // 门诊病历
    getMedicalList() {
      this.loadingText = '数据加载中';
      console.log('我是门诊详情')
      // console.log('患者信息==',this.info)
      let _this = this;
      var e = this.infokey
      if (e == 2 && this.info.patientId) {
        // console.log('我是门诊病历2');
        this.$common.RequestData({
          url: this.$common.getMedicalList,
          data: {
            patientId:this.info.patientId,
            isAsc: 'desc',
            orderByColumn: 'visit_date',
            type: '1'
          },
          method: 'get'
        }, res => {
          this.medicalList = res.rows
          if (this.medicalList.length <=0) {
            this.loadingText = '暂无数据'
          }
          // console.log(res.rows)
          // console.log('门诊病历2的数据',this.medicalList);
        })
      } else if (e == 1 && this.info.patientId) {
        // 处方
        this.$common.RequestData({
          url: this.$common.getMedicationList,
          data: {
            patientId: this.info.patientId,
            isAsc: this.type,
            orderByColumn: 'apply_time',
            type: '1',
            medicationTypeStr: '1,2,3'
          },
          method: 'get'
        }, res => {
          this.medicationList = res.rows
          if (this.medicationList.length <=0) {
            this.loadingText = '暂无数据'
          }
        })
      } else if (e == 3 && this.info.patientId) {
        // 治疗
        this.$common.RequestData({
          url: this.$common.getMonitorList,
          data: {
            patientId: this.info.patientId,
            isAsc: this.type,
            orderByColumn: 'apply_time',
            type: '1',
            medicationTypeStr: '0'
          },
          method: 'get'
        }, res => {
          this.monitorList = res.rows
          if (this.monitorList.length <=0) {
            this.loadingText = '暂无数据'
          }
        })
      } else if (e == 4 && this.info.patientId) {
        // 检查
        this.$common.RequestData({
          url: this.$common.getInspectList,
          data: {
            patientId: this.info.patientId,
            isAsc: this.type,
            orderByColumn: 'apply_time',
            type: '1'
          },
          method: 'get'
        }, res => {
          this.inspectList = res.rows
          if (this.inspectList.length <=0) {
            this.loadingText = '暂无数据'
          }
        })
      } else if (e == 5 && this.info.patientId) {
        // 检验
        this.$common.RequestData({
          url: this.$common.getExaminationList,
          data: {
            patientId: this.info.patientId,
            isAsc: this.type,
            orderByColumn: 'apply_time',
            type: '1'
          },
          method: 'get'
        }, res => {
          this.examinationList = res.rows || []
          if (this.examinationList.length <=0) {
            this.loadingText = '暂无数据'
          }
        })
      } else if (e == 6 && this.info.patientId) {
        this.tzcontent()
      }

    },

    goToUploadPage() {
      uni.navigateTo({
        url: `/pages/patient/uploadMedical?patientId=${this.info.patientId}`
      });
    },

    // 处理上传成功后的刷新事件
    handleRefreshData(data) {
      if (data && data.categoryId) {
        this.changInfo(data.categoryId);
      }
    },

    // 删除原来的弹出窗口
    close1() {
      this.$refs.guipopup1.close();
    },
    // 处理图片URL字符串，支持多张图片
    getImageUrls(extraData) {
      if (!extraData) return [];
      // 如果是逗号分隔的多个URL，过滤空字符串
      return extraData.split(',').filter(url => url.trim()).map(url => url.trim());
    },

    // 图片预览
    previewImage(current, urls) {
      uni.previewImage({
        current: current,
        urls: urls,
        loop: true
      });
    },

    // 编辑医疗记录
    editMedicalRecord(item, type) {
      // 根据不同类型获取正确的访问日期
      let visitDate = '';

      switch(type) {
        case '1': // 病历
          visitDate = item.visitDate || '';
          break;
        case '2': // 处方
          visitDate = item.giverTime || '';
          break;
        case '3': // 检查
        case '4': // 检验
          visitDate = item.time || '';
          break;
        default:
          visitDate = '';
      }

      uni.navigateTo({
        url: `/pages/patient/uploadMedical?patientId=${this.info.patientId}&type=${type}&id=${item.id || item[0].id}&mode=edit&extraData=${encodeURIComponent(item.extraData || item[0].extraData)}&visitDate=${encodeURIComponent(visitDate)}`
      });
    },

    // 删除医疗记录
    deleteMedicalRecord(item, type) {
      uni.showModal({
        title: '提示',
        content: '确定删除此条记录吗？',
        success: (res) => {
          if (res.confirm) {
            delMedicalReport(item.id, type).then(res => {
              if (res.code === 200) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                this.getMedicalList();
              } else {
                uni.showToast({
                  title: res.msg || '删除失败',
                  icon: 'none'
                });
              }
            }).catch(() => {
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              });
            });
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.item-row {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.gui-accordion {
  background-color: #f5f6f8;
  padding: 20rpx;
  border-radius: 10rpx;
}

.list-item-name {
  font-weight: bold;
}

.list-item {
  padding: 10rpx 0;
  margin-right: 20rpx;

}

.time-line {
  display: flex;
  flex-direction: column;
  width: 30rpx;
  position: relative;
  justify-content: center;
  align-items: center;

  .time-line-i {
    width: 2rpx;
    background-color: #aaa;
    height: 100%;
  }
}

.gui-rows-title {
  flex: 1
}

.visitDate-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.navscroll {
  white-space: nowrap;

  // height: 100rpx;
  ::v-deep ::-webkit-scrollbar {
    width: 4px !important;
    height: 1px !important;
    overflow: auto !important;
    background: transparent !important;
    -webkit-appearance: auto !important;
    display: block;
  }

  .garce-items {
    display: inline-block; // 设置为行内块
  }
}

.prescription {
  display: flex;
  flex-direction: row;
  flex-flow: row wrap;
}

.prescription-item {
  width: 30%;
  line-height: 26px;
  text-align: center;
}

>>>.gui-scroll-x-items {
  align-items: center;
}

.demo-nav {
  padding: 15rpx 30rpx;
}

.demo-text {
  line-height: 200rpx;
  padding-bottom: 3000px;
}

.grace-box-banner .garce-items .line2 {
  font-size: 28rpx;
  color: #008AFF;
  display: inline-block; // 设置为行内块
  border: 1px solid #62dbff;
  padding: 0 15rpx;
  margin: 0 10rpx;
  border-radius: 20rpx;
}

.line2.active {
  color: #ffffff !important;
  /* font-weight:bold; */
  background-color: #008AFF;
}

.l-timeline-l {
  display: flex;
  flex-direction: row;
  // border-left: 1px solid #aaaaaa;
}

.l-timeline-b {
  /* border-bottom: 2px solid #008AFF; */
}

.l-time {
  // position: relative;
  // top: -15rpx;
}

.acolor {
  background-color: #7784eb;

}

.bcolor {
  background-color: #7784eb;
  height: 40rpx;
  line-height: 40rpx;
  width: 190rpx;
}

.tagicon {
  margin-right: 10rpx;
  height: 40rpx;
  width: 6rpx;
  border-radius: 5rpx;
  background: #008AFF;
  display: block;
}

.l-icon {
  background: #008AFF;
  width: 25rpx;
  height: 25rpx;
  border-radius: 25rpx;
  position: absolute;
  top: 0;
  // position: relative;
  // top: -50rpx;
  // left: -15rpx;
}

.l-content {
  padding-bottom: 30px;
  margin-top: 30rpx;
  // position: relative;
  // top: -25rpx;
}

.gui-accordion-icon {
  width: 50rpx;
  // height: 80rpx;
  // line-height: 80rpx;
  font-size: 32rpx;
}

.gui-flex-direction-row {
  flex-direction: row-reverse;
}

.gui-accordion-title-text {
  // width: 200rpx;
  flex: 1;
}

>>>.gui-block-text {
  font-size: 30rpx !important;
}

.resuimg {
  width: 24px;
  height: 24px;
  font-size: 24px;
  line-height: 24px;
  vertical-align: middle;
  color: rgba(0, 186, 173, 1);
}

.resutext {
  width: 70px;
  height: 21px;
  font-size: 14px;
  text-align: left;
  font-weight: bold;
  line-height: 24px;
  padding-left: 8rpx;
  color: rgba(80, 80, 80, 1);
}

.propose_net {
  width: 100%;
  height: auto;
  margin-top: 5rpx;
  padding: 0 10rpx;
  text-indent: 2em;
  line-height: 52rpx;
  overflow-x: scroll;
}

.end_text {
  width: 325px;
  height: 42px;
  font-size: 14px;
  text-align: center;
  margin: 20% auto 0;
  color: rgba(212, 48, 48, 1);
}

.propose {
  width: 92%;

}


/* 干预 */
.ganyuFu {
  background-color: #f5f6f8;
  line-height: 50rpx;
  padding-top: 10rpx;
  width: 85%;
  max-width: 85%;
  border-radius: 10rpx;
  padding: 5rpx 10rpx 5rpx 5rpx;
}

.ios {
  font-size: 35rpx;
  margin: 10rpx 10rpx;
  color: #1aca0d;
}

/* 疗效评估 */
.laioxiaoFu {
  /* display: flex; */
  background-color: #f5f6f8;
  line-height: 50rpx;
  padding-top: 10rpx;
  width: 85%;
  max-width: 85%;
  border-radius: 10rpx;
  padding: 5rpx 10rpx 5rpx 5rpx;
}

.laioxiaoZi {
  display: flex;
  align-items: center;
  line-height: 50rpx;
  padding: 10rpx 0rpx 10rpx 15rpx;
}

.Aios {
  font-size: 50rpx;
  margin: 10rpx 20rpx 10rpx 10rpx;
  color: #1aca0d;
  /* height: 100% !important; */
  display: flex;
  align-items: center;
  float: left;
}

.yuandian {
  float: left;
  display: block;
  line-height: 50rpx;
  top: 10px;
  width: 8px;
  height: 8px;
  border-radius: 20px;
  /* background: #cbd0db; */
  background: #6f6f6f;
  margin-top: 8px !important;
  margin: 8px;

}

/* 舌面详情按钮 */
.sm_xq {
  float: right;
  margin-right: 30rpx;
}

/* 头像 */
.head_img {
  border-radius: 10rpx;
  border: 1px solid #cdcdcd;
}

>>>.gui-list-title-text {
  font-size: 28rpx !important;
}

/* 上传按钮样式 */
.upload-btn {
  position: fixed;
  bottom: 350rpx;
  right: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #7784eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 9;
}

.upload-btn-text {
  color: #fff;
  font-size: 28rpx;
}

/* 上传数据样式 */
.uploaded-data-section {
  width: 100%;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.upload-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  float: right;
}

.upload-time {
  font-size: 28rpx;
  color: #666;
}

.upload-source {
  font-size: 24rpx;
  color: #67c23a;
  background-color: rgba(103, 194, 58, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-top: 10rpx;
}

.image-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10rpx;
}

.action-btn {
  padding: 4rpx 16rpx;
  margin-left: 15rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  color: #fff;
}

.edit-btn {
  background-color: #409eff;
}

.delete-btn {
  background-color: #f56c6c;
}

.uploaded-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  border: 1px solid #e0e0e0;
}
</style>
