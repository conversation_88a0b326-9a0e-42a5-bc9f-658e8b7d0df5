<template>
	<view class="p-30">
		<!-- <view v-if="obj.taskInfo.status === -1">
			<view style="padding:50rpx 0;">
				<text slot="text"class="gui-block-text gui-text-center gui-margin-top text-zhuti" >此内容已经被撤回...</text>
			</view>
		</view> -->
		<view>
			<view class="gui-h3 text-center fs-36 font-bold px-20 pb-60">{{questionnaireName || '-'}}</view>
			<view class="">
				<view class="mb-20" v-for="(item,index) in obj">
					<view class="fs-30 gui-bold">{{index+1}}、{{item.subjectName}}</view>
					<view v-if="item.subjectType =='image'" class="ml-40 mt-15 mr-40 mb-15">
						<view v-if="imgs" class="pb-10">使用前：</view>
						<image :src="imgs" style="width: 700rpx; height: 300rpx; "  @click="clickImg(imgs)"></image>
						<view v-if="imgAfter" class="pb-10 pt-10">使用后：</view>
						<image :src="imgAfter" style="width: 700rpx; height: 300rpx; "  @click="clickImg(imgAfter)"></image>
					</view>
					<view v-else class="ml-40 mt-15 gui-border mr-40 p-20" style="color: #7485eb;min-height: 60rpx;">{{item.answer ||''}}</view>
				</view>
			</view>
			<!-- <view class="fs-40 gui-bold gui-flex gui-justify-content-end mb-20 gui-bg-white">
			  <view class="pageNumstyle">{{pageNums + 1}}/{{obj.length}}</view>
			</view>
			<view :class="['animate', animations[animationIndex]]" >
				<view class="fs-36 mb-30">{{questionnaireData.subjectName}}</view>
				<view class="fs-36 cpbg mx-30" v-show="questionnaireData.subjectType != 'image' ">
					<view class="my-40 mx-60" v-if="questionnaireData.subjectType == 'radio' && questionnaireData.subjectDict">
						<view class="" v-for="(item,index) in questionnaireData.subjectDict.split(/[,，]/g)" :key="index" >
							<view class="answertext"  :class="obj[pageNums].answer === item?'saveanswer':''">
								{{item}}
							</view>
						</view>
					</view>
					<view class="my-40 mx-60 align-center" v-if="questionnaireData.subjectType == 'checkbox' && questionnaireData.subjectDict">
						<view class="" v-for="(item,index) in questionnaireData.subjectDict.split(/[,，]/g)" :key="index">
							<view class="answertext"  :class="checkData.indexOf(item) > -1?'saveanswer':''">
								{{item}}
							</view>
						</view>
						<view class="fs-26 gui-color-gray text-center">
							提示：以上选项可多选
						</view>
					</view>
					<view class="w-100" v-if="questionnaireData.subjectType == 'text'">
						<input v-model="questionnaireData.answer" style="height: 120rpx;padding-left: 20rpx;" type="text" class="gui-border fs-30" placeholder="请输入" maxlength="100" :disabled="obj.taskInfo.status != 0" />
					</view>
					<view class="w-100 pos-relative" v-if="questionnaireData.subjectType == 'textarea'">
						<textarea v-model="questionnaireData.answer" placeholder="请输入" maxlength="200" :disabled="obj.taskInfo.status != 0" class="box-size-border gui-border w-100 p-20 fs-30" placeholder-class="fs-28" />
						<view class="pos-absolute text-grey-b2" style="bottom: 50rpx;right: 50rpx;">{{questionnaireData.answer ? questionnaireData.answer.length : 0}}/200</view>
					</view>
				</view>
				<view class="ml-15 mt-20" v-show="questionnaireData.subjectType == 'image' ">
					<view class="">
						<view class="fs-36">
							使用前 ：
						</view>
						<view class="" v-if="imgs">
							<image :src="imgs" style="width: 700rpx; height: 300rpx; "  @click="clickImg(imgs)"></image>
						</view>
						<view class="" v-else>
							<gui-upload-imageq :btnName="'添加使用前的照片'" :maxFileNumber="1" ref="upbefore" uploadServerUrl="https://您的域名/地址">
							</gui-upload-imageq>
						</view>
					</view>
					<view class="mt-40">
						<view class="fs-36">
							使用后 ：
						</view>
						<view class="" v-if="imgAfter">
							<image :src="imgAfter" style="width: 700rpx; height: 300rpx; "  @click="clickImg(imgAfter)"></image>
						</view>
						<view class="" v-else>
							<gui-upload-imageq  :btnName="'添加使用后的照片'" :maxFileNumber="1" ref="upafter" uploadServerUrl="https://您的域名/地址">
							</gui-upload-imageq>
						</view>
					</view>
				</view>
			</view> -->
			<!-- <view style="position: fixed;bottom: 90rpx;width: 690rpx;">
			  <view class="gui-flex gui-row gui-justify-content-center">
			    <view @click="previousPage" v-if="pageNums > 0"
			          class="gui-flex gui-justify-content-center mt-40 mr-20">
			      <view class="contentBnt bg-white">上一页</view>
			    </view>
				v-if="(obj.taskInfo.status == 4 || obj.taskInfo.status == 1 || questionnaireData.subjectType == 'checkbox' || questionnaireData.subjectType == 'text' || questionnaireData.subjectType == 'textarea') && obj.answers.length != (pageNums+1) "
				<view @click="nextPage" 
				      class="gui-flex gui-justify-content-center mt-40 mr-20">
				  <view class="contentBnt bg-white">下一页</view>
				</view>
			    <view @click="reSubmit" v-if="obj.taskInfo.status == 0 && obj.answers.length === (pageNums+1) "
			          class="gui-flex gui-justify-content-center mt-40 ">
			      <view class="saveBnt bg-zhuti">提 交</view>
			    </view>
			  </view>
			</view> -->
			<view style="height: 180rpx;"></view>
		</view>
	</view>
</template>

<script>
	import {getFollowTaskInfo,submitQuestionnaire,getInfoDtail,taskDtail,listDtail} from '@/api/im.js'
	export default {
		data() {
			return {
				questionnaireName:'',
				animationIndex: 1,
				animations: ['fadeOutLeft · 左侧淡出', 'bounceInRight · 右侧飞入', 'bounceInLeft · 左侧飞入', 'fadeOutRight · 右侧淡出'],
				questionnaireData: {},
				/* 下一页 */
				pageNums: 0,
				/* 所有答案集合 */
				// obj.answers: [],
				lock:false,
				id:"",
				taskType:'',
				checkData:[],
				imgUrl:[],
				imgs:'',
				imgUrlAfter:[],
				imgAfter:'',
				openType:0,//上一页来源  3、咨询页
				obj:[],
				questionArr:[],
			}
		},
		onShow() {
		 //  if (uni.getStorageSync("mbobj.answers") && uni.getStorageSync("mbpageNums")) {
		 //    this.$set(this, 'pageNums', uni.getStorageSync("mbpageNums"))
		 //    this.$set(this, 'obj.answers', uni.getStorageSync("mbobj.answers"))
			// if (uni.getStorageSync("mbobj.answers").subjectType === 'checkbox') {
			// 	let answerarr = uni.getStorageSync("mbobj.answers").answer.split(/[,，]/g)
			// 	this.$set(this,'checkData',answerarr)
			// }
		 //  }
		},
		onLoad(options) {
			this.openType = options.openType
			this.id = options.id;
			this.taskType = options.taskType;
			this.getDetail();
		},
		methods:{
			clickImg(item) {
				wx.previewImage({
					urls: [item], //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
					current: '', // 当前显示图片的http链接，默认是第一个
					success: function(res) {},
					fail: function(res) {},
					complete: function(res) {},
				})
			},
			// 记录选择图片时的待提交数据
			changeImage: function(e) {
				// 数组类型
				this.imgUrl = e
			},
			// 记录选择图片时的待提交数据
			changeAfter: function(e) {
				// 数组类型
				this.imgUrlAfter = e
			},
			//预提交-需要先上传
			reSubmit() {
				// 先取出 url地址 赋值给arrs
				if(this.imgUrl.length>0){
					let arrs = [];
					this.imgUrl.forEach((item, index) => {
						arrs.push(item.url)
					})
					this.imgUrlAfter.forEach((item, index) => {
						arrs.push(item.url)
					})
					let that = this
					// 将相同的过滤出来
					let resultArr = arrs.filter(function(item, index, self) {
						// 字符匹配
						return self.indexOf(item) == index;
					})
					let arr = '';
					let c = 0
					for (let key in resultArr) {
						this.$common.uploadFile(resultArr[key], resx => {
							c += 1; //上传成功一个+1
							if (c == resultArr.length) {
								//所有图片已经传完
								arr += resx.data.url;
								that.save(arr) //提交表单
							} else {
								arr += resx.data.url + ',';
							}
						});
					}
				}else{
					this.save() 
				}
				
			},
			save(arr){
				return
				let isOk = true;
				this.obj.answers.forEach(item=>{
					if (item.subjectType == 'image') {
						item.answer = arr
					}
					if(item.isRequired == 1){
						if(!item.answer){
							isOk = false;
						}
					}
				})
				if(!isOk){return this.$common.msg("请完整填写问卷")}
				uni.showModal({
					title: '提示',
					content: '内容已填写完成，确认提交问卷吗？',
					cancelText: "取消",
					confirmText: "提交",
					confirmColor: '#C59F79',
					cancelColor: '#000000',
					success: res => {
						if (res.confirm) {
							submitQuestionnaire({
								patientId:uni.getStorageSync('mbuser').id, // 系统用户id
								id:this.id,
								taskType:this.taskType,
								questionnaireId:this.obj.taskInfo.questionnaireId,
								questionAnswers:this.obj.answers
							}).then(res=>{
								this.$common.msg("提交成功","success")
								setTimeout(()=>{
									if (this.openType == 3) {
										this.$common.navBack(1)
									} else{
										// uni.removeStorageSync("obj.answers")
										// uni.removeStorageSync("pageNums")
										this.$common.navTab('/pages/task/index')
										
										// this.getDetail();
									}
								},1000)
							})
						}
					}
				})
				
			},
			checkChange(e,index,type){
				this.obj.answers[index].answer = type == 2 ? e.detail.value.join() : e.detail.value;
			},
			getDetail(){
				taskDtail(this.id).then(res =>{
					this.questionnaireName = res.data.questionnaireName
					if (res.data) {
						let questionId = res.data.questionnaireId
						getInfoDtail(questionId,res.data.patientId,res.data.id).then(res =>{
							if (res.data.answers.length>0) {
								this.obj = res.data.answers
									console.log(this.obj)
									res.data.answers.forEach(item=>{
										if (item.subjectType == 'image' && item.answer != null) {
											let imgdata = item.answer.split(",")
											this.imgs = imgdata[0]
											this.imgAfter = imgdata[1]
											this.$forceUpdate();
										console.log('图片==',this.imgs)
										}
									})
								
							} else{
								listDtail({questionId:questionId}).then(res=>{
									this.obj = res.rows
								})
							}
						})
					}
				})
			},
			// 答案选择事件
			answer(value, data,type) {
				if( this.obj.taskInfo.status !== 0 ){
					return
				}
			  if (!this.lock) {
			    this.lock = true
			    if (!value) {
			      this.$common.msg("请完整填写问卷！")
			      return
			    }
				if (data.subjectType === 'checkbox') {
					if(typeof(this.checkData) == 'string'){
						this.checkData = this.checkData.split(',')
					}
					if(this.checkData.indexOf(value)>-1){
						this.checkData.splice(this.checkData.indexOf(value),1)
					}else{
						this.checkData.push(value)
					}
				}
				var checkData = this.checkData
			    this.$set(this.obj.answers, this.pageNums, {
			      answer: data.subjectType === 'checkbox' ? checkData.join(',') : value,
				  id:data.id,
				  isRequired:data.isRequired,
			      questionId: data.questionId,
				  scoreStandard:data.scoreStandard,
			      subjectDict: data.subjectDict,
			      subjectName: data.subjectName,
			      subjectOrder: this.pageNums,
			      subjectType: data.subjectType,
				  warming:data.warming,
			      // subjectId: data.subjectId,
			      // questionType: data.questionType,
			      // constitutionType: data.constitutionType,
			      patientId: uni.getStorageSync("mbcardObj")?.patientId || ''
			    })
			    if (data.subjectType == 'radio' && (Number(this.pageNums + 1) === this.obj.answers.length)) {
					this.save()
					return
			    }
			    if (Number(this.pageNums + 1) !== this.obj.answers.length && data.subjectType != 'checkbox') {
			      setTimeout(() => {
			        this.lock = false
			        this.nextPage()
			      }, 200);
			    } else {
			      this.lock = false
			    }
			  }
			},
			/* 下一页 */
			nextPage() {
			  // uni.setNavigationBarTitle({
			  //   title: '体质测评'
			  // })
			  this.$set(this, 'animationIndex', 0)
			  this.$set(this, 'pageNums', this.pageNums + 1)
			  setTimeout(() => {
			    this.$set(this, 'animationIndex', 1)
			  }, 200);
			  // uni.setStorageSync('mbpageNums', this.pageNums)
			  // uni.setStorageSync('mbobj.answers', this.obj.answers)
			  this.$set(this, 'questionnaireData', this.obj.answers[this.pageNums])
			  if (this.obj.answers[this.pageNums].subjectType === 'checkbox') {
			  	let answerarr = this.obj.answers[this.pageNums].answer || []
			  	this.$set(this,'checkData',answerarr||[])
			  }
			
			},
			/* 上一页 */
			previousPage() {
			  this.$set(this, 'animationIndex', 3)
			  // uni.setNavigationBarTitle({
			  //   title: '体质测评'
			  // })
			  this.$set(this, 'pageNums', this.pageNums - 1)
			  // uni.setStorageSync('mbpageNums', this.pageNums)
			  // uni.setStorageSync('mbobj.answers', this.obj.answers)
			  this.$set(this, 'questionnaireData', this.obj.answers[this.pageNums])
			  setTimeout(() => {
			    this.$set(this, 'animationIndex', 2)
			  }, 200);
			if (this.obj.answers[this.pageNums].subjectType === 'checkbox') {
				let answerarr = this.obj.answers[this.pageNums].answer || []
				this.$set(this,'checkData',answerarr||[])
			}
			},
		}
	}
</script>

<style lang="less" scoped>
/* 引入动画库 */
@import "@/GraceUI5/css/animate.css";

/* 定义动画修饰样式 */
.animate {
  animation-duration: 1s;
  animation-timing-function: linear;
}

.cpbg {
  border: 1px solid #C59F79;
  border-radius: 10rpx;
}

.pageNumstyle {
  padding: 10rpx 20rpx 10rpx 40rpx;
  border: 1px solid #C59F79;
  border-right: none;
  border-bottom-left-radius: 50rpx;
  border-top-left-radius: 50rpx;
}

.answertext {
  border: 1px solid #e5e5e6;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.saveanswer {
  background-color: #C59F79;
  color: #ffffff;
}

.contentBnt,
.saveBnt {
  border: 1px solid #C59F79;
  bottom: 30rpx;
  width: 220rpx;
  height: 80rpx;
  font-size: 34rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 5rpx;
}

.saveBnt {
  color: #fff;
}
	page{
		background: #fff;
	}
</style>
