<template>
	<gui-page :fullPage="true" :isLoading="pageLoading" ref="guiPage">
		<view slot="gBody" class="profile-container">
			<!-- 头部信息 -->
			<view class="profile-header">
				<view class="header-left">
					<view class="patient-name">{{profile.patientName}}</view>
					<view class="patient-gender">{{profile.sex == 1 ? '男' : profile.sex == 2 ? '女' : '未知'}} {{profile.age}}岁</view>
				</view>
				<!-- <view class="header-right">
					<view class="edit-btn" @click="editPatientTags">
						<text class="gui-icons">&#xe63a;</text>
						<text>患者标签</text>
					</view>
				</view> -->
			</view>
			
			<!-- 主体内容 -->
			<view class="profile-body">
				<!-- 左侧就诊数据 -->
				<view class="left-stats">
					<view class="visit-item" v-for="(item, index) in visitStats" :key="index">
						<view class="visit-tag" :style="{ backgroundColor: item.color }">{{item.name}}: {{item.count}}次</view>
					</view>
				</view>
				
				<!-- 中间人体轮廓图 -->
				<view class="body-outline">
					<image class="outline-image" src="/static/images/huanzhehuaxiang.png" mode="aspectFit"></image>
				</view>
				
				<!-- 右侧标签 -->
				<view class="right-tags">
					<view v-if="profile.crowdLabel && profile.crowdLabel.includes('Ⅱ型糖尿病')" class="tag middle-tag">
						<text>Ⅱ型糖尿病</text>
					</view>
					<view v-for="(tag, index) in medicalTags" :key="index" class="tag" :class="tag.position">
						<text>{{tag.name}}</text>
					</view>
					<view v-if="profile.crowdLabel && profile.crowdLabel.includes('急性非ST段抬高型心肌梗死')" class="tag top-tag">
						<text>急性非ST段抬高型心肌梗死</text>
					</view>
				</view>
			</view>
			
			<!-- 健康数据 -->
			<view class="health-data-container">
				<view class="monitor-count">
					<text>监测: {{jccount}}次</text>
				</view>
				
				<view class="health-metrics">
					<view class="health-item">
						<text class="item-label">血糖</text>
						<view class="item-value-container">
							<text class="item-value">{{getMonitorValue('血糖') || '--'}}</text>
							<text v-if="isAbnormal('血糖')" class="arrow-icon" :class="{'up-arrow': isHigh('血糖'), 'down-arrow': !isHigh('血糖')}">{{isHigh('血糖') ? '↑' : '↓'}}</text>
						</view>
						<text class="item-unit">mmol/L</text>
						<text class="item-date">{{getMonitorDate('血糖') || '--'}}</text>
					</view>
					<view class="health-item">
						<text class="item-label">血压</text>
						<view class="item-value-container">
							<text class="item-value">{{getMonitorValue('血压') || '--'}}</text>
							<text v-if="isAbnormal('血压')" class="arrow-icon" :class="{'up-arrow': isHigh('血压'), 'down-arrow': !isHigh('血压')}">{{isHigh('血压') ? '↑' : '↓'}}</text>
						</view>
						<text class="item-unit">mmHg</text>
						<text class="item-date">{{getMonitorDate('血压') || '--'}}</text>
					</view>
					<view class="health-item">
						<text class="item-label">心率</text>
						<view class="item-value-container">
							<text class="item-value">{{getMonitorValue('心率') || '--'}}</text>
							<text v-if="isAbnormal('心率')" class="arrow-icon" :class="{'up-arrow': isHigh('心率'), 'down-arrow': !isHigh('心率')}">{{isHigh('心率') ? '↑' : '↓'}}</text>
						</view>
						<text class="item-unit">bpm</text>
						<text class="item-date">{{getMonitorDate('心率') || '--'}}</text>
					</view>
				</view>
			</view>
			
			<!-- 底部个人信息 -->
			<view class="profile-footer">
				<view class="info-row">
					<text class="info-label">职业</text>
					<text class="info-value">{{profile.occupation && profile.occupation !== 'null' ? profile.occupation : '--'}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">婚姻状态</text>
					<text class="info-value">{{profile.marital && profile.marital !== 'null' ? profile.marital : '--'}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">现住址</text>
					<text class="info-value">{{profile.address && profile.address !== 'null' ? profile.address : '--'}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">联系人</text>
					<text class="info-value">{{profile.contacts && profile.contacts !== 'null' ? profile.contacts : '--'}}</text>   
				</view>
				<view class="info-row">
					<text class="info-label">联系电话</text>
					<text class="info-value">
						{{showFullPhone ? (profile.contactsTel || profile.tel) : maskPhone(profile.contactsTel || profile.tel)}}
						<text class="gui-icons eye-icon" @click="togglePhoneVisibility">&#xe609;</text>
					</text>
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
import { getPatientProfile } from '@/api/patient.js'

export default {
	data() {
		return {
			pageLoading: true,
			patientId: '',
			showFullPhone: false,
			profile: {
				patientId: '',
				patientName: '',
				sex: 1,
				age: 0,
				tel: '',
				idCard: '',
				marital: '',
				occupation: '',
				address: '',
				crowdLabel: '',
				contacts: '',
				contactsTel: '',
				xjcount: '0',
				sfcount: '0',
				cpcount: '0',
				jccount: '0',
				zxcount: '0',
				zycount: '0',
				mzcount: '0',
				fzcount: '0',
				monitor: []
			},
			// 就诊数据
			visitStats: [],
			jccount: '0',
			// 医疗标签
			medicalTags: [
				// { name: '自主义标签>', position: 'middle-top-tag' },
				// { name: '自主义标签>', position: 'middle-bottom-tag' }
			]
		}
	},
	created() {
		// 初始化就诊数据
		this.initVisitStats()
	},
	onLoad(option) {
		if (option.patientId) {
			this.patientId = option.patientId
			this.getPatientProfile()
		} else {
			this.pageLoading = false
			this.$common.msg('参数错误')
		}
	},
	methods: {
		// 初始化就诊统计数据
		initVisitStats() {
			this.visitStats = [
				{ name: '门诊', count: 0, color: '#3DC2BD', key: 'mzcount' },
				{ name: '住院', count: 0, color: '#3DC2BD', key: 'zycount' },
				{ name: '舌诊', count: 0, color: '#3DC2BD', key: 'sfcount' },
				{ name: '随访', count: 0, color: '#3DC2BD', key: 'sfcount' },
				{ name: '测评', count: 0, color: '#3DC2BD', key: 'cpcount' },
				{ name: '宣教', count: 0, color: '#3DC2BD', key: 'zxcount' },
				{ name: '沟通', count: 0, color: '#3DC2BD', key: 'fzcount' }
			]
		},
		
		// 获取患者画像数据
		getPatientProfile() {
			getPatientProfile(this.patientId).then(res => {
				if (res.code === 200) {
					this.profile = res.data
					this.updateVisitStats()
					this.jccount = this.profile.jccount
				} else {
					this.$common.msg(res.msg || '获取患者画像失败')
				}
				this.pageLoading = false
			}).catch(() => {
				this.pageLoading = false
				this.$common.msg('获取患者画像失败')
			})
		},
		
		// 更新就诊统计数据
		updateVisitStats() {
			this.visitStats.forEach(item => {
				if (this.profile[item.key]) {
					item.count = this.profile[item.key]
				}
			})
		},
		
		// 获取监测项的值
		getMonitorValue(name) {
			if (this.profile.monitor && this.profile.monitor.length > 0) {
				const item = this.profile.monitor.find(m => m.name === name)
				return item ? item.value : null
			}
			return null
		},
		
		// 获取监测项的日期
		getMonitorDate(name) {
			if (this.profile.monitor && this.profile.monitor.length > 0) {
				const item = this.profile.monitor.find(m => m.name === name)
				return item ? this.formatShortDate(item.date) : null
			}
			return null
		},
		
		// 判断是否异常值
		isAbnormal(name) {
			if (name === '血糖') {
				const value = this.getMonitorValue(name)
				return value && (parseFloat(value) < 3.9 || parseFloat(value) > 6.1)
			} else if (name === '血压') {
				const value = this.getMonitorValue(name)
				if (!value || value === '--') return false
				const values = value.split('/')
				if (values.length !== 2) return false
				const systolic = parseInt(values[0])
				const diastolic = parseInt(values[1])
				return systolic > 140 || diastolic > 90 || systolic < 90 || diastolic < 60
			} else if (name === '心率') {
				const value = this.getMonitorValue(name)
				if (!value || value === '--') return false
				const rate = parseInt(value)
				return rate > 100 || rate < 60
			}
			return false
		},
		
		// 判断是否偏高
		isHigh(name) {
			if (name === '血糖') {
				const value = this.getMonitorValue(name)
				return value && parseFloat(value) > 6.1
			} else if (name === '血压') {
				const value = this.getMonitorValue(name)
				if (!value || value === '--') return false
				const values = value.split('/')
				if (values.length !== 2) return false
				const systolic = parseInt(values[0])
				const diastolic = parseInt(values[1])
				return systolic > 140 || diastolic > 90
			} else if (name === '心率') {
				const value = this.getMonitorValue(name)
				if (!value || value === '--') return false
				const rate = parseInt(value)
				return rate > 100
			}
			return false
		},
		
		// 格式化短日期 (MM/DD)
		formatShortDate(dateString) {
			if (!dateString) return '-'
			const date = new Date(dateString)
			const month = (date.getMonth() + 1).toString().padStart(2, '0')
			const day = date.getDate().toString().padStart(2, '0')
			return `${month}/${day}`
		},
		
		// 格式化日期
		formatDate(dateString) {
			if (!dateString) return ''
			return this.$common.parseTime(dateString, '{y}-{m}-{d} {h}:{i}')
		},
		
		// 编辑患者标签
		editPatientTags() {
			this.$common.msg('编辑患者标签功能暂未开放')
		},
		
		// 脱敏手机号
		maskPhone(phone) {
			if (!phone) return '--';
			if (typeof phone !== 'string') phone = String(phone);
			return phone.replace(/(\d{3})\d*(\d{4})/, '$1****$2');
		},
		
		// 切换手机号显示状态
		togglePhoneVisibility() {
			this.showFullPhone = !this.showFullPhone;
		}
	}
}
</script>

<style lang="scss" scoped>
.profile-container {
	padding: 20rpx;
	background-color: rgb(239, 238, 255);
	min-height: 100vh;
}

.profile-header {
	border-radius: 16rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.header-left {
	display: flex;
	align-items: center;
}

.patient-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-right: 20rpx;
}

.patient-gender {
	font-size: 28rpx;
	color: #666;
}

.header-right {
	display: flex;
	align-items: center;
}

.edit-btn {
	display: flex;
	align-items: center;
	color: #7784eb;
	font-size: 28rpx;
}

.edit-btn .gui-icons {
	margin-right: 8rpx;
	font-size: 32rpx;
}

.profile-body {
	display: flex;
	justify-content: space-between;
	position: relative;
    height:  255px;
}

.left-stats {
	width: 30%;
	display: flex;
	flex-direction: column;
	border-radius: 16rpx;
	padding: 20rpx;
}

.visit-item {
	margin-bottom: 15rpx;
}

.visit-tag {
	display: inline-block;
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
	color: #fff;
	font-size: 24rpx;
	text-align: center;
}

.health-data-container {
	background-color: rgb(227, 253, 251);
	border-radius: 16rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.monitor-count {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
	padding-bottom: 10rpx;
}

.health-metrics {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}

.health-item {
	flex: 1;
	margin-right: 20rpx;
	
	&:last-child {
		margin-right: 0;
	}
}

.item-label {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.item-value-container {
	display: flex;
	align-items: center;
	margin-bottom: 6rpx;
}

.item-value {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
}

.arrow-icon {
	margin-left: 8rpx;
	font-size: 36rpx;
}

.up-arrow {
	color: #ff6b35;
}

.down-arrow {
	color: #3dc2bd;
}

.item-unit {
	font-size: 22rpx;
	color: #999;
	display: block;
	margin-bottom: 4rpx;
}

.item-date {
	font-size: 22rpx;
	color: #999;
}

.body-outline {
	width: 65%;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	margin-top: -65rpx;
}

.outline-image {
	width: 100%;
	height: 550rpx;
	opacity: 0.9;
}

.right-tags {
	width: 30%;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.tag {
	background-color: #ff6b9d;
	color: #fff;
	padding: 10rpx 20rpx;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
	font-size: 24rpx;
	text-align: center;
	max-width: 90%;
}

.middle-tag {
	background-color: #ff9500;
}

.top-tag {
	margin-top: 40rpx;
}

.middle-top-tag {
	margin-top: 100rpx;
}

.middle-bottom-tag {
	margin-top: 40rpx;
}

.profile-footer {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
}

.info-row {
	display: flex;
	margin-bottom: 15rpx;
}

.info-label {
	width: 180rpx;
	font-size: 28rpx;
	color: #666;
}

.info-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	display: flex;
	align-items: center;
}

.eye-icon {
	margin-left: 10rpx;
	color: #999;
	font-size: 32rpx;
}
</style> 