{"_from": "crypto-js@4.2.0", "_id": "crypto-js@4.2.0", "_inBundle": false, "_integrity": "sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==", "_location": "/crypto-js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "crypto-js@4.2.0", "name": "crypto-js", "escapedName": "crypto-js", "rawSpec": "4.2.0", "saveSpec": null, "fetchSpec": "4.2.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz", "_shasum": "4d931639ecdfd12ff80e8186dba6af2c2e856631", "_spec": "crypto-js@4.2.0", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>", "url": "http://github.com/evanvosberg"}, "browser": {"crypto": false}, "bugs": {"url": "https://github.com/brix/crypto-js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "JavaScript library of crypto standards.", "homepage": "http://github.com/brix/crypto-js", "keywords": ["security", "crypto", "Hash", "MD5", "SHA1", "SHA-1", "SHA256", "SHA-256", "RC4", "Rabbit", "AES", "DES", "PBKDF2", "HMAC", "OFB", "CFB", "CTR", "CBC", "Base64", "Base64url"], "license": "MIT", "main": "index.js", "name": "crypto-js", "repository": {"type": "git", "url": "git+ssh://**************/brix/crypto-js.git"}, "version": "4.2.0"}