
let domainType = 3//1是广中医 2是贵阳 3是慢病 4是南宁中医院 5是 百色中医院
//--慢病医院环境打包基础路径：mbh5；测试环境则为：h5
//import cofing from '@/formal.js'// 正式
import cofing from '@/domain.js' //测试
import tools from '@/tools/utils/jsdecrypt.js'
cofing(domainType)
let {domain,socketUrl,ddId,bingingWxUser,appid,agentid} = cofing(domainType)
//domain = 'http://yb.frp.starup.net.cn/prod-api';  //员工本机
//domain = 'http://192.168.1.74:8880/prod-api';  //员工本机
//domain = 'http://192.168.1.71:8081/dev-api';  //员工本机
//domain = 'http://192.168.1.71:8880';  //员工本机
// socketUrl = 'ws://192.168.1.74:9898/ws';
export default {
	domainType,//1是广中医 2是贵阳
	domain,
	socketUrl,
	ddId,
	bingingWxUser,
	appid,
	agentid,
	uploadFuJianFile: domain + '/oss/uniapp/upload', // 上传单个文件
    updateDiary:domain+'/zwb/health/diary/edit',// 饮食日记反馈
	getListToGroup:domain+'/zwb/health/diary/listToGroup',// 饮食日记详情
	instructions:domain+'/doctor/instructions/',// 操作手册详情
	taskgetArticle1:domain+'/doctor/task/info/getArticle', //文章详情
	taskgetArticle2:domain+'/zwb/channel/openArticleDetail', //中台文章详情
	getLoginCheckCode:domain+'/getLoginCheckCode',// 获取登录登录授权开关和授权码
	doLoginCheckCode:domain+'/doLoginCheckCode',// 验证登录授权码
	selectPerformsList:domain+'/doctor/index/selectPerformsList',// 首页健康数据提示
	captchaImage: domain + '/captchaImage',  //验证码接口
	login: domain + '/doctor/login',  //登录接口
	logout: domain + '/doctor/logout',  //退出登录接口
	getToken: domain + '/doctor/getTokenInfo',  //刷新token
	updatePwd: domain + '/doctor/updatePwd',//修改密码接口
	patientList: domain + '/doctor/patient/list',  //患者信息接口
	patientListV2: domain + '/doctor/patient/listV2',  //患者信息接口V2
	patientDetail: domain + '/doctor/patient/',  //患者详情接口
	patientListHis: domain + '/doctor/patient/his/list',  //患者列表（直查医院his接口）-患者信息接口
	createWxQrcode: domain + '/zwb/record/createWxQrcode',  //患者信息太阳码
	currentDoctor: domain + '/doctor/qrcode/current',  //获取当前医生的太阳码信息
	getInfo: domain + '/doctor/getInfo',//医生信息接口
	commentImage: domain + '/oss/upload', // 上传单个文件
	dingBindUser: domain + '/doctor/ding/bindUser',//钉钉用户绑定
	QuantityData: domain +'/doctor/index/selectStatisticalQuantity',//普通医生统计总数接口
	statistics:domain +'/doctor/index/statistics',//管理员医生统计总数接口
	healthManage: domain +'/doctor/index/healthManagement',//健康管理总人数接口
	getTaskInfo: domain +'/doctor/index/taskInfoTaskTypeIs3',//康养总数
	getLingualWarningList: domain+'/doctor/index/getLingualWarningList',//舌面像接口
	readLingual: domain+'/doctor/index/readLingual/',//阅读舌面像上传提醒
	DetectionData: domain+'/doctor/index/abnormalDetection',//监测评估异常接口
	WarningList: domain+'/doctor/index/selectMonitorList',//监测预警接口接口
	QuestionWarning: domain+'/doctor/index/selectQuestionWarningList',//评估问卷预警接口
	Appointment:domain+'/doctor/index/getAppointmentCount',//今日人流量接口
	getCaseCount:domain+'/doctor/index/getCaseCount',//总人流量接口
	doctorRanking:domain+'/doctor/index/doctorRanking',//医生排行榜接口
	Consultation:domain+'/doctor/index/getConsultation',//人群数量趋势图接口
	detailList:domain+'/zwb/templateMiddleTable/detail-list',// 体质辨识
	// patientList: domain + '/doctor/patient/list',  //患者信息接口
	getMedicalList: domain + '/doctor/patient/getMedicalList',  //患者信息接口
	getMedicationList: domain + '/doctor/patient/getMedicationList',  //患者信息接口
	getMonitorList: domain + '/doctor/patient/getMonitorList',  //患者信息接口
	getInspectList: domain + '/doctor/patient/getInspectList',  //患者信息接口
	getExaminationList: domain + '/doctor/patient/getExaminationList',  //患者信息接口
	patientProfile: domain + '/doctor/patient/profile/',  //患者画像接口
	getResultInfo:domain +'/zwb/InspectResultInfo',//查看检查报表详情接口
	getExamination:domain +'/zwb/examinationInfo',//查看检验报告接口
	addTaskPerform:domain +'/doctor/perform/addTaskPerform',//上传舌面像
	getLingualList:domain +'/doctor/task/getLingualList',//查看舌面像报告接口
	getHeightList:domain +'/doctor/patient/getHeightList',//查看乐高数据接口
	fillContent:domain +'/doctor/index/fill/content/',//查看体质报告
	// getphysical:domain+'/system/dict/data/type/physical',//患者体质接口
	getphy:domain+'/doctor/patient/type',//筛选接口
	getCrowdType:domain+'/doctor/patient/crowdType/',//人群类型筛选接口
	// getpeople: domain +'/system/dict/data/type/people_type',//人群类型
	// getrqsx:domain+'/system/dict/data/type/rqsx',//人群属性
	// getlevel:domain+'/system/dict/data/type/fllow_manage_level',
	getTaskList:domain +'/doctor/patient/getTaskList',//查看患者的所有干预记录
	getEvaluationList:domain +'/doctor/patient/getEvaluationList',//查看患者的所有疗效屏柜
	selectDoctorStatistical:domain +'/doctor/index/selectDoctorStatistical',//首页统计数量
	// getTeamList:domain+'/zwb/smartScreen/getTeamList',// 患者团队医生-2024-3-22已更换接口
	getTeamList:domain+'/doctor/patient/getTeamList',// 患者团队医生
	//人群
	queryCrowdLabelList:domain+'/zwb/crowdLabel/queryCrowdLabelList',
	queryPatientList:domain+'/zwb/patient/queryPatientList',
	dpMedical:domain+'/zwb/record/dp-medical',
	getInfoMedical:domain+'/zwb/record/getInfoMedical',

	/* iot设备接口 */
	getByPatientIdAndIsMenstruation:domain +'/zwb/iot/getByPatientIdAndIsMenstruation', //体温是经期
	getMenstrualCycle:domain +'/zwb/iot/getMenstrualCycle',// 判断体温是否是经期
	queryList: domain +'/zwb/crowdLabel/queryList',
	getHealthData: domain + '/zwb/iot/health/data', // 健康数据 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] GET
	getDeviceDetailList: domain + '/zwb/iot/getDeviceDetailList/', // 患者设备列表 GET
	getDeviceDetails: domain + '/zwb/iot/getDeviceDetails', // 查询设备详细信息 GET
	bindingDevice: domain + '/zwb/iot/binding', // 绑定关联设备 POST
	relieveDevice: domain + '/zwb/iot/relieve', // 解除绑定设备 POST
	addBloodGlucoseRecord: domain + '/zwb/iot/add/bloodGlucoseRecord', // 添加血糖记录 POST
	getBloodGlucoseRecord: domain + '/zwb/iot/get/bloodGlucoseRecord', // 获取血糖记录 GET
	indicatorList: domain + '/zwb/iot/indicator/list', // 查询预警指标配置列表 GET

	/* 医护工作台接口 */
	getDataOverview: domain + '/doctor/home/<USER>', // 获取数据看板统计
	getTodoItemCount: domain + '/doctor/home/<USER>', // 获取待办事项统计
	getMonitorPerform: domain + '/doctor/home/<USER>', // 获取健康数据提醒
	getCrowdTypeCount: domain + '/doctor/home/<USER>', // 获取患者人群类型统计

	/* im板块接口 */
	mySessionList: domain + '/wx/im/mySessionList', //会话列表
	getFriendSession: domain + '/web/im/getFriendSession/', //医生创建会话
	friendList: domain + '/wx/im/friendList', //好友列表
	friendAskList: domain + '/wx/im/friendAskList', //好友请求列表
	friendAdd: domain + '/wx/im/friendAdd', //好友添加
	friendAsktoDo: domain + '/wx/im/friendAsktoDo', //好友添加处理
	sendMsg: domain + '/wx/im/sendMsg', //发送消息
	revokeMessage: domain + '/web/im/revokeMessage', //发送消息
	msgCount: domain + '/wx/im/msgCount', //获取未读消息总数
	msgReaded: domain + '/wx/im/msgReaded', //标记已读
	sessionRecord: domain + '/wx/im/sessionRecord', //会话消息历史记录
	getImUser: domain + '/wx/im/getImUser', //获取用户的im用户信息
	setEndHh: domain + '/wx/im/setEndHh', //结束聊天会话接口
	sendMsgBatch: domain + '/web/im/sendAppMsgBatch', //发起会话
	getDicts: domain + '/doctor/patient/type/', // 字典接口
	toBinding: domain + '/doctor/user/toBinding', // 字典接口

	// 待纳入列表相关接口
	getUnrecordedPatient: domain + '/zwb/his/mb/getUnrecordedPatient', // 获取未建档患者列表
	addArchivalByRecordId: domain + '/zwb/his/mb/addArchivalByRecordId', // 根据记录ID建档
	setPatientCrowdType: domain + '/zwb/his/mb/setPatientCrowdType', // 设置患者人群分组


	/**A
	 * 请求封装
	 * @param {Object} res
	 * @param {Object} success
	 * @param {Object} fail
	 */


	RequestData: function(res, success,showLoading = true, fail) {
		var that = this;
		res.data = res.data ? res.data : {};
		let method = res.method ? res.method : "POST";
		let shopToken = uni.getStorageSync('mbtoken') ? uni.getStorageSync('mbtoken') : "";
		if(showLoading){
			uni.showLoading({
				title: "请求中..."
			})
		}

		var urldata={
			url: res.url,
			data: res.data,
			method: method
		};

		uni.request({
			url: res.url,
			data: res.data,
			method: method,
			header: {
				"Content-Type": "application/json; charset=UTF-8",
				"Authorization": shopToken ? 'Bearer ' + shopToken : ""
			},
			dataType: 'json',
			success: function(res) {
				if (res.header['otherstr']) {
					res.data = tools.decryptData(res.data, res.header.otherstr)
				}
				console.log('http URL', urldata.url)
				console.log('http data', res)
				uni.hideLoading();
				if (res.data.code == 0 || res.data.code == 200) {
					if(res.data != null){success(res.data);}

				} else if (res.data.code == 401) {
					that.getTokenInfo(urldata,success);
				}  else {
					that.msg(res.data.msg ? res.data.msg : res.msg,"none",2500);
				}
			},
			fail: function(e) {
				uni.hideLoading();
				if (fail) fail();
			},
			complete: function() {

			}
		})
	},
	// 不需要token的请求封装
	RequestDataNo: function(res, success, fail, loadtag= true) {
		var that = this;
		res.data = res.data ? res.data : {};
		let method = res.method ? res.method : "POST";
		if(loadtag){
			uni.showLoading({
				title: "请求中..."
			})
		}

		uni.request({
			url: res.url,
			data: res.data,
			method: method,
			header: {
				"Content-Type": "application/json; charset=UTF-8"
			},
			dataType: 'json',
			success: function(res) {
				if (res.header['otherstr']) {
					res.data = tools.decryptData(res.data, res.header.otherstr)
				}
				console.log('http data', res)
				uni.hideLoading();
				if (res.data.code === 0 || res.data.code == 200) {
					success(res.data);
				} else {
					that.msg(res.data.msg ? res.data.msg : res.msg);
					success(res.data);
				}
			},
			fail: function(e) {
				uni.hideLoading();
				if (fail) fail();
			},
			complete: function() {
				uni.hideLoading();
			}
		})
	},
	// 判断登录和注册情况：用户微信是否注册在系统数据库中，已注册则自动登录，未注册提示注册
	checkLogin: function(success){
		if(!uni.getStorageSync("mbtoken") || !uni.getStorageSync("mbsecret")){
			//如果有secret触发自动登录
			if(uni.getStorageSync("mbsecret")){
				this.getTokenInfo('',res=>{
					if(res.code == 200){
						success(1);
					}else {
						success(0);
					}

				});
			}else {
				success(0);
			}
		}else {
			//已有登录信息
			success(1);
		}
	},
	// 刷新获取token信息
	getTokenInfo: function(urldata,success,type){
		var that=this;
		var secret = uni.getStorageSync("mbsecret");
		if(secret){
			this.RequestDataNo({
				url:this.getToken,
				data:{secret:secret},
				method:"POST"
			},res=>{
				if(res.code==200){
					uni.setStorageSync("mbtoken",res.data);
					if(urldata){
						if (type == 2) {
							that.uploadFile(urldata.filePath,success)
						} else{
							that.RequestData(urldata,ures=>{
								success(ures);
							})
						}
					}else {
						success(res)
					}

				}else{
					//无效数据清除重新进入页面判断是否注册
					uni.removeStorageSync( "mbtoken")
					uni.removeStorageSync( "mbsecret")
					that.navLaunch("/pages/login/index")
					success(res)
				}
			})
		}else {
			//无效数据清除重新进入页面判断是否注册
			// uni.clearStorageSync()
			that.navLaunch("/pages/login/index")
		}
	},
	//模态窗
	// model: function(title, content, success) {
	// 	uni.showModal({
	// 		title: title ? title : '提示',
	// 		content: content ? content : '这是一个模态弹窗',
	// 		success: function(res) {
	// 			success(res);
	// 		}
	// 	});
	// },

	// 获取用户信息
	getUserInfo: function(){
		this.RequestData({
			url:this.info,
			data:{},
			method:"GET"
		},res=>{
			uni.setStorageSync("mbuserInfo",res.data)
		})
	},

	// 判断是否登录
	isLogin: function(){
		if(uni.getStorageSync("mbtoken")){
			return true;
		}
		return false;
	},

	//H5图片上传
	H5uploadFile: function(tempFile, success) {
		let that = this;
		var urldata ={
			url: that.commentImage,
			file: tempFile
		};
		that.loadings();
		uni.uploadFile({
			url: that.commentImage,
			name: 'file',
			file: tempFile,
			fileType: 'image',
			header: {
				"Authorization": 'Bearer ' + uni.getStorageSync('mbtoken')
			},
			success: (res) => {
				var data = JSON.parse(res.data);
				if (data.code == 200) {
					success(data);
					// uni.hideLoading();
				}else if (data.code == 401) {
					that.getTokenInfo(urldata,success,2);
				} else {
					that.msg(data.msg);
				}
			},
			fail: function(erro) {
				console.log('进来报错啦啦啦啦',erro)
				that.getTokenInfo(urldata,success,2);
			}
		});
	},
	//图片上传
	uploadFile: function(tempFilePath, success) {
		let that = this;
		var urldata ={
			url: that.commentImage,
			filePath: tempFilePath
		};
		that.loadings();
		uni.uploadFile({
			url: that.commentImage,
			filePath: tempFilePath,
			name: 'file',
			header: {
				"Authorization": 'Bearer ' + uni.getStorageSync('mbtoken')
			},
			success: (res) => {
				var data = JSON.parse(res.data);
				if (data.code == 200) {
					success(data);
					// uni.hideLoading();
				}else if (data.code == 401) {
					that.getTokenInfo(urldata,success,2);
				} else {
					that.msg(data.msg);
				}
			},
			fail: function(erro) {
				console.log('进来报错啦啦啦啦',erro)
				that.getTokenInfo(urldata,success,2);
			}
		});
	},

	// 文件上传2:商城专用
	uploadFileMall :function(success){
		let that = this;
		that.loadings();
		uni.chooseImage({
			success: (chooseImageRes) => {
				const tempFilePaths = chooseImageRes.tempFilePaths;
				that.loadings();
				uni.uploadFile({
					url: that.upload,
					filePath: tempFilePaths[0],
					name: 'file',
					header: {
						"Authorization": uni.getStorageSync('mbtoken')
					},
					success: (res) => {
						var data = JSON.parse(res.data);
						if (data.code == 200) {
							success(data);
							uni.hideLoading();
						} else {
							that.msg(data.msg);
						}
					},
					complete: function() {
						uni.hideLoading();
					}
				});
			},
			fail: function() {
				uni.hideLoading();
			}
		});
	},

	//是否为空判断
	isEmpty: function(obj) {
		if (obj == '') return true;
		if (obj == null) return true;
		if (obj == 'null') return true;
		if (obj === undefined) return true;
		return false;
	},

	// 打电话
	callPhone: function(phone){
		uni.makePhoneCall({
			phoneNumber:phone,
			success() {

			}
		})
	},

	//调整富文本图片大小
	adjustRichTextImageSize: function(text){
		text=text.replace(/\<img/gi, '<img style=width:100%;margin:auto;height:auto;');
		return text
	},

	//提示
	msg: function(message, icon, duration) {
		if (!icon) icon = "none";
		if (!duration) duration = 1000;
		uni.showToast({
			title: message,
			icon: icon,
			duration: duration
		})
	},

	//模态窗
	model: function(title, content, success,confirmText) {
		uni.showModal({
			title: title ? title : '提示',
			content: content ? content : '这是一个模态弹窗',
			cancelColor:"#F68512",
			confirmColor:"#b2b2b2",
			confirmText:'取消',
			cancelText:confirmText ? confirmText : '确认',
			success: function(res) {
				success(res);
			}
		});
	},

	//等待窗 - 增加遮罩蒙版
	loadings: function(title) {
		uni.showLoading({
			title: title ? title : "加载中...",
			mask: true
		});
	},


	//保留当前页面，跳转到应用内的某个页面
	navTo: function(url) {
		uni.navigateTo({
			url: url
		});
	},

	//关闭当前页面，跳转到应用内的某个页面
	navCloseTo: function(url) {
		uni.redirectTo({
			url: url
		});
	},

	//跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
	navTab: function(url) {
		uni.switchTab({
			url: url
		});
	},

	//关闭所有页面，打开到应用内的某个页面
	navLaunch: function(url) {
		uni.reLaunch({
			url: url
		});
	},

	//关闭当前页面，返回上一页面或多级页面
	navBack: function(index) {
		uni.navigateBack({
			delta: index
		});
	},

	// 日期格式化
	parseTime: function(time, pattern) {
		if (arguments.length === 0 || !time) {
			return null
		}
		const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
		let date
		if (typeof time === 'object') {
			date = time
		} else {
			if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
				time = parseInt(time)
			}
			if ((typeof time === 'number') && (time.toString().length === 10)) {
				time = time * 1000
			}
			date = new Date(time.substr(0, 19).replace(/T/g, ' ').replace(/-/g, '/'))
		}
		const formatObj = {
			y: date.getFullYear(),
			m: date.getMonth() + 1,
			d: date.getDate(),
			h: date.getHours(),
			i: date.getMinutes(),
			s: date.getSeconds(),
			a: date.getDay()
		}
		const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
			let value = formatObj[key]
			// Note: getDay() returns 0 on Sunday
			if (key === 'a') {
				return ['日', '一', '二', '三', '四', '五', '六'][value]
			}
			if (result.length > 0 && value < 10) {
				value = '0' + value
			}
			return value || 0
		})
		return time_str
	}
}
