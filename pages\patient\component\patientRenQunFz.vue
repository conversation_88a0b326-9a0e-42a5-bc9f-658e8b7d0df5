	<template>
  <view
    class="gui-relative gui-box-shadow gui-bg-white"
    style="border-radius: 30px 30px 0 0;"
  >
    <view class="popup-container">
      <scroll-view
        scroll-y="true"
        class="popup-scroll"
        :style="{ height: scrollHeight + 'px' }"
      >
        <view class="px-40 pt-20">
          <view
            class="w-100 gui-flex gui-align-items-center gui-justify-content-center gui-bold fs-36"
          >
            设置人群分组
          </view>

          <!-- 责任医生 -->
          <view class="gui-form-item gui-border-b">
            <text class="gui-form-label fs-32" style="width: 180rpx;text-align: end;">
              <text class="gui-color-red">*</text>
              责任医生：
						</text>
            <view class="gui-form-body">
              <view class="gui-flex gui-space-around">
                <view class="gui-flex gui-space-around">
                  <picker
                    mode="selector"
                    :range="doctorData"
                    @change="pickerDoctor"
                    range-key="nickName"
                  >
                    <view
                      class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center"
                    >
                      <text class="gui-text fs-32">{{
                        doctorIndex == -1
                          ? "请选择"
                          : doctorData[doctorIndex].nickName
                      }}</text>
                      <text
                        class="gui-form-icon gui-icons gui-text-center gui-color-gray"
                        >&#xe603;</text
                      >
                    </view>
                  </picker>
                </view>
              </view>
            </view>
          </view>

          <!-- 责任护士 -->
          <view class="gui-form-item gui-border-b">
            <text class="gui-form-label fs-32" style="width: 180rpx; text-align: end;"
              >责任护士：</text
            >
            <view class="gui-form-body">
              <view class="gui-flex gui-space-around">
                <view class="gui-flex gui-space-around">
                  <picker
                    mode="selector"
                    :range="nurseData"
                    @change="pickerNurse"
                    range-key="nickName"
                  >
                    <view
                      class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center"
                    >
                      <text class="gui-text fs-32">{{
                        nurseIndex == -1
                          ? "请选择"
                          : nurseData[nurseIndex].nickName
                      }}</text>
                      <text
                        class="gui-form-icon gui-icons gui-text-center gui-color-gray"
                        >&#xe603;</text
                      >
                    </view>
                  </picker>
                </view>
              </view>
            </view>
          </view>

          <view class="gui-form-item gui-border-b">
            <text class="gui-form-label fs-32" style="width: 180rpx;text-align: end;"
              >人群类型：</text
            >
            <view class="gui-form-body">
              <picker
                mode="selector"
                :range="gender"
                :value="genderIndex === -1 ? 0 : genderIndex"
                @change="pickerChange"
                range-key="dictLabel"
              >
                <view
                  class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center"
                >
                  <text class="gui-text fs-32" :class="genderIndex === -1 ? 'gui-color-gray' : ''">{{
                    genderIndex === -1 || !gender[genderIndex] ? '请选择人群分组' : gender[genderIndex].dictLabel
                  }}</text>
                  <text
                    class="gui-form-icon gui-icons gui-text-center gui-color-gray"
                    >&#xe603;</text
                  >
                </view>
              </picker>
            </view>
          </view>

          <view class="pt-20">
            <view class="font-bold">
              <text class="gui-text fs-32"
                >{{ genderIndex !== -1 && gender[genderIndex] ? gender[genderIndex].dictLabel + ' 选择标签' : '请先选择人群分组' }}</text
              >
            </view>
            <!-- 移除scroll-view，直接展示所有标签 -->
            <view class="tags-container" v-if="genderIndex !== -1 && gender[genderIndex]">
              <gui-stags
                checkedBg="gui-bg-zdy"
                :tags="leixs"
                :padding="30"
                :size="32"
                :lineHeight="2.5"
                @change="crowdTypeChange"
                type="checkbox"
              ></gui-stags>
            </view>
            <view v-else class="gui-text-center gui-color-gray py-50">
              <text class="gui-text fs-28">请先选择人群分组</text>
            </view>
          </view>

          <!-- 风险等级 -->
          <view class="gui-form-item gui-border-b">
            <text class="gui-form-label fs-32" style="width: 160rpx"
              >风险等级：</text
            >
            <view class="gui-form-body">
              <view class="gui-flex gui-space-around pt-20 pb-20">
                <view
                  class="risk-level-item"
                  :class="riskLevel === '低级' ? 'risk-active' : ''"
                  @click="selectRiskLevel('低级')"
                >
                  <text class="fs-28">低级</text>
                </view>
                <view
                  class="risk-level-item"
                  style="margin-left: 20rpx; margin-right: 20rpx"
                  :class="riskLevel === '中级' ? 'risk-active' : ''"
                  @click="selectRiskLevel('中级')"
                >
                  <text class="fs-28">中级</text>
                </view>
                <view
                  class="risk-level-item"
                  :class="riskLevel === '高级' ? 'risk-active' : ''"
                  @click="selectRiskLevel('高级')"
                >
                  <text class="fs-28">高级</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 底部安全距离 -->
          <view class="bottom-safe-area"></view>
        </view>
      </scroll-view>

      <!-- 底部按钮固定在底部 -->
      <view
        class="bottom-buttons"
        :style="openType == 2 ? 'bottom: 90rpx;' : 'bottom: 0rpx;'"
      >
        <view
          @tap.stop="rqclose"
          class="w-50 gui-flex gui-align-items-center gui-justify-content-center py-30 gui-bg-gray"
          >取消设置</view
        >
        <view
          @click="submitRenq"
          class="w-50 gui-flex gui-align-items-center gui-justify-content-center zhuti-bnt-bg gui-color-white py-30"
        >
          保存入组</view
        >
      </view>
    </view>
    <gui-iphone-bottom></gui-iphone-bottom>
  </view>
</template>
	<script>
import { patientCrowdLabel, getDutyUserList } from "@/api/patient.js";

export default {
  props: {
    genderArr: {
      type: [Object, Array],
      default() {
        return [];
      },
    },
    patientId: {
      type: String,
      default: "",
    },
    openType: {
      type: Number,
      default: 1,
    },
    // 添加医生和护士数据props
    doctorList: {
      type: Array,
      default: () => [],
    },
    nurseList: {
      type: Array,
      default: () => [],
    },
    initialDoctorId: {
      type: String,
      default: "",
    },
    initialNurseId: {
      type: String,
      default: "",
    },
    // 患者信息（用于回显）
    patientInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    genderArr: {
      handler(newValue) {
        if (newValue && newValue.length > 0) {
          this.gender = newValue;
          console.log('人群类型数据更新:', this.gender);

          // 如果有患者ID，重新加载患者信息进行回显
          if (this.patientId) {
            this.loadPatientInfo();
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      gender: [
        {
          dictLabel: "",
        },
      ],
      genderIndex: -1,
      riskLevel: "", // 危险等级 (文本：高级、中级、低级)
      leixs: [
        {
          id: 1,
          text: "标签1",
          checked: false,
        },
        {
          id: 1,
          text: "标签2",
          checked: false,
        },
        {
          id: 1,
          text: "标签3",
          checked: false,
        },
        {
          id: 1,
          text: "标签4",
          checked: false,
        },
        {
          id: 1,
          text: "标签5",
          checked: false,
        },
      ],
      crowdType: [],
      userInfo: {},
      doctorData: [], // 医生数据
      doctorIndex: -1, // 选中的医生索引
      nurseData: [], // 护士数据
      nurseIndex: -1, // 选中的护士索引
      scrollHeight: 0, // 滚动区域高度
      patientCrowdLabels: [], // 患者当前的人群标签（用于回显）
    };
  },
  mounted() {
    console.log('Component mounted');
    console.log('PatientId:', this.patientId);

    this.userInfo = uni.getStorageSync("mbuserInfo") || {};
    this.calculateScrollHeight();

    // 先加载医生和护士数据，然后再加载患者信息进行回显
    this.initializeComponent();
  },
  computed: {},
  methods: {
    // 初始化组件数据
    async initializeComponent() {
      try {
        // 1. 先加载医生和护士数据
        await this.loadDoctorAndNurseData();

        // 2. 如果有患者ID，获取患者详情信息用于回显
        if (this.patientId) {
          await this.loadPatientInfo();
        }

        // 3. 延迟检查回显状态
        setTimeout(() => {
          this.checkEchoStatus();
        }, 500);
      } catch (error) {
        console.error('初始化组件失败:', error);
      }
    },
    // 加载医生和护士数据
    loadDoctorAndNurseData() {
      return new Promise((resolve, reject) => {
        getDutyUserList({
          deptId: this.userInfo.deptId,
          appId: this.userInfo.deptAppIds
        }).then(res => {
          if (res.data) {
            this.doctorData = res.data.doctor || [];
            this.nurseData = res.data.nurse || [];

            // 设置默认选中项
            // 优先根据当前登录用户类型自动设置默认医生或护士
            if (res.data?.type) { //管理员角色时，接口没有返回字段type
              if (res.data.type == 2) {
                // 当前用户是护士
                const nurseIdx = this.nurseData.findIndex(item => item.nickName == this.userInfo.nickName);
                this.nurseIndex = nurseIdx !== -1 ? nurseIdx : 0; // 如果找不到当前用户，默认选择第一个
              } else {
                // 当前用户是医生
                const doctorIdx = this.doctorData.findIndex(item => item.nickName == this.userInfo.nickName);
                this.doctorIndex = doctorIdx !== -1 ? doctorIdx : 0; // 如果找不到当前用户，默认选择第一个
              }
            } else {
              // 如果没有用户类型信息，默认选择第一项
              if (this.doctorData.length > 0) {
                this.doctorIndex = 0;
              }
              if (this.nurseData.length > 0) {
                this.nurseIndex = 0;
              }
            }
            resolve(res);
          } else {
            reject(new Error('获取医护人员数据失败'));
          }
        }).catch(err => {
          console.error('获取医护人员信息失败', err);
          reject(err);
        });
      });
    },
    // 加载患者详情信息
    loadPatientInfo() {
      if (!this.patientId) return Promise.resolve();

      return new Promise((resolve, reject) => {
        this.$common.RequestData({
          url: this.$common.patientDetail + this.patientId,
          data: {},
          method: "get"
        }, res => {
          if (res.code == 200 && res.data) {
            this.setPatientInfoForDisplay(res.data);
            resolve(res);
          } else {
            reject(new Error('获取患者详情失败'));
          }
        }, true, (error) => {
          console.error('获取患者详情失败', error);
          reject(error);
        });
      });
    },
    // 检查回显状态
    checkEchoStatus() {
      console.log('=== Echo Status Check ===');
      console.log('patientInfo:', this.patientInfo);
      console.log('doctorData length:', this.doctorData ? this.doctorData.length : 'undefined');
      console.log('nurseData length:', this.nurseData ? this.nurseData.length : 'undefined');
      console.log('Current doctorIndex:', this.doctorIndex);
      console.log('Current nurseIndex:', this.nurseIndex);

      if (this.patientInfo && this.patientInfo.nurseId && this.nurseData && this.nurseData.length > 0) {
        console.log('Attempting manual nurse echo...');
        const nurseIndex = this.nurseData.findIndex(item => String(item.userId) === String(this.patientInfo.nurseId));
        console.log('Manual nurse search result:', nurseIndex);
        if (nurseIndex !== -1) {
          this.nurseIndex = nurseIndex;
          console.log('Manually set nurseIndex to:', this.nurseIndex);
        }
      }

      if (this.patientInfo && this.patientInfo.doctorId && this.doctorData && this.doctorData.length > 0) {
        console.log('Attempting manual doctor echo...');
        const doctorIndex = this.doctorData.findIndex(item => String(item.userId) === String(this.patientInfo.doctorId));
        console.log('Manual doctor search result:', doctorIndex);
        if (doctorIndex !== -1) {
          this.doctorIndex = doctorIndex;
          console.log('Manually set doctorIndex to:', this.doctorIndex);
        }
      }
      console.log('=== End Echo Status Check ===');
    },
    // 设置患者信息用于回显
    setPatientInfoForDisplay(patientInfo) {
      console.log('设置患者信息回显:', patientInfo);

      // 回显责任医生
      if (patientInfo.doctorId && this.doctorData && this.doctorData.length > 0) {
        const doctorIndex = this.doctorData.findIndex(item => item.userId == patientInfo.doctorId);
        if (doctorIndex !== -1) {
          this.doctorIndex = doctorIndex;
          console.log('医生回显成功:', this.doctorData[doctorIndex]);
        } else {
          console.log('未找到匹配的医生:', patientInfo.doctorId);
        }
      }

      // 回显责任护士
      if (patientInfo.nurseId && this.nurseData && this.nurseData.length > 0) {
        const nurseIndex = this.nurseData.findIndex(item => item.userId == patientInfo.nurseId);
        if (nurseIndex !== -1) {
          this.nurseIndex = nurseIndex;
          console.log('护士回显成功:', this.nurseData[nurseIndex]);
        } else {
          console.log('未找到匹配的护士:', patientInfo.nurseId);
        }
      }

      // 回显人群类型
      if (patientInfo.crowdType && this.gender && this.gender.length > 0) {
        const crowdTypeIndex = this.gender.findIndex(item => item.dictValue == patientInfo.crowdType);
        if (crowdTypeIndex !== -1) {
          this.genderIndex = crowdTypeIndex;
          console.log('人群类型回显成功:', this.gender[crowdTypeIndex]);
          // 加载对应的人群标签
          this.getCrowdLabel(this.gender[crowdTypeIndex].id);
        } else {
          console.log('未找到匹配的人群类型:', patientInfo.crowdType);
        }
      }

      // 回显风险等级
      if (patientInfo.riskType) {
        this.riskLevel = patientInfo.riskType;
        console.log('风险等级回显成功:', patientInfo.riskType);
      }

      // 回显人群标签
      if (patientInfo.crowdLabel) {
        this.patientCrowdLabels = patientInfo.crowdLabel.split(';').filter(item => item.trim());
        console.log('人群标签回显数据:', this.patientCrowdLabels);
      } else {
        this.patientCrowdLabels = [];
        console.log('患者无人群标签数据');
      }
    },
    // 人群类型选择
    pickerChange: function (e) {
      this.genderIndex = e.detail.value;
      // 清空之前选择的人群标签
      this.crowdType = [];
      if (this.genderIndex !== -1 && this.gender[this.genderIndex]) {
        this.getCrowdLabel(this.gender[this.genderIndex].id);
      }
    },
    // 选择危险等级
    selectRiskLevel: function (level) {
      this.riskLevel = level;
    },
    // 选择人群标签
    crowdTypeChange: function (e, data) {
      this.crowdType = [];
      e.map((item) => {
        this.crowdType.push(data[item].id);
      });
    },
    //获取人群标签
    getCrowdLabel(id) {
      if (id) {
        uni.showLoading({
          title: "请求中",
        });
        patientCrowdLabel(id).then((res) => {
          this.leixs = this.arr(res.data);
          uni.hideLoading();

          // 如果有患者的人群标签信息，设置回显状态
          if (this.patientCrowdLabels && this.patientCrowdLabels.length > 0) {
            this.setSelectedCrowdLabels();
          }
        }).catch(err => {
          uni.hideLoading();
          console.error('获取人群标签失败', err);
        });
      }
    },
    arr(val = []) {
      //数据处理
      let result = [];
      val.forEach((item) => {
        let temp = [];
        temp["id"] = item.id;
        temp["text"] = item.title;
        temp["checked"] = false;
        result.push(temp);
      });
      return result;
    },
    // 设置选中的人群标签（用于回显）
    setSelectedCrowdLabels() {
      if (!this.patientCrowdLabels || !this.leixs) return;

      // 清空之前的选择
      this.crowdType = [];

      this.leixs.forEach(item => {
        // 检查当前标签ID是否在患者的标签列表中
        // crowdLabel 可能是标签ID列表
        if (this.patientCrowdLabels.includes(String(item.id))) {
          item.checked = true;
          // 同时更新 crowdType 数组
          if (!this.crowdType.includes(item.id)) {
            this.crowdType.push(item.id);
          }
        }
        // 也可能是标签文本列表
        else if (this.patientCrowdLabels.includes(item.text)) {
          item.checked = true;
          // 同时更新 crowdType 数组
          if (!this.crowdType.includes(item.id)) {
            this.crowdType.push(item.id);
          }
        }
      });

      // 强制更新视图
      this.$forceUpdate();
    },
    //保存人群分组 - 调用待纳入列表专用API
    submitRenq() {
      if (this.genderIndex === -1) {
        return this.$common.msg("请选择人群分组！");
      }
      if (this.crowdType.length <= 0) {
        return this.$common.msg("请选择人群标签！");
      }
      if (!this.riskLevel) {
        return this.$common.msg("请选择风险等级！");
      }
      if (this.doctorIndex === -1) {
        return this.$common.msg("请选择责任医生！");
      }

      // 获取选中的医生数据
      const selectedDoctor = this.doctorData[this.doctorIndex];

      // 构建请求参数，按照后端接口要求
      let params = {
        patientId: this.patientId,
        doctorId: selectedDoctor.userId,
        doctorName: selectedDoctor.nickName,
        deptId: this.userInfo.deptId,
        crowdType: this.gender[this.genderIndex]?.dictValue || '',
        crowdLabel: this.crowdType.join(';'), // 人群标签ID数组转为分号分隔字符串
        riskType: this.riskLevel,
      };

      console.log('submitRenq - patientId:', this.patientId);
      console.log('submitRenq - 完整请求参数:', params);

      // 如果选择了护士，添加护士信息
      if (this.nurseIndex !== -1) {
        const selectedNurse = this.nurseData[this.nurseIndex];
        params.nurseId = selectedNurse.userId;
        params.nurseName = selectedNurse.nickName;
      }

      uni.showLoading({
        title: "保存中...",
      });

      // 调用待纳入列表专用的设置患者人群分组信息接口
      this.$common.RequestData(
        {
          url: this.$common.setPatientCrowdType,
          data: params,
          method: "post",
        },
        (res) => {
          uni.hideLoading();
          if (res.code == 200) {
            this.tags = [];
            this.crowdType = [];
            this.riskLevel = "";
            this.$common.msg("成功入组");
            this.$emit("renqSubmit", true);
            this.$forceUpdate();
          } else {
            this.$common.msg(res.msg || "保存失败，请重新保存。");
          }
        },
        true,
        () => {
          uni.hideLoading();
        }
      );
    },
    rqclose() {
      this.$emit("rqclose", true);
    },
    // 计算滚动区域高度
    calculateScrollHeight() {
      uni.getSystemInfo({
        success: (res) => {
          // 进一步增加弹窗高度到90vh，减少底部按钮高度计算
          const popupHeight = res.windowHeight * 0.95;
          const buttonHeight = uni.upx2px(100);
          this.scrollHeight = popupHeight - buttonHeight;
        },
      });
    },
    // 医生选择
    pickerDoctor: function (e) {
      this.doctorIndex = e.detail.value;
    },
    // 护士选择
    pickerNurse: function (e) {
      this.nurseIndex = e.detail.value;
    },
  },
};
</script>
	<style scoped>
.popup-container {
  height: 80vh; /* 进一步增加高度到90vh */
  position: relative;
  display: flex;
  flex-direction: column;
}

.popup-scroll {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增加iOS滑动优化 */
}

.tags-container {
  margin-top: 15rpx;
  padding-bottom: 20rpx;
  /* 移除高度限制，让标签自然展开 */
}

.bottom-safe-area {
  height: 200rpx; /* 进一步增加底部安全距离 */
}

.bottom-buttons {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  width: 100%;
  z-index: 10;
  background: white;
  border-top: 1rpx solid #f0f0f0;
  height: 100rpx; /* 减少按钮高度 */
  padding-bottom: env(safe-area-inset-bottom); /* 适配刘海屏 */
}

::v-deep .gui-space-around {
  justify-content: flex-start !important;
}

::v-deep .gui-space-between {
  justify-content: flex-start !important;
}

.risk-level-item {
  padding: 15rpx 30rpx;
  border: 2rpx solid #e1e0e0;
  border-radius: 10rpx;
  text-align: center;
  background-color: #f8f9fa;
}

.risk-active {
  background-color: #7784eb;
  border-color: #7784eb;
  color: white;
}
</style>








