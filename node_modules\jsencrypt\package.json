{"_from": "jsencrypt@3.3.2", "_id": "jsencrypt@3.3.2", "_inBundle": false, "_integrity": "sha512-arQR1R1ESGdAxY7ZheWr12wCaF2yF47v5qpB76TtV64H1pyGudk9Hvw8Y9tb/FiTIaaTRUyaSnm5T/Y53Ghm/A==", "_location": "/jsencrypt", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "jsencrypt@3.3.2", "name": "jsencrypt", "escapedName": "jsencrypt", "rawSpec": "3.3.2", "saveSpec": null, "fetchSpec": "3.3.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/jsencrypt/-/jsencrypt-3.3.2.tgz", "_shasum": "b0f1a2278810c7ba1cb8957af11195354622df7c", "_spec": "jsencrypt@3.3.2", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "lib/index.js", "bugs": {"url": "http://github.com/travist/jsencrypt/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/travist"}, {"name": "Antonio", "url": "https://github.com/zoloft"}, {"name": "<PERSON>", "url": "https://github.com/jmgaya"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dependencies": {}, "deprecated": false, "description": "A Javascript library to perform OpenSSL RSA Encryption, Decryption, and Key Generation.", "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@babel/register": "^7.18.9", "@types/expect": "^24.3.0", "@types/mocha": "^10.0.0", "@types/node": "^18.11.4", "chai": "^4.3.6", "dirty-chai": "^2.0.1", "fs-jetpack": "^5.1.0", "mocha": "^10.0.0", "process": "^0.11.10", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.2.4", "url": "^0.11.0", "webpack": "^5.35.1", "webpack-cli": "^4.6.0"}, "files": ["bin", "lib"], "homepage": "http://www.travistidwell.com/jsencrypt", "license": "MIT", "main": "bin/jsencrypt.js", "module": "lib/index.js", "name": "jsencrypt", "repository": {"type": "git", "url": "git://github.com/travist/jsencrypt.git"}, "scripts": {"build": "npm run build:dev && npm run build:test && npm run build:prod", "build:dev": "tsc && tsc --project tsconfig-def.json && webpack", "build:prod": "tsc && tsc --project tsconfig-def.json && webpack --config webpack.prod.js", "build:test": "tsc && tsc --project tsconfig-def.json && webpack --config webpack.test.js", "serve": "bundle exec jekyll server --config _config.build.yml", "test": "ts-mocha test/test.rsa.js"}, "types": "lib/index.d.ts", "version": "3.3.2"}