<template name="gui-im-input">
  <view>
    <view :style="{paddingBottom:paddingB + 'px'}" class="gui-im-footer gui-bg-gray gui-dark-bg-level-2">
      <view class="gui-flex gui-row gui-nowrap gui-space-between gui-align-items-center">
        <!--        <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" v-if="voiceBtnShow"-->
        <!--              @tap="showRec">&#xe617;</view>-->
        <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" @tap="chooseImg">&#xe63d;
        </view>
        <view class="gui-im-input gui-bg-white gui-dark-bg-level-3">
					<textarea type="text" v-model="inputMsg" :fixed="true" :maxlength="-1" rows="7" @confirm="sendTextMsg"
                    :cursor-spacing="35" :adjust-position="adjust" :show-confirm-bar="false" :auto-height="true"
                    @focus="inputFocus" @blur="inputBlur"></textarea>
        </view>
        <view class="gui-items gui-color-white gui-bg-blue b-radius-10"
              style="padding:0 20rpx; margin-right:10rpx;"
              hover-class="gui-tap" @tap="sendTextMsg">发送</view>
      </view>
      <view>
        <gui-iphone-bottom></gui-iphone-bottom>
      </view>
    </view>
    <!-- 语音输入 -->
    <view class="gui-im-record" v-if="recShow">
      <view class="gui-im-record-txt">
        {{ recTxt }}
        <text v-if="recing">已录音 : {{ recLength }} s</text>
      </view>
      <view class="gui-im-record-btn"
            @tap="rec" :class="[recing ? 'gui-im-recording' : '']"></view>
      <view class="gui-im-send-voice"
            v-if="tmpVoice != ''">
        <text @tap="sendVoiceMsg">发送语音</text>
      </view>
      <view class="gui-im-record-close graceIMFonts icon-close"
            @tap="closeRec" v-if="!recing"></view>
    </view>
  </view>
</template>
<script>
// 挂载 vuex
import { mapState, mapMutations } from 'vuex';
import url from "@/common.js"
var bgAudioMannager = uni.getBackgroundAudioManager();

export default {
  name  : "gui-im-input",
  props : {
    receiverUid : {
      type : String,
      default : ""
    },
    orderId : {
      type : String,
      default : ""
    },
    token : {
      type : String,
      default : ""
    },
    group : {
      type : String,
      default : ""
    },
    adjustPosition : {
      type : Boolean,
      default : true
    },
  },
  data() {
    return {
      paddingB        : '12',
      uploading       : false,
      recShow         : false,
      recTxt          : "请点击绿色按钮开始录音",
      inputMsg        : "",
      recorderManager : null,
      recing          : false,
      recLength       : 1,
      recTimer        : null,
      tmpVoice        : '',
      voiceLen        : 0,
      voiceBtnShow    : false,
      // 播放相关
      player          : null,
      playTxt         : "试听语音",
      adjust: true //h5无效
    }
  },
  computed: {
    ...mapState(['graceIMUID'])
  },
  watch: {
    adjustPosition: {
      deep: true,
      handler(newValue, oldValue) {
        //子组件中监控值变化做 相关业务功能
        this.adjust = newValue
        console.log('watch-adjustPosition:'+ newValue)
      }
    }
  },
  created : function(){
    // #ifndef H5
    this.voiceBtnShow    = true;
    this.recorderManager = uni.getRecorderManager();
    this.recorderManager.onStop((res) => {
      this.tmpVoice    = res.tempFilePath;
      this.recing      = false;
      this.recTxt       =  "... 已录音 "+this.recLength+
          "s, 点击绿色按钮重新录音 ...";
      clearInterval(this.recTimer);
    });
    this.recorderManager.onError(() => {
      uni.showToast({ title: '录音失败', icon: 'none' });
      this.recing = false;
      this.recTxt   = "请点击绿色按钮开始录音",
          clearInterval(this.recTimer);
    });
    // #endif
    // #ifdef MP
    try {
      var res = uni.getSystemInfoSync();
      res.model = res.model.replace(' ', '');
      res.model = res.model.toLowerCase();
      var res1  = res.model.indexOf('iphonex');
      if(res1 > 5){res1 = -1;}
      var res2   = res.model.indexOf('iphone1');
      if(res2 > 5){res2 = -1;}
      if(res1 != -1 || res2 != -1){
        this.paddingB = uni.upx2px(50)+'px';
      }
    } catch (e){return null;}
    // #endif
  },
  methods:{
    inputFocus(e) {
      console.info('=========adjust:' + this.adjust)
      if(!this.adjust){
        if (e.detail.height) {
          this.paddingB = String(e.detail.height); //输入框抬高到软键盘上面
        }
      }else {
        this.paddingB = '12'
      }

    },
    inputBlur() {
      this.paddingB = '12'
    },
    // 录音
    rec : function(){
      if (this.recing){
        this.recorderManager.stop();
        this.recing = false;
        this.recTxt   =  "... 已录音 "+this.recLength
            +"s, 点击绿色按钮重新录音 ...";
        clearInterval(this.recTimer);
      } else {
        this.recorderManager.start({duration:60000, format:'mp3' });
        this.recing     = true;
        this.recTxt     =  "... 正在录音 ...";
        this.recLength  = 1;
        this.recTimer   = setInterval(()=>{this.recLength++;}, 1000);
      }
    },
    // 发送录音
    sendVoiceMsg : function(){
      if (this.tmpVoice == '') {
        uni.showToast({ title: "请先录制一段语音", icon: "none" });
        return;
      }
      // 关闭界面
      this.recShow = false;
      this.$emit('sendVoice', this.tmpVoice, this.recLength);
      this.tmpVoice  = '';
      this.recLength = 0;
      this.recTxt    = "请点击绿色按钮开始录音";
    },
    // 展示录音界面
    showRec : function(){this.recShow  = true;},
    // 关闭录音界面
    closeRec: function (){this.recShow = false;},

    // 发送文本消息
    sendTextMsg: function () {
      if (this.inputMsg < 1) {return false;}
      // let inputMsg = this.replaceEmoji(this.inputMsg);
      var msg = {
        type: 'msg',
        contentType: 'txt',
        receiverType: '2', //接收方类型 1医生，2患者 -用于离线消息推送判断
        receiverUid: this.receiverUid, //接收者用户uid
        senderUid: this.graceIMUID, //发送方用户uid
        groupIndex: this.group, //好友组
        content: this.inputMsg, //发送的信息
        orderId: this.orderId,  // 咨询订单id
        patientId: uni.getStorageSync('mbcardObj').patientId,  // 患者id
        token: this.token //后台校验token，后面要封装在请求头
      }
      //改用http接口发送消息
      url.RequestDataNo({
        url:url.sendMsg,
        data:msg
      },res=>{
        this.$emit('sendText', this.inputMsg);
        this.inputMsg = '';
      })
    },
    // 选择图片
    chooseImg : function(){
      uni.chooseImage({
        count      : 1,
        sizeType   : ['compressed'],
        sourceType : ['album', 'camera'],
        success    : (res)=>{
          const tempFilePaths = res.tempFilePaths;
          this.sendImg(tempFilePaths[0]);
        }
      });
    },
    // 发送图片信息
    sendImg(img){
      this.$common.uploadFile(img,resx=>{
        let msg = {
          type: 'msg',
          contentType: 'img',
          receiverUid: this.receiverUid, //接收者用户uid
          senderUid: this.graceIMUID, //发送方用户uid
          groupIndex: this.group, //好友组
          content: resx.data.url, //发送的信息
          orderId: this.orderId,  // 咨询订单id
          token: this.token //后台校验token，后面要封装在请求头
        }
        url.RequestDataNo({
          url:url.sendMsg,
          data:msg
        },res=>{
          this.$emit('chooseImage', img);
        })
      })
    },
  }
}
</script>

<style scoped>
.gui-im-footer {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
}

.gui-im-footer .gui-items {
  width: auto;
  line-height: 70rpx;
  flex-shrink: 0;
  font-size: 28rpx;
}

.gui-im-menus {
  width: 80rpx;
  height: 80rpx;
  flex-shrink: 0;
  line-height: 80rpx;
  text-align: center;
}

.gui-im-input {
  width: 600rpx;
  margin: 10rpx;
  padding: 12rpx 16rpx;
  border-radius: 6rpx;
}

.gui-im-input textarea {
  width: 100%;
  height: 40rpx;
  line-height: 40rpx;
  max-height: 350rpx;
  font-size: 34rpx;
  margin-top: 10rpx;
  overflow: auto;
}

.gui-im-record {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
  padding: 30px 0;
  padding-bottom: 100rpx;
  z-index: 11;
}

.gui-im-record-close {
  width: 100rpx;
  height: 100rpx;
  position: absolute;
  top: 0px;
  right: 0px;
  z-index: 100;
  text-align: center;
  line-height: 100rpx;
  font-size: 38rpx !important;
}

.gui-im-record-txt {
  text-align: center;
  font-size: 26rpx;
  line-height: 30px;
  padding-bottom: 10px;
}

.gui-im-record-btn {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  border: 5px solid #F1F2F3;
  border-radius: 100%;
  background: #00B26A;
}

.gui-im-recording {
  background: #FF0000;
  animation: fade linear 2s infinite;
}

@keyframes fade {
  from {
    opacity: 0.1;
  }

  50% {
    opacity: 1;
  }

  to {
    opacity: 0.1;
  }
}

.gui-im-record-txt text {
  color: #00B26A;
  padding: 0 12px;
}

.gui-im-send-voice {
  margin-top: 12px;
  font-size: 28rpx;
  color: #00BA62;
  text-align: center;
}

.gui-im-send-voice text {
  margin: 0 15px;
  color: #00BA62;
}

.gui-icons {
  font-size: 50rpx;
}
</style>
