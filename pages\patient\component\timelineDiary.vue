<template>
	<scroll-view  :scroll-y="true" style="height:100%">
		<view class="mx-20">
			<view class="propose">
				<!-- 结果标题 -->
				<view class="">
					<view class="l-timeline-l" v-for="(item,index) in questionResult">
						<view class="time-line">
							<view class="l-icon"></view>
							<view class="time-line-i"></view>
						</view>
						<view style="flex: 1;padding-left: 20rpx;">
						<view class="visitDate-row  l-time fs-26">
							<view>
								{{item.recordTime}}
							</view>
							<!-- 标题 -->
							<!-- <view
								class="gui-accordion-title gui-flex gui-rows gui-nowrap gui-align-items-center"
								@tap="changeTo(index)">
								<text class=" gui-color-blue fs-26">详情</text>
								<text
									class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
									v-if="currentIndex != index">&#xe603;</text>
								<text
									class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
									v-else>&#xe654;</text>
							</view> -->
						</view>
						<view class="l-content" >
							<view class="gui-accordion">
								<view class="fs-24 item-row" v-if="item.list.length>0">
									<view v-for="(items,i) in item.list" class="gui-flex list-item">
										<image mode="aspectFill" @click="clickImg(items.imgUrlList[0],items.imgUrlList)" style="width: 100%;height: 150rpx;margin-bottom: 15rpx;"  :src="items.imgUrlList[0]"></image>
										<view class="item-row-date">
											<view>上传时间：{{ parseTime(items.recordTime, '{h}:{i}') }}</view>
											<view>{{items.imgCount}}张</view>
										</view>
										<!-- <view class="num">
											{{items.imgCount}}张
										</view> -->
										<view style="display: flex;flex-direction: column;justify-content: space-between;flex:1;margin-top: 10rpx;">
											<view :class="'title'+items.id" class="title" ref="title">
												<view class="text" :class="{'hide':items.lineHeight>2&&items.iSinfo}" style="line-height: 30rpx;">{{items.content}}</view>
												<view class="tips" @click="showinfo(items)" v-if="items.lineHeight>2">
													{{items.iSinfo?'查看全文':'收起'}}
												</view>
											</view>
											<view class="feedback">
												<textarea placeholder="请输入反馈内容" @focus="expandTextarea(items)" @blur="shrinkTextarea(items)" class="feedback-textarea" v-model="items.feedbackContent"/>
												<button :loading="loading" :disabled="loading" type="primary" @mousedown="feedback(items)" class="feedback-btn" v-show="items.isExpanded">确定</button>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						</view>
					</view>
				</view>
				<gui-empty
					v-if="questionResult.length == 0">
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<!-- 请根据您的项目要求制作并更换为空图片 -->
						<image class="gui-empty-img"
							src="https://images.weserv.nl/?url=https://upload-images.jianshu.io/upload_images/15372054-1f849183cebb80b1.png"></image>
					</view>
					<text slot="text"
						class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#9DABFF;">暂无数据 ......</text>
				</gui-empty>
			</view>
		</view>
	</scroll-view>
</template>

<script>
	export default {
		props:{
			info:{
				type:Object,
				default:{}
			},
		},
		data(){
			return{
				// 每个文本的信息
				elData: [],
				iSinfo: false,
				currentIndex:0,
				questionResult:[],
				loading:false
			}
		},
		mounted() {
			this.tzcontent()
		},
		methods:{
			parseTime(time, pattern){
				console.log(time, pattern,'时间格式化')
				const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
				let date
				if (typeof time === 'object') {
					date = time
				} else {
					if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
						time = parseInt(time)
					} else if (typeof time === 'string') {
						time = time.replace(new RegExp(/-/gm), '/');
					}
					if ((typeof time === 'number') && (time.toString().length === 10)) {
						time = time * 1000
					}
					date = new Date(time)
				}
				const formatObj = {
					y: date.getFullYear(),
					m: date.getMonth() + 1,
					d: date.getDate(),
					h: date.getHours(),
					i: date.getMinutes(),
					s: date.getSeconds(),
					a: date.getDay()
				}
				const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
					let value = formatObj[key]
					// Note: getDay() returns 0 on Sunday
					if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
					if (result.length > 0 && value < 10) {
						value = '0' + value
					}
					return value || 0
				})
				return time_str
			},
			feedback(row){
				console.log(1234659876558)
				if(!row.feedbackContent){
				    this.$confirm("请输入反馈内容");
				    return
				}
				this.loading=true;
				row.isFeedback = 1;
				this.$common.RequestData({
					url: this.$common.updateDiary,
					data:row,
					method: 'post',
				}, res => {
					this.loading=false;
					this.$common.msg("反馈成功","success")
				})
			},
			expandTextarea(items){
				items.isExpanded = true
				console.log('我进来了呵呵呵')
			},
			shrinkTextarea(items){
				items.isExpanded = false
				console.log('我进来了呵呵dddddd呵')
			},
			showinfo(items){
				items.iSinfo = !items.iSinfo
			},
			clickImg(i,imgs){
				console.log(i,imgs)
				uni.previewImage({
					urls:imgs,
					current:i
				})
			},
			// changeTo: function(idx) {
			// 	if (this.currentIndex == idx) {
			// 		this.currentIndex = -1;
			// 	} else {
			// 		this.currentIndex = idx;
			// 	}
			// },
			// report(e){
			// 	// this.$emit('report',e)
			// 	e.patientId = this.info.patientId
			// 	this.$common.navTo('/pages/patient/tzreport?obj=' + JSON.stringify(e)+'&info='+JSON.stringify(this.info))
			// },
			tzcontent(patientId){
				let patientIds = patientId?patientId:this.info.patientId
				if (!patientIds) {return}
				this.$common.RequestData({
					url: this.$common.getListToGroup,
					data: {
						pageNum: 1,
						pageSize: 1000,
						patientId: patientIds,
					},
					method: 'get',
				}, res => {
					this.questionResult = res.rows || []
					this.questionResult.forEach(item=> {
					    item.list.forEach(son=> {
							let imgs = son.imgUrl.split(',')
							son.imgCount = imgs.length
							son.imgUrlList = imgs
							this.$nextTick(() => {
								let el = uni.createSelectorQuery().in(this).select('.title'+son.id)
								console.log(el)
								el.boundingClientRect(res => {
									let lineHeight = parseInt(res.height / uni.upx2px(30));
									this.$set(son,'lineHeight',lineHeight)
									this.$set(son,'iSinfo',true)
									this.$set(son,'isExpanded',false)
								}).exec()
							})
						})
					})
					console.log(this.questionResult,'99999999')
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.feedback-btn{
		font-size: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 40rpx;
		position: absolute;
		bottom: 0;
		right: 0;
		padding-left: 16rpx;
		padding-right: 16rpx;
	}
	.feedback{
		position: relative;
	}
	.feedback-textarea{
		width: 100%;
		margin-top: 10rpx;
		background-color: #fff;
		height: 100rpx;
		font-size: 26rpx;
	}
	.item-row-date{
		display: flex;
		font-size: 24rpx;
		background-color: #3e3e3e;
		color: #fff;
		flex-direction: row;
		justify-content: space-between;
	}
	// 展开 文字
	.text{
		white-space: normal;  // 规定段落中的文本不进行换行
		word-break: break-all;  // 允许单词中换行，在容器的最右边进行断开不会浪费控件
		word-wrap: break-word;  // 防止长单词溢出，单词内部短句
	}
	.hide {
	    display:-webkit-box;
	    overflow: hidden;
	    text-overflow: ellipsis; //修剪文字，超过2行显示省略号
	    display: -webkit-box;
	    -webkit-line-clamp: 2; //此处为上限行数
	    -webkit-box-orient: vertical;
	}
	.tips {
		color: #008AFF;
		text-align: right;
	}
	.num{
		width: 50rpx;
		position: absolute;
		height: 50rpx;
		top: 100rpx;
		right: 5rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 100%;
	}
	.date{
		padding: 0 15rpx;
		margin: 20rpx 0 10rpx 0;
		color: #3e3e3e;
	}
	.title{
		position: relative;
		// overflow: hidden;
		// text-overflow: ellipsis;
		width: 100%;
		// display: -webkit-box;
		// -webkit-box-orient: vertical;
		// -webkit-line-clamp: 2;
		// word-break: break-all;
		padding: 0 15rpx;
		color: #3e3e3e;
	}
	.item-row{
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}
	.gui-accordion{
		// background-color: #f5f6f8;
		padding:20rpx;
		border-radius: 10rpx;
	}
	.list-item-name{
		font-weight: bold;
	}
	.list-item{
		background-color: #f2f2f2;
		display: flex;
		position: relative;
		flex-direction: column;
		justify-content: space-between;
		margin-right: 15rpx;
		width: 47%;
		margin-bottom: 15rpx;
		border:2rpx solid #eee;
	}
	.time-line{
		display: flex;
		flex-direction: column;
		width: 30rpx;
		position: relative;
		justify-content: center;
		align-items: center;
		.time-line-i{
			width: 2rpx;
			background-color: #aaa;
			height: 100%;
		}
	}
	.gui-rows-title{
		flex:1
	}
	.visitDate-row{
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}
	.navscroll{
		white-space: nowrap;
		// height: 100rpx;
		::v-deep ::-webkit-scrollbar {
			width: 4px !important;
			height: 1px !important;
			overflow: auto !important;
			background: transparent !important;
			-webkit-appearance: auto !important;
			display: block;
		}
		.garce-items{
			display: inline-block; // 设置为行内块
		}
	}
	.prescription{
		display: flex;
		flex-direction: row;
		flex-flow:row wrap;
	}
	.prescription-item{
		width: 30%;
		line-height: 26px;
		text-align: center;
	}
	>>>.gui-scroll-x-items {
		align-items: center;
	}

	.demo-nav {
		padding: 15rpx 30rpx;
	}

	.demo-text {
		line-height: 200rpx;
		padding-bottom: 3000px;
	}

	.grace-box-banner .garce-items .line2 {
		font-size: 28rpx;
		color: #008AFF;
		display: inline-block; // 设置为行内块
		border: 1px solid #62dbff;
		padding: 0 15rpx;
		margin: 0 10rpx;
		border-radius: 20rpx;
	}

	.line2.active {
		color: #ffffff !important;
		/* font-weight:bold; */
		background-color: #008AFF;
	}

	.l-timeline-l {
		display: flex;
		flex-direction: row;
		// border-left: 1px solid #aaaaaa;
	}

	.l-timeline-b {
		/* border-bottom: 2px solid #008AFF; */
	}

	.l-time {
		// position: relative;
		// top: -15rpx;
	}

	.acolor {
		background-color: #7784eb;

	}

	.bcolor {
		background-color: #7784eb;
		height: 40rpx;
		line-height: 40rpx;
		width: 190rpx;
	}

	.tagicon {
		margin-right: 10rpx;
		height: 40rpx;
		width: 6rpx;
		border-radius: 5rpx;
		background: #008AFF;
		display: block;
	}

	.l-icon {
		background: #008AFF;
		width: 25rpx;
		height: 25rpx;
		border-radius: 25rpx;
		position: absolute;
		top: 0;
		// position: relative;
		// top: -50rpx;
		// left: -15rpx;
	}

	.l-content {
		padding-bottom: 20px;
		margin-top: 20rpx;
		// position: relative;
		// top: -25rpx;
	}

	.gui-accordion-icon {
		width: 50rpx;
		// height: 80rpx;
		// line-height: 80rpx;
		font-size: 32rpx;
	}

	.gui-flex-direction-row {
		flex-direction: row-reverse;
	}

	.gui-accordion-title-text {
		// width: 200rpx;
		flex: 1;
	}

	>>>.gui-block-text {
		font-size: 30rpx !important;
	}

	.resuimg {
		width: 24px;
		height: 24px;
		font-size: 24px;
		line-height: 24px;
		vertical-align: middle;
		color: rgba(0, 186, 173, 1);
	}

	.resutext {
		width: 70px;
		height: 21px;
		font-size: 14px;
		text-align: left;
		font-weight: bold;
		line-height: 24px;
		padding-left: 8rpx;
		color: rgba(80, 80, 80, 1);
	}

	.propose_net {
		width: 100%;
		height: auto;
		margin-top: 5rpx;
		padding: 0 10rpx;
		text-indent: 2em;
		line-height: 52rpx;
		overflow-x: scroll;
	}

	.end_text {
		width: 325px;
		height: 42px;
		font-size: 14px;
		text-align: center;
		margin: 20% auto 0;
		color: rgba(212, 48, 48, 1);
	}

	.propose {
		width: 92%;

	}


	/* 干预 */
	.ganyuFu {
		background-color: #f5f6f8;
		line-height: 50rpx;
		padding-top: 10rpx;
		width: 85%;
		max-width: 85%;
		border-radius: 10rpx;
		padding: 5rpx 10rpx 5rpx 5rpx;
	}

	.ios {
		font-size: 35rpx;
		margin: 10rpx 10rpx;
		color: #1aca0d;
	}

	/* 疗效评估 */
	.laioxiaoFu {
		/* display: flex; */
		background-color: #f5f6f8;
		line-height: 50rpx;
		padding-top: 10rpx;
		width: 85%;
		max-width: 85%;
		border-radius: 10rpx;
		padding: 5rpx 10rpx 5rpx 5rpx;
	}

	.laioxiaoZi {
		display: flex;
		align-items: center;
		line-height: 50rpx;
		padding: 10rpx 0rpx 10rpx 15rpx;
	}

	.Aios {
		font-size: 50rpx;
		margin: 10rpx 20rpx 10rpx 10rpx;
		color: #1aca0d;
		/* height: 100% !important; */
		display: flex;
		align-items: center;
		float: left;
	}

	.yuandian {
		float: left;
		display: block;
		line-height: 50rpx;
		top: 10px;
		width: 8px;
		height: 8px;
		border-radius: 20px;
		/* background: #cbd0db; */
		background: #6f6f6f;
		margin-top: 8px !important;
		margin: 8px;

	}

	/* 舌面详情按钮 */
	.sm_xq {
		float: right;
		margin-right: 30rpx;
	}

	/* 头像 */
	.head_img {
		border-radius: 10rpx;
		border: 1px solid #cdcdcd;
	}

	>>>.gui-list-title-text {
		font-size: 28rpx !important;
	}
</style>
