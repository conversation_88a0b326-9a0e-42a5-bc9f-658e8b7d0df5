<template>
	<gui-page :isLoading="pageLoading" :apiLoadingStatus="apiLoadingStatus" :loadmore="true" @loadmorefun="WarningList"
		ref="guipage">
		<view slot="gBody">
			<view class="gui-padding mt-30">
				<view class="gui-tbody ai-center gui-border-b fu " v-for="(item, index) in warninglist"
					:key="index">
					<view @click="gotongue(item)">
						<view class="gui-flex gui-align-items-center gui-justify-content-between">
							<view class="gui-flex gui-align-items-center gui-flex-1">
								<view class="yuan"></view>
								<view class=" gui-td-text gui-text-center  Atext  ml-10 ">{{item.name}} {{item.sex== 1 ?'男':'女'}} {{item.age}}岁</view>
								<view class=" gui-td-text gui-text-center   Atext ml-10"
									:class="item.measureResult && item.measureResult.includes('异常') ? 'gui-color-red' : ''">
									<!-- <span>（{{item.monitorName}})</span> -->
									{{item.measureResult}}
								</view>
							</view>
							<view class="gui-td-text gui-color-gray Atext">
								{{item.measureTime}}
							</view>
						</view>
					</view>

				</view>
			</view>
		</view>
		</view>
	</gui-page>

</template>

<script>
	export default {
		data() {
			return {
				page:1,
				warninglist: [],
				pageLoading: true,
				// 用于记录是否有 api 请求正在执行
				apiLoadingStatus: false
			}
		},
		onLoad() {
			// this.WarningList();
		},
		onShow() {
			this.WarningList();
		},
		methods: {
			//监测预警
			WarningList() {
				this.apiLoadingStatus = true;
				this.$common.RequestData({
					url: this.$common.WarningList,
					data: {
						seachType: 1,
						pageNum:this.page,
						pageSize:15
					},
					method: 'get',
				}, res => {
					// this.warninglist = res.rows;
					var demoArr = res.rows
					console.log(demoArr)
					if (this.page >= 2) {
						this.warninglist = this.warninglist.concat(demoArr);
						// 加载完成后停止加载动画
						this.$refs.guipage.stoploadmore();
						// 假定第3页加载了全部数据，通知组件不再加载更多
						// 实际开发由接口返回值来决定
						if (this.page >=  Math.ceil(Number(res.total / 15))) {
							this.$refs.guipage.nomore();
						}
					}
					// 第一页数据
					else {
						this.warninglist = demoArr;
						this.pageLoading = false;
					}
					this.page++;
					this.apiLoadingStatus = false;
				})
			},
			gotongue(item) {
				this.$common.RequestData({
					url: this.$common.readLingual + item.id,
					data: {},
					method: 'get',
				}, res => {
					if (res.code == 200) {
						// this.$common.navTo('/pages/patient/details?patientId=' + item.patientId + '&monitorId=' +
						// 	item.monitorId)
						this.$common.navTo('/pages/patient/details')
						uni.setStorageSync('mbdetailsPatientId',item.patientId)
						uni.setStorageSync('mbmonitorId',item.monitorId)
					} else {
						this.$common.msg("数据异常，请稍后刷新重试~")
					}
				}, )

			},
		}
	}
</script>

<style scoped>
	page {
		background-color: white !important;
	}

	/* 圆点 */
	.yuan {
		width: 8px;
		height: 8px;
		border-radius: 50%;
		line-height: 8px;
		background-color: #919191;
		display: inline-block;
	}

	/* flex布局辅助类 */
	.gui-justify-content-between {
		justify-content: space-between;
	}
	
	.gui-flex-1 {
		flex: 1;
	}

	.bt {
		display: inline-block;
	}

	.Atext {
		font-size: 29rpx !important;
	}

	.theader {
		color: #7784eb;
	}

	.gui-td-text {
		line-height: 60rpx !important;
	}

	.fu {
		overflow: auto;
		clear: both;
		padding: 25rpx 0;
	}

	.chulid {
		display: inline-block;
		line-height: 60rpx;
		margin-left: 10px;
	}
</style>
