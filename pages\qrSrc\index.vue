<template>
	<div class="">
		<!-- 视频播放区域 -->
		<view v-if="model.videoUrl && port=='Android'">
			<x-video ref="videoPlayer" :progress="0" videoId="myVideo" :src="model.videoUrl"/>
		</view>
		<view v-show="model.videoUrl && port=='iOS'">
			<x-video ref="videoPlayer" :progress="0" videoId="myVideo" :src="model.videoUrl"/>
		</view>
		<view style="margin: 20rpx;" class="font-bold text-black fs-36 mb-20" id="title_value">
			{{model.articleName}}
		</view>
		<view class="row-model" style="margin: 20rpx;" v-if="type == 1">
			<text class="text-grey-b2 fs-24">{{model.createTime}}</text>
			<view class="gui-flex ml-30">
				<text class="gui-icons">&#xe609;</text>
				<text class="ml-10">{{model.readNum || 0}}</text>
			</view>
			<view class="gui-flex ml-30">
				<text class="gui-icons">&#xe622;</text>
				<text class="ml-10">{{model.shareNum || 0}}</text>
			</view>
		</view>
		<mp-html style="margin: 20rpx;" :content="model.content" />
	</div>
</template>
<script>
	export default {
		data() {
			return {
				video:null,
				id: null,
				type: '',
				poster:'',
				duration:0,
				model: {
					articleName: '',
					content: '',
					createTime: "",
					readNum: '',
					articleId: '',
					videoUrl:'',
					from:''
				}
			}
		},
		onLoad(e) {
			console.log(e)
			this.from = e.from=='zt'?e.from:''
			this.id = e.id
			this.type = e.type
		},
		mounted(){
			let port = uni.getSystemInfoSync().platform
			switch (port) {
				case 'android':
				this.port = 'Android'
					console.log('Android');//android
					break;
				case 'ios':
				this.port = 'iOS'
					console.log('iOS'); //ios
					break;
			}
			uni.setNavigationBarTitle({
			    title:this.type == 1?'文章详情':'操作手册',
			})
			if(this.type == 1){
				this.taskgetArticleNoToken()
			}else{
				this.instructions()
			}

		},
		methods: {
			// 操作手册
			instructions(){
				this.$common.RequestDataNo({
					url: this.$common.instructions+this.id,
					method: 'get',
				}, res => {
					console.log(res,'操作手册')
					this.model = res.data
					this.model.content = res.data.contents
					this.model.articleName = res.data.title

				})
			},
			// 文章详情 无token
			taskgetArticleNoToken() {
				// 中台文章
				if(this.from){
					this.$common.RequestDataNo({
						url: this.$common.taskgetArticle2,
						data: {
							id: this.id,
						},
						method: 'get',
					}, res => {
						this.model = res.data
					})
					return
				}
				// 治未病文章
				this.$common.RequestDataNo({
					url: this.$common.taskgetArticle1,
					data: {
						id: this.id,
						from:this.from
					},
					method: 'get',
				}, res => {
					this.model = res.data
				})
			}
		}
	}
</script>
<script lang="renderjs" module="video">
	export default {
		methods: {

		}
	}
</script>

<style>
	page {
		background: #fff;
	}

	.gui-course-video {
		width: 100%;
	}

	.row-model {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
</style>
