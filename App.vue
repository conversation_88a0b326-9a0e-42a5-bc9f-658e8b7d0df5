<script>
	export default {
		onLaunch: function() {
			// #ifdef APP-PLUS
			plus.screen.lockOrientation('portrait-primary'); //锁定屏幕
			const dom = weex.requireModule('dom');
			dom.addRule('fontFace', {
				'fontFamily': "graceIconfont",
				'src': "url('/static/grace.ttf')"
			});
			// #endif
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/* 加载框架核心样式 */
	@import "./GraceUI5/css/graceUI.css";
	/* 加载主题样式 */
	@import "./GraceUI5/skin/black.css";
	@import "./GraceUI5/css/common.css";
	@import "./tools/styles/style.css";
	/* 引入样式 */
	@import "./tools/styles/common.css";

	/* 加载图标字体 - 条件编译模式 */
	/* #ifdef APP-PLUS-NVUE */
	.gui-icons {
		font-family: graceIconfont;
	}
	page{
		background-color: white;
	}
	/* #endif */
	 /* #ifdef H5 */
	uni-checkbox .uni-checkbox-input {
		border-radius: 50% !important;
		color: #ffffff !important;
	}

	uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
		border: none !important;
		background: #008AFF;
		border-color: #008AFF;
	}

	uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked::before {
		width: 20rpx;
		height: 20rpx;
		line-height: 20rpx;
		text-align: center;
		font-size: 18rpx;
		color: #fff;
		background: transparent;
		transform: translate(-70%, -50%) scale(1);
		-webkit-transform: translate(-70%, -50%) scale(1);
	}
	/* #endif */
</style>
