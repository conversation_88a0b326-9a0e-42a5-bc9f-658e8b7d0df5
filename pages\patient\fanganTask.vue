<template>
	<gui-page :fullPage="true" :isLoading="pageLoading" ref="guiPage">
		<view slot="gBody" class="gui-flex1">
			<!-- 搜索框 -->
			<view class="header-search gui-bg-gray gui-border-box">
				<gui-search @clear="searchClear" @confirm="search" :placeholder="'请输入文章名称查询'"></gui-search>
			</view>
			<!-- 主体区域 -->
			<view class="gui-flex gui-rows gui-space-between" v-if="mainCate.length > 0">
				<!-- 左侧分类列表 -->
				<scroll-view :scroll-y="true" :show-scrollbar="false"
				:scroll-with-animation="true" :scroll-into-view="leftTo"
				class="gui-cate-left" :style="{height:mainHeight+'px'}">
					<view class="gui-cate-left-item gui-border-box"
					v-for="(item, index) in mainCate" :key="index"
					:class="[currentCateIndex == item.id ? 'gui-cate-left-current' : '']"
					>
						<text class="gui-border-l"
						:class="['gui-cate-left-item-title','gui-block-text', currentCateIndex == item.id ? 'gui-cate-left-current' : '']"
						@tap="changCate(item.id)">{{item.name}}</text>
					</view>
				</scroll-view>
				<!-- 右侧列表 -->
				<scroll-view
				:scroll-into-view="productListTo" :show-scrollbar="false"
				:scroll-with-animation="true" :scroll-y="true"
				class="gui-cate-right"
				:style="{height:mainHeight+'px'}">

					<!-- 循环展示分类及分类对应的产品列表@click="productsClick(item.id)" -->
					<view class="gui-margin-top"  v-if="products.length > 0"
					v-for="(item, index) in products" :key="index" >
						<!-- 循环展示产品 -->
						<view class="gui-flex gui-rows gui-nowrap gui-cate-product-list gui-border-b" >
							<view class="gui-cate-pbody gui-flex gui-rows gui-space-between gui-align-items-center">
								<view class="gui-text gui-primary-color gui-block-text ellipsis-2">{{item.programContent}}</view>
								<view class="gui-flex pr-40">
									<view v-if="productArr.find((i) => i.id == item.id)" class="" @click="delProduct(item)">
										<text style="font-size: 46rpx;" class=" gui-icons gui-color-gray">&#xe636;</text>
									</view>
									<view v-else class="gui-bg-zdy gui-flex gui-justify-content-center gui-align-items-center p-10" style="border-radius: 50rpx;height: 40rpx;width: 40rpx;" @click="addProduct(item)">
										<text class=" gui-icons fs-24 gui-color-white">&#xe6c7;</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view v-if="products.length <= 0">
						<gui-empty>
							<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
								<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
							</view>
							<text slot="text"
							class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
						</gui-empty>
					</view>
				</scroll-view>
			</view>
			<!-- 兑换记录 -->
			<view class="record">
				<view class="gui-flex gui-rows w-100 h-100 gui-align-items-center">
					<view class="pl-90 w-40">
						<view style="position: relative;z-index: 9999999;" @click="popupOpen">
							<text v-if="productArr.length>0" class="gui-badge gui-bg-red gui-color-white" style="z-index: 9999999;position: absolute; left: 55rpx; top: -5rpx; z-index: 1;">{{productArr.length}}</text>
							<text style="z-index: 9999999;" class=" gui-icons fs-60 gui-color-orange">&#xe60a;</text>
						</view>
					</view>
					<view class="gui-flex gui-rows gui-justify-content-center gui-align-items-center w-60 h-100 gui-bg-zdy  fs-30" style="border-radius: 80rpx;">
						<view class="gui-color-white" @click="toshop()">确定发送</view>
					</view>
				</view>
			</view>
			<view v-if="mainCate.length <= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text"
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
			<!-- 底部弹出 -->
			<gui-popup ref="guipopup" position="bottom" style="z-index: 88;">
				<view class="gui-relative gui-box-shadow gui-bg-gray pt-60 px-100" style="width: 100%; min-height: 100rpx; border-top-left-radius: 40rpx !important;border-top-right-radius: 40rpx !important;">
					<view class="mt-20" v-for="item in productArr">
						<view class="mb-20 pb-20 gui-flex gui-space-between gui-align-items-center" style="border-bottom: 1px solid #e6e6e6;">
							<view class="fs-30 ellipsis-2">{{item.programContent}}</view>
							<view class="" @click="delProduct(item)">
								<text class=" gui-icons fs-36 gui-color-gray">&#xe636;</text>
							</view>
						</view>
					</view>
					<view style="height: 160rpx;"></view>
					<!-- iphone 底部操作按钮躲避 -->
					<gui-iphone-bottom></gui-iphone-bottom>
					<!-- 关闭按钮 -->
					<text class="gui-block-text demo-close gui-icons gui-color-orange gui-absolute-rt" style="top: 8rpx; right: 20rpx;"
					@click="popupClose" >&#xe78a;</text>
				</view>
			</gui-popup>
		</view>
	</gui-page>
</template>
<script>
	import { programType,programConten,setTask,programIntervene,interveneInspect } from '@/api/patient.js'
var graceJS = require('@/GraceUI5/js/grace.js');
var cateChangeData = require('@/GraceUI5/demoData/cateChange.js');
export default {
	data() {
		return {
			img : "https://images.unsplash.com/photo-1660505465468-c898ea7ff674?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHx0b3BpYy1mZWVkfDQ2fHhqUFI0aGxrQkdBfHxlbnwwfHx8fA%3D%3D&auto=format&fit=crop&w=200&q=90",
			// 全屏加载，当数据请求完毕时关闭
			pageLoading : true,
			// 核心区域高度
			mainHeight  : 300,
			// 左侧分类定位
			leftTo      : 'cate',
			// 标识切换时左侧点击触发的
			isLeftTap   : false,
			// 左侧分类数据
			mainCate    : [],// 当前分类
			currentCateIndex : 0,
			// 延迟执行防止卡顿
			scrollTimer : null,
			// 产品列表滚动定位
			productListTo: '',
			products:[],
			productArr:[],
			// taskStatus:3,//类型：1：问卷 3：宣教
			batchId:null,
			patientId:'',
		}
	},
	onLoad:function(options){

		this.patientId = options.patientId//患者详情-下达干预
		// 获取页面主体高度从而得出主体高度
		graceJS.getRefs('guiPage', this, 0, (ref)=>{
			ref.getDomSize('guiPageBody', (e)=>{
				// 02. 导航的高度
				// 可以 使用 graceJS.select() 函数动态获取， 也可以使用 uni.upx2px 转换已知高度
				this.mainHeight  = e.height-150;
				setTimeout(()=>{
					this.pageLoading = false;
				},1000)
			});
		});
	},
	beforeDestroy() {
	  // 销毁定时器
	  if(this.intervalItem) {
	    clearInterval(this.intervalItem)
	  }
	},
	onShow() {
		this.getListType()
	},
	methods: {
		search(e){//搜索
			// console.log('搜索内容==',e)
			this.getList(this.currentCateIndex,e)
		},
		searchClear(e){
			this.getList(this.currentCateIndex,e)
		},
		//底部弹出模式
		popupOpen(){
			if (this.productArr.length <= 0) {return this.$common.msg('请选择发送内容')}
			this.$refs.guipopup.open();
		},
		popupClose(){this.$refs.guipopup.close();},
		addProduct(value,key){//添加项目
			// 检查是否已经选择了相同ID的项目
			if (this.productArr.some(item => item.id === value.id)) {
				this.$common.msg('该内容已添加，不可重复选择');
				return;
			}
			
			// 直接添加新内容，允许相同taskType的多个项目
			this.productArr.push(value);
		},
		delProduct(val,key){//删减项目
			this.productArr.forEach((item,index,array)=>{
			//item为遍历的当前元素，index为当前索引，array为正在操作的数组
			  if(item.id == val.id){
				array.splice(index,1)
			  }
			});
			if (this.productArr.length <= 0) {
				this.popupClose()
			}
		},
		toshop(){//确定发送
			if (this.productArr.length <= 0) {
				return this.$common.msg("请先选择发送的内容")
			}
			this.pageLoading =true;
			interveneInspect(this.patientId,this.productArr[0].programmeTypeId).then(res =>{
				let that = this
				if (res.data) {
					that.pageLoading =false;
					that.$common.msg('当天的方案已存在，请重新选择！','',2000)
				}else{
					let obj = {}
					that.productArr.forEach(item=>{
						// 如果该taskType已存在，则添加到数组中，否则创建新数组
						if(obj[item.taskType]) {
							obj[item.taskType].push(item)
						} else {
							obj[item.taskType] = [item]
						}
					})
					programIntervene(obj).then(res =>{
						if (res.code == 200) {
							that.productArr = [];
							uni.setStorageSync('mbtasktype',3)
							setTimeout(() =>{
								that.$common.navBack(1)
							},500)

						} else{
							that.pageLoading =false;
							that.$common.msg('发送失败，请重新选择！')
						}
							that.pageLoading =false;

					})
				}
			})
		},
		getListType(){
			programType().then(res => {
				// console.log('宣教分类==',res.data)
				if (res.data.length >0) {
					this.mainCate = res.data;
					this.currentCateIndex = res.data[0].id;
					this.getList(res.data[0].id)
				}
			})
		},
		getList(e,name){
			programConten({programmeId:e?e:this.currentCateIndex}).then(res =>{
				console.log('宣教内容======',res.data)
				this.products = [];
				this.productArr = [];
				
				if (res.data && Array.isArray(res.data) && res.data.length > 0) {
					let dataItem = res.data[0]; // 取数组的第一个元素
					
					// 关键修复：解析content字段中的JSON字符串
					if (dataItem.content) {
						let dataObj = JSON.parse(dataItem.content); // 解析JSON字符串
						console.log('解析后的content对象:', dataObj);
						
						// 遍历对象的每个key（"1", "2", "3"等taskType）
						for (let taskType in dataObj) {
							if (dataObj.hasOwnProperty(taskType)) {
								let taskArray = dataObj[taskType];
								console.log(`taskType ${taskType}:`, taskArray);
								// 检查该taskType对应的数组是否不为空
								if (Array.isArray(taskArray) && taskArray.length > 0) {
									// 遍历数组中的所有元素，不只是第一个
									taskArray.forEach(item => {
										let itemObj = {
											...item,
											patientId: this.patientId,
											programmeTypeId: dataItem.programmeId || this.currentCateIndex
										}
										console.log('添加项目:', itemObj);
										this.products.push(itemObj);
									});
								}
							}
						}
					}
				}
			})
		},
		changCate : function (e) {
			var cateid = e
			this.currentCateIndex = cateid;
			this.getList(this.currentCateIndex);
		}
	}
}
</script>
<style scoped>
	.record{
		z-index: 999999;
		position: absolute;
		bottom: 80rpx;
		background-color: #eeeeee;
		border-radius: 80rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		width: 680rpx;
		height: 80rpx;
		right:40rpx
	}
.header-search{padding:20rpx 80rpx; width:750rpx;}
.gui-cate-left{width:200rpx; background-color:#F6F7F8;}
.gui-cate-right{width:520rpx; overflow:hidden;}
.gui-cate-left-item{height:100rpx; padding:35rpx 0; font-size:26rpx;}
.gui-cate-left-item-title{line-height:32rpx; font-size:28rpx; border-color:#F6F7F8; border-left-width:8rpx; text-align:center;}
.gui-cate-left-current{border-color:#7784eb !important; background-color:#FFFFFF; color:#7784eb; font-weight:bold;}
.gui-cate-right-title{line-height:80rpx;}
.gui-cate-product-list{padding-bottom:30rpx;padding-right:25rpx;}
.gui-cate-pimg{width:180rpx;}
.gui-cate-pbody{
	margin-left:30rpx; width:100rpx; flex:1; padding-top:-20rpx;
}
.gui-block-text{
	font-size: 34rpx;
}
.gui-cate-price{font-size:32rpx; line-height:60rpx;}
.demo-close{width:70rpx; height:70rpx; line-height:70rpx; opacity:0.88; text-align:center; font-size:50rpx;}
</style>
