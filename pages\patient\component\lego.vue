<template>
	<gui-page >
		<!-- 页面主体 -->
		<view slot="gBody">
			<view class="view_block">
				<view class="title">
					<text>身高(cm)</text>
					<!-- <image class="gui-icons" src="../../../static/log/bx.png"></image>
					<text class="font-small" @click="add(1)">添加记录</text>
					<text class="font-small" style="float: right;" @click="jilu(1)">记录></text> -->
				</view>
				<view class="charts-box">
					<qiun-data-charts type="line" :opts="opts" :chartData="chartData" />
				</view>
			</view>
			<view class="view_block">
				<view class="title">
					<text>体重(kg)</text>
				</view>
				<view class="charts-box">
					<qiun-data-charts type="line" :opts="opts" :chartData="tizData" />
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
	export default {
		data() {
			return {
				patientInfo: '',//患者信息
				chartData: {
					categories: [],
					series: [{
						name: "",
						data: []
					}]
				},
				tizData: {
					categories: [],
					series: [{
						name: "",
						data: []
					}]
				},
				//您可以通过修改 config-ucharts.js 文件中下标为 ['line'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 10, 0, 15],
					legend: {
						show: false
					},
					xAxis: {
						disableGrid: false,
					},
					yAxis: {
						gridType: "dash",
						dashLength: 2,
						// showTitle:true,
						disabled: false,
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						}
					}
				}
			}
		},
		props: {
			isRefresh: {
				type: Number,
				default: 0
			},
			info:{
			  type : String,
			  default: 0
			}
		},
		watch: {
			isRefresh: {
				deep: true,
				handler(newValue, oldValue) {
					if (newValue != oldValue) {
						//子组件中监控值变化做 相关业务功能
					}
				}
			}
		},
		created() {
			this.getServerData()
			this.$forceUpdate()
		},
		methods: {
			getServerData() {
				//模拟从服务器获取数据时的延时 getHeightList
				this.$common.RequestData({
					url: this.$common.getHeightList,
					method: 'GET',
					data: {
						patientId: this.info || ''
					}
				}, res => {
					const data = res.data
					const xData = data.xData
					const yData = data.yData
					/* 过滤数组 */
					//身高数据
					this.chartData = JSON.parse(JSON.stringify({
						categories: xData,
						series: [{
							name: "身高",
							data: yData
						}]
					}));
					//体重数据
					this.tizData = JSON.parse(JSON.stringify({
						categories: xData,
						series: [{
							name: "体重",
							data: data.tzyData
						}]
					}));
				})
			}
		}
	}
</script>
<style lang="less">
	.view_block {
		background-color: #fff;
		padding: 16rpx 20rpx 10rpx 20rpx;
		border-radius: 20rpx;
		margin-bottom: 40rpx;

		.title {
			margin: 10rpx 10rpx;

			text:nth-child(1) {
				// background-color: red;
				font-size: 20rpx;
				margin-left: 15rpx;
				padding: 0rpx 10rpx;
			}

			image {
				width: 30rpx;
				height: 30rpx;
				margin: 0rpx 10rpx;
			}

			.font-small {
				// background-color: pink;
				font-size: 28rpx;
				color: #3c3c3c;
				padding: 0rpx 10rpx;
			}
		}
	}
	.charts-box {
		width: 100%;
		height: 300px;
	}
</style>
