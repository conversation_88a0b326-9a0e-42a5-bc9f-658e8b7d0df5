export default {
	translateImg(imgSrc, scale, type , callback) {
		console.log('我进来了呵呵呵呵',imgSrc)
		var img = new Image();
		var that1 = this;
		img.src = imgSrc;
		img.onload = function() {
			var that = this;
			var h = that.height; // 默认按比例压缩
			var w = that.width;

			var maxWidth = 2000; // 设置最大宽度
			var targetWidth = w;
			var targetHeight = h;
			if(w>maxWidth){
				targetWidth = maxWidth
				targetHeight = Math.round(h*(maxWidth/w))
			}
			// var ratio = Math.min(maxWidth/w,maxHeight/h);
			var canvas = document.createElement('canvas');
			var ctx = canvas.getContext('2d');
			var width = document.createAttribute("width");
			width.nodeValue = targetWidth;
			var height = document.createAttribute("height");
			height.nodeValue = targetHeight;
			canvas.setAttributeNode(width);
			canvas.setAttributeNode(height);
			ctx.drawImage(that, 0, 0, targetWidth,targetHeight);
			var base64 = canvas.toDataURL('image/jpeg', scale);//压缩比例
			canvas = null;
			if(type != 'blobString'){//图片预览组件需要的是blob地址，但是上传之后数据库没有后缀
				var blob = that1.base64ToBlob(base64);
				var blobUrl = window.URL.createObjectURL(blob);//blob地址
				callback(blobUrl);
			}else{
				// var blob = that1.base64ToBlob(base64);
				callback(base64);
			}
		}
	},
	translateImg(imgSrc, scale, type , callback) {
		console.log('我进来了呵呵呵呵',imgSrc)
		var img = new Image();
		var that1 = this;
		img.src = imgSrc;
		img.onload = function() {
			var that = this;
			var h = that.height; // 默认按比例压缩
			var w = that.width;

			var maxWidth = 2000; // 设置最大宽度
			var targetWidth = w;
			var targetHeight = h;
			if(w>maxWidth){
				targetWidth = maxWidth
				targetHeight = Math.round(h*(maxWidth/w))
			}
			// var ratio = Math.min(maxWidth/w,maxHeight/h);
			var canvas = document.createElement('canvas');
			var ctx = canvas.getContext('2d');
			var width = document.createAttribute("width");
			width.nodeValue = targetWidth;
			var height = document.createAttribute("height");
			height.nodeValue = targetHeight;
			canvas.setAttributeNode(width);
			canvas.setAttributeNode(height);
			ctx.drawImage(that, 0, 0, targetWidth,targetHeight);
			var base64 = canvas.toDataURL('image/jpeg', scale);//压缩比例
			canvas = null;
			if(type != 'blobString'){//图片预览组件需要的是blob地址，但是上传之后数据库没有后缀
				var blob = that1.base64ToBlob(base64);
				var blobUrl = window.URL.createObjectURL(blob);//blob地址
				callback(blobUrl);
			}else{
				// var blob = that1.base64ToBlob(base64);
				callback(base64);
			}
		}
	},
	base64ToBlob(base64) {
		var arr = base64.split(','),
			mime = arr[0].match(/:(.*?);/)[1],
			bstr = atob(arr[1]),
			n = bstr.length,
			u8arr = new Uint8Array(n);
		while (n--) {
			u8arr[n] = bstr.charCodeAt(n);
		}
		return new Blob([u8arr], {
			type: mime
		});
	},
	// 时间格式化
	formatDate(d, format = 'yyyy-MM-dd') {
		if (!d) return '';
		let date = d;
		switch (typeof date) {
			case 'string':
				date = new Date(date.replace(/-/g, '/'));
				break;
			case 'number':
			default:
				date = new Date(date);
		}
		if (!(date instanceof Date)) return '';

		const dict = {
			yyyy: date.getFullYear(),
			M: date.getMonth() + 1,
			d: date.getDate(),
			H: date.getHours(),
			m: date.getMinutes(),
			s: date.getSeconds(),
			MM: (`${date.getMonth() + 101}`).substr(1),
			dd: (`${date.getDate() + 100}`).substr(1),
			HH: (`${date.getHours() + 100}`).substr(1),
			mm: (`${date.getMinutes() + 100}`).substr(1),
			ss: (`${date.getSeconds() + 100}`).substr(1),
		};
		try {
			return format.replace(/(yyyy|MM?|dd?|HH?|ss?|mm?)/g, f => dict[f]);
		} catch (e) {
			return '';
		}
	},
	//调整富文本图片大小
	adjustRichTextImageSize(text){
		text=text.replace(/\<img/gi, '<img style=width:100%;margin:auto;margin-left:-2em;height:auto;');
		return text
	},
	// 转义非法字符
	escapeCharacter(str = '') {
		return str
			.replace(/&ldquo;/g, '“').replace(/&rdquo;/g, '”')
			.replace(/&lsquo;/g, '‘').replace(/&rsquo;/g, '’')
			.replace(/&quot;/g, '"')
			.replace(/&#039;/g, "'")
			.replace(/&lt;/g, '<')
			.replace(/&gt;/g, '>')
			.replace(/&hellip;&hellip;/g, '……')
			.replace(/&mdash;&mdash;/g, '——')
			.replace(/&amp;/g, '&');
	},

	// 字符串转html
	unescapeHTML(value) {
		const val = value.toString();
		return val.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&')
			.replace(/&quot;/g, '"').replace(/&apos;/g, "'"); // eslint-disable-line
	},

	// 判断数据类型
	getObjType(obj) {
		var toString = Object.prototype.toString
		var map = {
			'[object Boolean]': 'boolean',
			'[object Number]': 'number',
			'[object String]': 'string',
			'[object Function]': 'function',
			'[object Array]': 'array',
			'[object Date]': 'date',
			'[object RegExp]': 'regExp',
			'[object Undefined]': 'undefined',
			'[object Null]': 'null',
			'[object Object]': 'object'
		}
		if (obj instanceof Element) {
			return 'element'
		}
		return map[toString.call(obj)]
	},
	/**
	 * 对象深拷贝
	 */
	deepClone(data) {
		var type = getObjType(data)
		var obj
		if (type === 'array') {
			obj = []
		} else if (type === 'object') {
			obj = {}
		} else {
			// 不再具有下一层次
			return data
		}
		if (type === 'array') {
			for (var i = 0, len = data.length; i < len; i++) {
				obj.push(deepClone(data[i]))
			}
		} else if (type === 'object') {
			for (var key in data) {
				obj[key] = deepClone(data[key])
			}
		}
		return obj
	},
	/**
	 * 根据字典的value查找对应的index
	 */
	findArray(dic, value) {
		for (let i = 0; i < dic.length; i++) {
			if (dic[i].value === value) {
				return i
			}
		}
		return -1
	},
	/* 验证pad还是pc */
	vaildatePc() {
		const userAgentInfo = navigator.userAgent
		const Agents = ['Android', 'iPhone',
			'SymbianOS', 'Windows Phone',
			'iPad', 'iPod'
		]
		let flag = true
		for (var v = 0; v < Agents.length; v++) {
			if (userAgentInfo.indexOf(Agents[v]) > 0) {
				flag = false
				break
			}
		}
		return flag
	},

	// 数组最大值
	arrayMax(arr) {
		return Math.max.apply(null, arr)
	},
	// 数组最小值
	arrayMin(arr) {
		return Math.min.apply(null, arr)
	},
	// 数组并集
	arrayUnion(arr1, arr2) {
		return [...new Set([...arr1, ...arr2])]
	},
	// 数组交集
	arrayIntersect(arr1, arr2) {
		// let arr3 = [...arr1].filter(value => arr2.includes(value))
		// return [...new Set([...arr3])]
		return [...new Set([...arr1].filter(value => arr2.includes(value)))]
	},
	// 数组差集
	arrayDiff(arr1, arr2) {
		return [...new Set([...arr1].filter(value => !arr2.includes(value)))]
	},
	// 数组去重
	arrayUnique(arr) {
		return [...new Set([...arr])]
	}
}
