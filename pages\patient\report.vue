<template>
	<view style="height:100vh;"  >
		<view  class="mx-40 my-20" v-if="!pdfUrl">
		<view class="gui-text-center gui-bold fs-30"> <text>{{list.applyItems}}</text></view>
		<view style="margin-top:15rpx" class="gui-flex gui-space-between">
			<view class="gui-text-small ">姓名：{{list.patientName}}</view>
			<view class="gui-text-small   ">性别：{{list.gender== 1 ?'男':'女'}}</view>
			<view class="gui-text-small ">年龄：{{list.age}}</view>
		</view>
		<view style="margin-top:15rpx" class="gui-flex gui-space-between">
			<view class="gui-text-small ">科室：{{list.applyDept}}</view>
			<view class="gui-text-small   ">医生：{{list.applyDoctor}}</view>
			<view class="gui-text-small "></view>
		</view>
		<view class="gui-table  gui-border-t" style="margin-top:50rpx; ">
			<view class=" gui-flex gui-columns  ">
				<view
					class="gui-td1 p-20 td gui-border-r gui-border-b gui-td-text fs-30 gui-border-l">临床诊断</view>
				<view class="gui-td2 gui-border-r gui-border-b gui-td-text gui-bold px-20">{{list.diagnosis}}</view>
			</view>
			<view class=" gui-flex gui-columns ">
				<view class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-border-l gui-flex">检查所见</view>
				<view class="shsj gui-td2 gui-border-r gui-border-b gui-td-text  px-20 fs-28 ">{{list.observation}}</view>
				<view class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-border-l gui-flex">检查结论</view>
				<view class="shsj gui-td2 gui-border-r gui-border-b gui-td-text  px-20 fs-28 ">{{list.conclusion}}</view>
				<view class="gui-td2 gui-td-text  px-20 fs-28 ">
					<image :src="item" style="width: 100%;" v-for="(item,index) in img"  @click="previewImage(index)"></image>
				</view>
			</view>
			<view style="margin-top:40rpx" class="gui-flex gui-space-between">
				<view class="gui-text-small ">申请医生：{{list.applyDoctor|| '-'}}</view>
				<view class="gui-text-small   "></view>
				<view class="gui-text-small ">检查日期：{{list.executeTime|| '-' }}</view>
			</view>
			<view style="margin-top:15rpx" class="gui-flex gui-space-between">
				<view class="gui-text-small ">报告人：{{list.reportor }}</view>
				<view class="gui-text-small   "></view>
				<view class="gui-text-small ">报告日期：{{list.reportTime }}</view>
			</view>


		</view>
		</view>
		<view v-if="pdfUrl" style="height: 100%;">
			<iframe :src="pdfUrl" width="100%" height="100%"/>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info: '',
				list: [],
				pdfUrl:'',
				img:[]
			}
		},
		onLoad: function(option) {
			console.log(JSON.parse(option.info));
			this.info = JSON.parse(option.info);
			this.report();

		},

		methods: {
			report() {
				this.$common.RequestData({
					url: this.$common.getResultInfo + '/' + this.info.id,
					data: {},
					method: 'get',
				}, res => {
					this.list = res.data;
					if (res.data.imagUrls) {
						if(res.data.imagType == 'base64'){
							const imglist = JSON.parse(res.data.imagUrls);
							imglist.forEach(item => {
							    this.img.push('data:image/jpg;base64,' + item.imagUrl);
							})
						}else{
							const img = res.data.imagUrls ? res.data.imagUrls.split(',') : []
							if (img.length>0) {
								if (img[0].includes('.pdf')) {
									this.pdfUrl = img[0]
							    } else {
							        this.img = img
							    }
							} 
						}
					}

				}, )
			},
			previewImage(index){
				uni.previewImage({
					current: index,     // 当前显示图片的索引值
					urls: this.img,    // 需要预览的图片列表，photoList要求必须是数组
					loop:true,          // 是否可循环预览
				})

			}
		}
	}
</script>

<style scoped>
	.demo {
		width: 210rpx;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		margin: 10rpx;
	}

	.demo-auto-width {
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		margin: 10rpx;
	}

	.gui-text-small {
		font-size: 20rpx;
	}

	.gui-td1 {
		/* width: 30%; */
		display: flexbox;
	}

	.gui-td2 {
		/* width: 70%; */
		display: flexbox;
	}

	.td {
		color: #7784eb;
	}

	.shsj {
		text-indent: 2em
	}
</style>
