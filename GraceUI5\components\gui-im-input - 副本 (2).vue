<template name="gui-im-input">
  <view>
    <view :style="{paddingBottom:paddingB + 'px'}" class="gui-im-footer gui-bg-gray gui-dark-bg-level-2">
      <view class="gui-flex gui-row gui-nowrap gui-space-between gui-align-items-center">
       <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" v-if="voiceBtnShow"
             @tap="showRec">&#xe617;</view>
        <!-- <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" @tap="chooseImg">&#xe63d;</view> -->
		<!-- <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" @tap="chooseTask">&#xe62f;</view> -->
        <view class="gui-im-input gui-bg-white gui-dark-bg-level-3">
					<textarea type="text" v-model="inputMsg" :fixed="true" :maxlength="-1" rows="7" @confirm="sendTextMsg" 
                    :cursor-spacing="35" :adjust-position="adjust" :show-confirm-bar="false" :auto-height="true"
                    @focus="inputFocus" @blur="inputBlur"></textarea>
        </view>
        <view class="gui-items gui-color-white gui-bg-blue b-radius-10"
              style="padding:0 20rpx; margin-right:10rpx;"
              hover-class="gui-tap" @tap="sendTextMsg">发送</view>
      </view>
	  <view class="gui-flex gui-row gui-nowrap gui-align-items-center" style="margin-left: 20rpx;">
	  	       <!-- <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" v-if="voiceBtnShow"
	  	             @tap="showRec">&#xe617;</view> -->
	  	        <view style="font-size: 50rpx;" class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" @tap="chooseImg">&#xe63d;</view>
	  			<view class="gui-flex gui-align-items-center" style="margin-left: 20rpx;" @tap="chooseTask(3)">
	  				<view style="font-size: 50rpx;" class=" gui-icons gui-secondary-text" hover-class="gui-tap" >&#xe62f; </view>
	  				<view style="margin-right: 20rpx;"> | 宣教指导</view>
	  			</view>
				<view class="gui-flex gui-align-items-center"@tap="chooseTask(1)">
					<view style="font-size: 45rpx;" class=" gui-icons gui-secondary-text" hover-class="gui-tap" >&#xe624;</view>
					<view> | 问卷填写</view>
				</view>
	  </view>
      <view>
        <gui-iphone-bottom></gui-iphone-bottom>
      </view>
    </view>
    <!-- 语音输入 -->
    <view class="gui-im-record" v-if="recShow">
		<view class="">
			<button @click="recOpen">打开录音,请求权限</button>
					| <button @click="recStart">开始录音</button>
					  <button @click="recStop">结束录音</button>
					| <button @click="recPlay">本地试听</button>
		</view>
     <!-- <view class="gui-im-record-txt">
        {{ recTxt }}
        <text v-if="recing">已录音 : {{ recLength }} s</text>
      </view>
      <view class="gui-im-record-btn"
            @tap="rec" :class="[recing ? 'gui-im-recording' : '']"></view>
      <view class="gui-im-send-voice"
            v-if="tmpVoice != ''">
        <text @tap="sendVoiceMsg">发送语音</text>
      </view>
      <view class="gui-im-record-close graceIMFonts icon-close"
            @tap="closeRec" v-if="!recing"></view> -->
    </view>
  </view>
</template>
<script>
// 挂载 vuex
import { mapState, mapMutations } from 'vuex';
import utils from '@/tools/utils/utils.js'
import url from "@/common.js"
var bgAudioMannager = uni.getBackgroundAudioManager();
//必须引入的核心
import Recorder from 'recorder-core';
//引入mp3格式支持文件；如果需要多个格式支持，把这些格式的编码引擎js文件放到后面统统引进来即可
import'recorder-core/src/engine/mp3'
import 'recorder-core/src/engine/mp3-engine'
//可选的插件支持项，这个是波形可视化插件
import 'recorder-core/src/extensions/waveview'

export default {
	name  : "gui-im-input",
	props : {
		inputContent: {
			type : String,
			default : ""
		},
		receiverUid : {
			type : String,
			default : ""
		},
		orderId : {
			type : String,
			default : ""
		},
		token : {
			type : String,
			default : ""
		},
		group : {
			type : String,
			default : ""
		},
		docTitle : {
			type : String,
			default : "个人"
		},
    adjustPosition : {
      type : Boolean,
      default : true
    },
	},
	data() {
		return {
			paddingB        : '12',
			uploading       : false,
			recShow         : false,
			recTxt          : "请点击绿色按钮开始录音",
			inputMsg        : "",
			recorderManager : null,
			recing          : false,
			recLength       : 1,
			recTimer        : null,
			tmpVoice        : '',
			voiceLen        : 0,
			voiceBtnShow    : true,
			// 播放相关
			player          : null,
			playTxt         : "试听语音",
			rec:null,
			recBlob:'',
      adjust: true
		}
	},
	computed: {
		...mapState(['graceIMUID'])
	},
  watch: {
    adjustPosition: {
      deep: true,
      handler(newValue, oldValue) {
        //子组件中监控值变化做 相关业务功能
        this.adjust = newValue
        console.log('watch-adjustPosition:'+ newValue)
      }
    },
	inputContent: {
	  deep: true,
	  handler(newValue, oldValue) {
	    console.log('撤回成功的消息内容==',newValue)
	    this.inputMsg = newValue
	  }
	}
  },
	created : function(){
		// #ifndef H5
		this.voiceBtnShow    = true;
		this.recorderManager = uni.getRecorderManager();
		this.recorderManager.onStop((res) => {
			this.tmpVoice    = res.tempFilePath;
			this.recing      = false;
			this.recTxt       =  "... 已录音 "+this.recLength+
			"s, 点击绿色按钮重新录音 ...";
			clearInterval(this.recTimer);
		});
		this.recorderManager.onError(() => {
			uni.showToast({ title: '录音失败', icon: 'none' });
			this.recing = false;
			this.recTxt   = "请点击绿色按钮开始录音",
			clearInterval(this.recTimer);
		});
		// #endif
		// #ifdef MP
		try {
		    var res = uni.getSystemInfoSync();
			res.model = res.model.replace(' ', '');
			res.model = res.model.toLowerCase();
			var res1  = res.model.indexOf('iphonex');
			if(res1 > 5){res1 = -1;}
			var res2   = res.model.indexOf('iphone1');
			if(res2 > 5){res2 = -1;}
			if(res1 != -1 || res2 != -1){
				this.paddingB = uni.upx2px(50)+'px';
			}
		} catch (e){return null;}
		// #endif
	},
	methods:{
    inputFocus(e) {
      if(!this.adjust){
        if (e.detail.height) {
          this.paddingB = String(e.detail.height); //输入框抬高到软键盘上面
        }
      }else {
        this.paddingB = 0;
      }

    },
    inputBlur() {
      this.paddingB = '12'
    },
		// 录音
		// rec : function(){
		// 	if (this.recing){
		// 		this.recorderManager.stop();
		// 		this.recing = false;
		// 		this.recTxt   =  "... 已录音 "+this.recLength
		// 		+"s, 点击绿色按钮重新录音 ...";
		// 		clearInterval(this.recTimer);
		// 	} else {
		// 		this.recorderManager.start({duration:60000, format:'mp3' });
		// 		this.recing     = true;
		// 		this.recTxt     =  "... 正在录音 ...";
		// 		this.recLength  = 1;
		// 		this.recTimer   = setInterval(()=>{this.recLength++;}, 1000);
		// 	}
		// },
		// 发送录音
		// sendVoiceMsg : function(){
		// 	if (this.tmpVoice == '') {
		// 		uni.showToast({ title: "请先录制一段语音", icon: "none" });
		// 		return;
		// 	}
		// 	// 关闭界面
		// 	this.recShow = false;
		// 	this.$emit('sendVoice', this.tmpVoice, this.recLength);
		// 	this.tmpVoice  = '';
		// 	this.recLength = 0;
		// 	this.recTxt    = "请点击绿色按钮开始录音";
		// },
		// 展示录音界面
		showRec : function(){this.recShow  = true;},
		recOpen(){
			//创建录音对象
			this.rec=Recorder({
				type:"mp3" //录音格式，可以换成wav等其他格式
				,sampleRate:16000 //录音的采样率，越大细节越丰富越细腻
				,bitRate:16 //录音的比特率，越大音质越好
				,onProcess:(buffers,powerLevel,bufferDuration,bufferSampleRate,newBufferIdx,asyncEnd)=>{
					//录音实时回调，大约1秒调用12次本回调
					//可实时绘制波形，实时上传（发送）数据
					// if(this.wave) this.wave.input(buffers[buffers.length-1],powerLevel,bufferSampleRate);
				}
			});
			
			//打开录音，获得权限
			this.rec.open(()=>{
				console.log("录音已打开");
				// if(this.$refs.recwave){//创建音频可视化图形绘制对象
				// 	this.wave=Recorder.WaveView({elem:this.$refs.recwave});
				// }
			},(msg,isUserNotAllow)=>{
				//用户拒绝了录音权限，或者浏览器不支持录音
				console.log((isUserNotAllow?"UserNotAllow，":"")+"无法录音:"+msg);
			});
		},
		recStart(){
			if(!this.rec){ console.error("未打开录音");return }
			this.rec.start();
			console.log("已开始录音");
		},

		recStop(){
			if(!this.rec){ console.error("未打开录音");return }
			this.rec.stop((blob,duration)=>{
				//blob就是我们要的录音文件对象，可以上传，或者本地播放
				this.recBlob=blob;
				//简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
				var localUrl=(window.URL||webkitURL).createObjectURL(blob);
				console.log("录音成功",blob,localUrl,"时长:"+duration+"ms");
				
				this.upload(blob);//把blob文件上传到服务器
				
				this.rec.close();//关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
				this.rec=null;
			},(err)=>{
				console.error("结束录音出错："+err);
				this.rec.close();//关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
				this.rec=null;
			});
		},
		upload(blob){
			//使用FormData用multipart/form-data表单上传文件
			//或者将blob文件用FileReader转成base64纯文本编码，使用普通application/x-www-form-urlencoded表单上传
			var form=new FormData();
			form.append("upfile",blob,"recorder.mp3"); //和普通form表单并无二致，后端接收到upfile参数的文件，文件名为recorder.mp3
			form.append("key","value"); //其他参数
			
			// var xhr=new XMLHttpRequest();
			// xhr.open("POST", "/upload/xxxx");
			// xhr.onreadystatechange=()=>{
			// 	if(xhr.readyState==4){
			// 		if(xhr.status==200){
			// 			console.log("上传成功");
			// 		}else{
			// 			console.error("上传失败"+xhr.status);
			// 		};
			// 	};
			// };
			// xhr.send(form);
		},
		
		recPlay(){
			//本地播放录音试听，可以直接用URL把blob转换成本地播放地址，用audio进行播放
			var localUrl=URL.createObjectURL(this.recBlob);
			var audio=document.createElement("audio");
			audio.controls=true;
			document.body.appendChild(audio);
			audio.src=localUrl;
			audio.play(); //这样就能播放了
			
			//注意不用了时需要revokeObjectURL，否则霸占内存
			setTimeout(function(){ URL.revokeObjectURL(audio.src) },5000);
		},

		
		// 关闭录音界面
		closeRec: function (){this.recShow = false;},

		// 发送文本消息
		sendTextMsg: function () {
			if (this.inputMsg < 1) {return false;}
			// let inputMsg = this.replaceEmoji(this.inputMsg);
			console.log('发送群组id===',this.group)
			var msg = {
				type: this.docTitle =='群组'?'groupMsg':'msg',
				contentType: 'txt',
				receiverType: '1', //接收方类型 1医生，2患者 -用于离线消息推送判断
				receiverUid: this.docTitle =='群组'?this.group:this.receiverUid, //接收者用户uid
				senderUid: this.graceIMUID, //发送方用户uid
				groupIndex: this.group, //好友组
				content: this.inputMsg, //发送的信息
				orderId: this.orderId,  // 咨询订单id
				patientId: uni.getStorageSync('mbcardObj').patientId,  // 患者id
				token: this.token //后台校验token，后面要封装在请求头
			}
			//改用http接口发送消息
			url.RequestDataNo({
				url:url.sendMsg,
				data:msg
			},res=>{
				this.$emit('sendText', this.inputMsg);
				this.inputMsg = '';
			})
		},
		chooseTask(e){
			// let imObj = {
			// // 	receiverType: '1', //接收方类型 1医生，2患者 -用于离线消息推送判断
			// 	receiverUid: this.receiverUid, //接收者用户uid
			// 	senderUid: this.graceIMUID, //发送方用户uid
			// // 	groupIndex: this.group, //好友组
			// // 	content: this.inputMsg, //发送的信息
			// // 	orderId: this.orderId,  // 咨询订单id
			// // 	patientId: uni.getStorageSync('mbhuanzInfo').patientId,  // 患者id
			// // 	token: this.token //后台校验token，后面要封装在请求头
			// // 	friendName: uni.getStorageSync('mbhuanzInfo').name, // 患者姓名
			// };
			// // this.$common.navTo('/pages/chat/task?imObj=' + encodeURIComponent(JSON.stringify(imObj)));
			if (uni.getStorageSync('mbhuanzInfo').patientId || this.docTitle=='群组') {
				this.$common.navTo('/pages/chat/task?groupIndex=' + this.group);//好友组
				uni.setStorageSync("mbtaskStatus",e)
				uni.setStorageSync("mbdocTitle",this.docTitle)
			} else{
				this.$common.msg('该用户没有绑定就诊卡')
			}
		},
		// 选择图片
		chooseImg : function(){
			console.log(utils.translate)
			uni.chooseImage({
				count      : 1,
				sizeType   : ['compressed'],
				sourceType : ['album', 'camera'],
				success    : (res)=>{
					const tempFilePaths = res.tempFilePaths;
					let resSize = res.tempFiles[0].size;
					if (resSize < 1048576) {
						this.sendImg(res.tempFiles[0])
					}else{
						utils.translate(tempFilePaths[0], 0.7, ' ', imgUrl => {
							console.log(imgUrl,'解压图片')
							//打印压缩后返回的图片url			
							this.sendImg(imgUrl)
						})
					}
					
				}
			});
		},
		getImageInfo(src) {
			let _this = this
			uni.getImageInfo({
				src,
				success(res) {
					console.log('压缩前', res)
					let canvasWidth = res.width //图片原始长宽
					let canvasHeight = res.height
					let img = new Image()
					img.src = res.path
					let canvas = document.createElement('canvas');
					let ctx = canvas.getContext('2d')
					canvas.width = canvasWidth / 2
					canvas.height = canvasHeight / 2
					ctx.drawImage(img, 0, 0, canvasWidth / 2, canvasHeight / 2)
					canvas.toBlob(function(fileSrc) {
					let imgSrc = window.URL.createObjectURL(fileSrc)
						console.log('压缩后', imgSrc)
						_this.sendImg(imgSrc)
					})
				}
			})
		},
		// 发送图片信息
		sendImg(img){
			this.$common.uploadFile(img,resx=>{
				let msg = {
					type: this.docTitle =='群组'?'groupMsg':'msg',
					contentType: 'img',
					receiverUid: this.docTitle =='群组'?this.group:this.receiverUid, //接收者用户uid
					senderUid: this.graceIMUID, //发送方用户uid
					groupIndex: this.group, //好友组
					content: resx.data.url, //发送的信息
					orderId: this.orderId,  // 咨询订单id
					token: this.token //后台校验token，后面要封装在请求头
				}
				url.RequestData({
					url:url.sendMsg,
					data:msg
				},res=>{
					this.$emit('chooseImage', img);
				})
			})
		},
	}
}
</script>
<!--<style scoped>-->
<!--.gui-im-footer{background:#FFFFFF; width:100%; position:fixed; left:0; bottom:0; height:130rpx; display:flex; flex-wrap:nowrap; overflow:hidden; box-shadow:1px 1px 6px #999999; align-items:center;}-->
<!--.gui-im-footer .gui-items{width:auto; line-height:88rpx; flex-shrink:0; font-size:36rpx; color:#2B2E3D;}-->
<!--.gui-im-menus{width:80rpx; height:80rpx; flex-shrink:0; line-height:80rpx; text-align:center;}-->
<!--.gui-im-input{width:600rpx; margin:10rpx; background:#F4F5F6; border-radius:6rpx; height:70rpx;}-->
<!--.gui-im-input input{width:100%; background:#F4F5F6; color:#2B2E3D; height:50rpx; line-height:40rpx; font-size:32rpx; margin-top:12rpx;}-->
<!--.gui-im-input textarea{width:100%; background:#F4F5F6; color:#2B2E3D; height:80rpx; line-height:40rpx; font-size:32rpx; margin-top:12rpx;}-->
<!--.gui-im-record{width:100%; position:fixed; left:0; bottom:0; background:#FFFFFF; padding:30px 0; padding-bottom:100rpx; z-index:11; box-shadow:1px 1px 6px #999999;}-->
<!--.gui-im-record-close{width:100rpx; height:100rpx; position:absolute; top:0px; right:0px; z-index:100; text-align:center; line-height:100rpx; color:#888888; font-size:38rpx !important;}-->
<!--.gui-im-record-txt{text-align:center; font-size:26rpx; line-height:30px; padding-bottom:10px; color:#CCCCCC;}-->
<!--.gui-im-record-btn{width:60px; height:60px; margin:0 auto; border:5px solid #F1F2F3; border-radius:100%; background:#00B26A;}-->
<!--.gui-im-recording{background:#FF0000; animation:fade linear 2s infinite;}-->
<!--@keyframes fade{from{opacity:0.1;} 50%{opacity:1;} to{opacity:0.1;}}-->
<!--.gui-im-record-txt text{color:#00B26A; padding:0 12px;}-->
<!--.gui-im-send-voice{margin-top:12px; font-size:28rpx; color:#00BA62; text-align:center;}-->
<!--.gui-im-send-voice text{margin:0 15px; color:#00BA62;}-->

<!--@font-face{font-family:"graceIMFonts"; src:url('data:font/truetype;charset=utf-8;base64,d09GMgABAAAAAASoAAsAAAAACpAAAARbAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDMgqILIZ4ATYCJAMUCwwABCAFhG0HSxsHCcgOJQUABgUAAMBk8EB6fW8yk/3Z7Cd4BLPXQW6vA9B99wc4f67lvzL5jC8R2YVkAS+XAsN4QPB1pPqF+wfHUj8K/8Hyvu1BXjGyLDq25lGBDagb1ySRuNkEfcvRGr5iXEVcDQGYRBOJS2OXykbwRoFqQYpmTZc48HZYUBEkgaF3BBypENvQMbT12hNgq/b24g35wgANXYLX6jelSITc52EvKqRlDzJKWljFuYDbcSCBTAAFYkGgZxZuhipT05hBv+dOLYCJgYb2POx50vPyFxW2DZaiQzqT/8MDDYlCgHCAGk+KrY0reB4m8aDB8yQeJDwv50HBiwrZtGh0ur0JhAGiEhCdYIisoERHonui2lcmOM2zJHp7m6a+suXpoNZng9tfDG17vkyohugi+3dbns4wdwWyFuTaGyL2DH1o+v/RweMTg2Y6LqngIEk2qXNSr6zPX9naOrilZVBb25D29qFN+/Nh99riT7Z3vdsad6xlyKkr1tWNZ09eGz6kkbRWS7iliav7hBVp1hm64Q4O3rMhcFCzzBa4ywoa3MgWtHtjyNAmVUhYR3kdd6O7DasHDfpeN2jwb7vbQFjoV6Fa7o6VK9v0qChdVrZdRURhRbwW3rqlUHWLS5mMreMeM3nRSDO2TbYUy16uGosg002pTpZwphre5b7iZKa3S1VPqnNboCS+PE4Hmd9c9SJ2Lk+1zG3JjnAsPmH0XBpvDWJl+WX186e6zIWKSG6z0np0qthYRZSdTEQULL34fb9uJZ66RKiru0eP4K4lSXcWecR/xPf20/WbFi0Bz/8qJrcUz0VLHq9jd7SqyEjVyoWKSFZYZV4TL99QQihCuiGEghgP3sLyqYFyCAlv/7hDnEsCyWXFCOnjZkAVINnlhrLtTEJFJEusvRjRsGVZpxpGRH+psDBFlC1MRCRsj0k9bjZvPtcjYrOpiXamCVUROssu7hLWO2VNhdl5+yxFRU/Z8/pcBwNGkpu2yNAKAPaY/CNXAvak/CmXAfaM/CYXH8o/w6M7d+aODar8ZrgUAB2fJ5zRauxBhbTgeNSSWHrgShOOqa8crbp6d0qSt09n10Gh0T6LTOBwDfjzrxh7EEUmBAZBFmh4EAUSg0SkwmaCjotCcGBQAyYZlBzvIgCKG0I5gXTGAAS+nIKGN9dB4ssnUmH/QCeY/+DAV0gwGSICzugiWUw3GxZgjmER8V1IESyTCDvz1YvrsdSmc9Qu18jNmDqVIIrLznUnS7CJaRH7ONuleMYIItQyUDFcD+u6hWxqqVhg0TJjdnpMzpSgJ4oWLAN8aYCC4WBgRBA8XSAUBCxMiLIww9fz+vVgJNrQ4aARXT/8zWAoTpTVI+KIJjYBskRjJuralEon7UjEw2iEgCC3QgsDRDF1YXQes0DYwZupYAQY0cgZIjbpxNDJSFJNdPU6Yx5LT5ueeAZdaEIKJXThwGPGdM60RcRz0WJbUY1TF8sb8IrH8sVdigkAAA==') format('truetype');}-->
<!--.graceIMFonts{font-family:"graceIMFonts" !important; font-style:normal; color:#2B2E3D; font-size:56rpx;}-->
<!--.icon-voice:before{content:"\e63a";}-->
<!--.icon-photograph:before{content:"\e619";}-->
<!--.icon-close:before{content:"\e625";}-->
<!--.icon-kbd{content:"\e73b";}-->
<!--</style>-->

<style scoped>
.gui-im-footer {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
}

.gui-im-footer .gui-items {
  width: auto;
  line-height: 70rpx;
  flex-shrink: 0;
  font-size: 28rpx;
}

.gui-im-menus {
  width: 80rpx;
  height: 80rpx;
  flex-shrink: 0;
  line-height: 80rpx;
  text-align: center;
}

.gui-im-input {
  width: 600rpx;
  margin: 10rpx;
  padding: 12rpx 16rpx;
  border-radius: 6rpx;
}

.gui-im-input textarea {
  width: 100%;
  height: 40rpx;
  max-height: 400rpx;
  line-height: 40rpx;
  font-size: 34rpx;  
  /* margin-top: 10rpx; */
}

.gui-im-record {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
  padding: 30px 0;
  padding-bottom: 100rpx;
  z-index: 11;
}

.gui-im-record-close {
  width: 100rpx;
  height: 100rpx;
  position: absolute;
  top: 0px;
  right: 0px;
  z-index: 100;
  text-align: center;
  line-height: 100rpx;
  font-size: 38rpx !important;
}

.gui-im-record-txt {
  text-align: center;
  font-size: 26rpx;
  line-height: 30px;
  padding-bottom: 10px;
}

.gui-im-record-btn {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  border: 5px solid #F1F2F3;
  border-radius: 100%;
  background: #00B26A;
}

.gui-im-recording {
  background: #FF0000;
  animation: fade linear 2s infinite;
}

@keyframes fade {
  from {
    opacity: 0.1;
  }

  50% {
    opacity: 1;
  }

  to {
    opacity: 0.1;
  }
}

.gui-im-record-txt text {
  color: #00B26A;
  padding: 0 12px;
}

.gui-im-send-voice {
  margin-top: 12px;
  font-size: 28rpx;
  color: #00BA62;
  text-align: center;
}

.gui-im-send-voice text {
  margin: 0 15px;
  color: #00BA62;
}

.gui-icons {
  /* font-size: 50rpx; */
}
</style>
