<template>
	<view class="gui-padding">
		<!-- 治疗方案  -->
		<!-- <fagl v-show="templateDictKey == 0" :userName="userName" :sex="sex" :patientId="patientId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey" :programmeTypeId="templateId" :programmeType="programmeType" :schemeTime="schemeTime"></fagl> -->
		<!-- 中医4P饮食健康管理建议-饮食 -->
		<ysjy v-show="templateDictKey == 1" :info="info" :patientId="patientId"  :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></ysjy>
		<!-- 居民中医体质记录 -->
		<jmzy v-show="templateDictKey == 2" :patientId="patientId"  :info="info" :visitRecordId="visitRecordId"  :templateId="templateId" :templateDictKey="templateDictKey"></jmzy>
		<!-- 其他模板 -->
		<others :patientId="patientId" :info="info" v-show="templateDictKey != 0 && templateDictKey != 1 && templateDictKey != 2 && templateDictKey != 26 && templateDictKey != 29 && templateDictKey != 30 && templateDictKey != 31 && templateDictKey != 35 && templateDictKey != 36" :templateId="templateId" :templateDictKey="templateDictKey"></others>
		<!-- 中医饮食指导（儿科一般指导） -->
		<rkyb :patientId="patientId" :info="info" v-show="templateDictKey == 26 || templateDictKey == 29 || templateDictKey == 30" :templateId="templateId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></rkyb>
		<!-- 运动减脂方案 -->
		<ydjz :patientId="patientId" :info="info" v-show="templateDictKey == 31" :templateId="templateId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></ydjz>
    <!-- 中医禁食疗法方案 -->
    <jslf :patientId="patientId" :info="info" v-show="templateDictKey == 35" :templateId="templateId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></jslf>
    <!-- 营养干预方案 -->
    <yygy :patientId="patientId" :info="info" v-show="templateDictKey == 36" :templateId="templateId" :visitRecordId="visitRecordId" :templateDictKey="templateDictKey"></yygy>

    <view class="h50"></view>
	</view>
</template>

<script>
	// import fagl from "../comtzreport/fagl.vue"
	import ysjy from "./comtzreport/ysjy.vue"
	import jmzy from "./comtzreport/jmzy.vue"
	import others from "./comtzreport/others.vue"
	import rkyb from './comtzreport/rkyb.vue'
	import ydjz from './comtzreport/ydjz.vue'
  import jslf from './comtzreport/jslf.vue'
  import yygy from './comtzreport/yygy.vue'

	export default {
		components:{ysjy,jmzy,rkyb,ydjz,others,jslf,yygy},
		data() {
			return {
				templateDictKey: "",  // 模板id
				patientId:"",         // 患者id
				templateId:"",        // 病历详情id
				programmeType: "",        // 方案类型
				visitRecordId:"",        // 就诊记录id
				obj:{},
				schemeTime:"",
				userName:'',
				sex:'',
				info:{}
			}
		},
		onLoad(op) {
			this.info = JSON.parse(op.info)
			console.log('当前用户i新宁县===',this.info)
			var p = JSON.parse(op.obj)
			this.userName = p.userName
			this.templateDictKey = p.templateDictKey;  // 病历模板 key
			this.patientId = p.patientId;              // 患者id
			this.templateId = p.templateId;              //病历详情id
			this.visitRecordId = p.visitRecordId;        //就诊记录id
			this.schemeTime = p.createTime;        //方案下达时间
			this.programmeType = p.programmeType;        //方案类型

			if(p.title){
				uni.setNavigationBarTitle({
					title:p.title
				})
			}
		},
		methods: {

		}
	}
</script>

<style>

</style>
