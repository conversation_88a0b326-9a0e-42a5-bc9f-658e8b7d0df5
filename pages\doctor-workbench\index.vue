<template>
	<view>
		<gui-page ref="guipage" :isLoading="pageLoading" :refresh="true" @reload="reload">
			<view slot="gBody" class="gui-flex1 gui-flex gui-columns">
				<!-- 顶部个人信息 -->
				<view class="header-info">
					<view class="gui-flex gui-rows pr-20 gui-space-between">
						<view class="gui-text-small demo">
							<view style="letter-spacing: 5rpx;" class="fs-40 gui-bold mb-20">
								<text>你好，{{ userInfo.nickName || userInfo.userName || '韦珍群' }}医生</text>
							</view>
							<!-- <view><text class="fs-30" style="color:#B2BAC6;">{{ currentDateStr }}</text></view> -->
						</view>
						<!-- <view class="yisheng">
							<img class="doctor-avatar" src="../../static/images/pone.png" />
						</view> -->
					</view>
				</view>

				<!-- 数据看板 -->
				<view class="data-overview-card">
					<!-- <view class="card-title">数据看板</view> -->
					<view class="data-horizontal-grid">
						<view class="data-item-horizontal">
							<view class="data-number">{{ dataOverview.patientTotalCount || 0 }}</view>
							<view class="data-today">今日 +{{ dataOverview.patientTodayCount || 0 }}</view>
							<view class="data-label">我的患者</view>
						</view>
						<view class="data-divider"></view>
						<view class="data-item-horizontal">
							<view class="data-number">{{ dataOverview.consultTotalCount || 0 }}</view>
							<view class="data-today">今日 +{{ dataOverview.consultTodayCount || 0 }}</view>
							<view class="data-label">累计咨询</view>
						</view>
						<view class="data-divider"></view>
						<view class="data-item-horizontal">
							<view class="data-number">{{ dataOverview.programTotalCount || 0 }}</view>
							<view class="data-today">今日 +{{ dataOverview.programTodayCount || 0 }}</view>
							<view class="data-label">累计指导</view>
						</view>
						<!-- <view class="data-divider"></view>
						<view class="data-item-horizontal">
							<view class="data-number">0</view>
							<view class="data-today">今日 +0</view>
							<view class="data-label">我的评价</view>
						</view> -->
					</view>
				</view>

				<!-- 待办事项和快捷入口容器 -->
				<view class="todo-quickentry-container">
					<!-- 待办事项 -->
					<view class="todo-card">
						<view class="card-title">待办事项</view>
						<view class="todo-horizontal-grid">
							<view class="todo-item-horizontal" @tap="goToTodoPage('reply')">
								<view class="todo-number">{{ todoCount.noReplyCount || 0 }}</view>
								<view class="todo-label">待回复</view>
							</view>
							<view class="todo-divider"></view>
							<view class="todo-item-horizontal" @tap="goToTodoPage('guide')">
								<view class="todo-number">{{ todoCount.noGuideCount || 0 }}</view>
								<view class="todo-label">待指导</view>
							</view>
							<view class="todo-divider"></view>
							<view class="todo-item-horizontal" @tap="goToTodoPage('group')">
								<view class="todo-number">{{ todoCount.noGroupCount || 0 }}</view>
								<view class="todo-label">待分组</view>
							</view>
						</view>
					</view>
					
					<!-- 快捷入口 -->
					<view class="quickentry-card" @tap="goToArchiveEntry">
						<view class="quickentry-title">快捷入口</view>
						<view class="quickentry-icon">
							<image src="../../static/images/shu.png" mode="aspectFit"></image>
						</view>
						<view class="quickentry-label">患者建档</view>
					</view>
				</view>

				<!-- 健康数据动态 -->
				<view class="health-data-card">
					<view class="card-header">
						<view class="card-title">健康动态</view>
						<view class="more-link" @tap="goToHealthDataMore">更多 ></view>
					</view>
					<view class="health-list" v-if="healthDataList.length > 0">
						<view class="health-item" v-for="(item, index) in healthDataList" :key="index">	
							<view class="health-status-dot" :class="item.measureResult && item.measureResult.includes('异常') ? 'abnormal-dot' : 'normal-dot'"></view>
							<view class="health-time">{{ formatTime(item.create_time) }}</view>
							<view class="health-patient-name">
								<text class="patient-name-text">{{ item.patientName }}</text>
							</view>
							<view class="health-content" @tap="abnormal(item)">
								<text class="health-data-value" :class="item.measureResult && item.measureResult.includes('异常') ? 'abnormal-result' : 'normal-result'">
									{{ formatHealthData(item) }}
								</text>
							</view>
						</view>
					</view>
					<view v-else class="no-data">暂无数据</view>
				</view>

				<!-- 入组患者人群排行 -->
				<view class="crowd-ranking-card">
					<view class="card-title">入组患者人群排行</view>
					<view class="ranking-header">
						<view class="ranking-col-number">序号</view>
						<view class="ranking-col">人群名称</view>
						<view class="ranking-col">累计（人）</view>
					</view>
					<view class="ranking-list" v-if="crowdTypeList.length > 0">
						<view class="ranking-item" v-for="(item, index) in crowdTypeList" :key="index" :class="index % 2 === 1 ? 'even-row' : ''">
							<view class="ranking-number">{{ index + 1 }}</view>
							<view class="ranking-name">{{ item.name }}</view>
							<view class="ranking-count">{{ item.count }}</view>
						</view>
					</view>
					<view v-else class="no-data">暂无数据</view>
				</view>
			</view>
		</gui-page>
	</view>
</template>

<script>
import {getUserPwdFlag} from '@/api/login.js'
import {
	mapState,
	mapMutations
} from 'vuex';
import url from "../../common.js";

export default {
	data() {
		return {
			imUser: {
				userId: uni.getStorageSync('mbuserInfo').userId, // 系统用户id 空
				userUid: '', //链接im返回的uuid
				userType: '1', // 用户类型  1：医生  2：患者
				uname: uni.getStorageSync('mbuserInfo').userName, // 用户名
				nickName: uni.getStorageSync('mbuserInfo').nickName, // 昵称
				uface: uni.getStorageSync('mbuserInfo').avatar // 头像
			},
			userInfo: {}, //医生信息
			roles: [], //角色
			pageLoading: true,
			userInfo: {},
			currentDateStr: '',
			dataOverview: {},
			todoCount: {},
			healthDataList: [],
			crowdTypeList: [],
			code: '',
			ddCorpId: this.$common.ddId,
			bingingWxUser: this.$common.bingingWxUser, //使用企业微信的环境用true，目前仅广中医true，其他环境是false
			wxcode: ''
		}
	},
	onLoad(option) {
		// #ifdef H5
		//获取user-agaent标识头
		let ua = window.navigator.userAgent.toLowerCase();
		//判断 微信浏览器 或者 企业微信浏览器
		if (this.isWechat() || this.isWorkWechat()) {
			// TODO 医生端授权开关
			// alert('医生端授权开关'+this.bingingWxUser)
			if (this.bingingWxUser == true) {
				let params = this.getRequestParams();
				let code = params['code'];
				this.wxcode = code
				this.toWxLogin()
			}
		}
		// #endif
	},
	onShow() {
		this.checkLogin(); //检查登录状态
	},
	computed: {
		...mapState(['getMsgReadedNum','graceIMStatus','graceIMMsgs'])
	},
	methods: {
		checkLogin() {
			let self = this;
			this.$common.checkLogin(res => {
				if (res === 0) {
					//跳转登录页面
					self.$common.navLaunch('/pages/login/index')
				} else if (res > 0) {
					this.getPageData();//初始化
				}
			});
		},
		getPageData() {
			this.getInfo();//获取角色信息
			this.getCurrentDate();//获取当前日期信息
			this.getpwdStatus();//检查密码过期判断修改提醒
		},
		init() {
			this.getUserInfo();
			this.getCurrentDate();
			this.getDataOverview();
			this.getTodoCount();
			this.getHealthData();
			this.getCrowdTypeCount();
			// this.setTabBarBadge();
		},
		
		getpwdStatus(){//检查密码过期判断修改提醒
			let that = this;
			getUserPwdFlag().then(res =>{
				// console.log('检查密码',res)
				if (res.data.pwdStatus) {
					uni.showModal({
						title: '提示',
						content: '密码过期，请重新设置！',
						showCancel:false,
						success: function (res) {
							if (res.confirm) {
								that.$common.navLaunch('/pages/my/revise')
							} else if (res.cancel) {
								that.getCode();
							}
						}
					});
				}
			})
			return
		},
		//用户信息
		getInfo() {
			this.$common.RequestData({
				url: this.$common.getInfo,
				data: {},
				method: 'get',
			}, res => {
				this.userInfo = res.data.user;
				uni.setStorageSync("mbuserInfo", res.data.user)
				this.roles = res.data.posts;
				// 获取数据
				this.getDataOverview();
				this.getTodoCount();
				this.getHealthData();
				this.getCrowdTypeCount();

				if (uni.getStorageSync("mbuserInfo") && this.graceIMStatus !=='success') {
					//发起IM登录
				  this.$store.dispatch('graceIMConnect', this.imUser).then(res=>{
				  });
				}
				// this.checkDing();
			}, )
		},
		// 获取用户信息
		getUserInfo() {
			this.userInfo = uni.getStorageSync('mbuserInfo') || {};
		},
		
		// 获取当前日期
		getCurrentDate() {
			const now = new Date();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
			const weekDay = weekDays[now.getDay()];
			this.currentDateStr = `${month}月${day}日 ${weekDay}`;
		},
		
		// 获取数据看板统计
		getDataOverview() {
			this.$common.RequestData({
				url: this.$common.getDataOverview,
				method: 'GET'
			}, res => {
				if (res.code === 200) {
					this.dataOverview = res.data;
				}
			});
		},

		// 获取待办事项统计
		getTodoCount() {
			this.$common.RequestData({
				url: this.$common.getTodoItemCount,
				method: 'GET'
			}, res => {
				if (res.code === 200) {
					this.todoCount = res.data;
					// 获取数据后设置角标
					// this.setTabBarBadge();
				}
			});
		},
		
		// 患者建档快捷入口
		goToArchiveEntry() {
			this.$common.navTo('/pages/patient/pendingList');
		},

		// 获取健康数据动态
		getHealthData() {
			this.$common.RequestData({
				url: this.$common.getMonitorPerform,
				method: 'GET'
			}, res => {
				if (res.code === 200) {
					this.healthDataList = res.data.slice(0, 3); // 只显示前3条
				}
			});
		},

		// 格式化时间显示
		formatTime(timeStr) {
			if (!timeStr) return '';
			try {
				const date = new Date(timeStr);
				// const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				// return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
				return `${month}/${day} ${hours}:${minutes}:${seconds}`;
			} catch (e) {
				return timeStr;
			}
		},

		// 格式化健康数据显示
		formatHealthData(item) {
			if (!item) return '';
			
			// 根据监测项目类型格式化显示
			switch(item.monitorName) {
				case '血压':
					return `血压: ${item.systolicPressure}/${item.diastolicPressure}mmHg`;
				case '血糖':
					if (item.fastingGlucose) return `空腹血糖: ${item.fastingGlucose}mmol/L`;
					if (item.dinnerGlucose) return `餐后血糖: ${item.dinnerGlucose}mmol/L`;
					if (item.randomGlucose) return `随机血糖: ${item.randomGlucose}mmol/L`;
					return `血糖: ${item.measureResult}`;
				case '脉搏':
					return `脉搏: ${item.pulse}次/分钟`;
				case '血氧':
					return `血氧: ${item.bloodOxygen}%`;
				case '体温':
					return `体温: ${item.temperature}℃`;
				case '呼吸':
					return `呼吸: ${item.breathing}次/分钟`;
				default:
					return `${item.monitorName}`;
			}
		},

		// 设置底部导航栏角标
		// setTabBarBadge() {
		// 	// 计算总的待办事项数量
		// 	const totalTodoCount = (this.todoCount.noReplyCount || 0) +
		// 						   (this.todoCount.noGuideCount || 0) +
		// 						   (this.todoCount.noGroupCount || 0) +
		// 						   (this.todoCount.noAuditCount || 0);

		// 	if (totalTodoCount > 0) {
		// 		// 设置工作台tab的角标
		// 		uni.setTabBarBadge({
		// 			index: 0, // 工作台是第一个tab
		// 			text: totalTodoCount.toString()
		// 		});
		// 	} else {
		// 		// 移除角标
		// 		uni.removeTabBarBadge({
		// 			index: 0
		// 		});
		// 	}
		// },

		// 待办事项跳转
		goToTodoPage(type) {
			// 根据不同类型跳转到对应页面
			switch(type) {
				case 'reply':
					// 跳转到待回复页面
					this.$common.navTab('/pages/chat/index');
					break;
				case 'guide':
					// 跳转到待指导页面
					uni.setStorageSync('patientParam', {type: 'guide'});
					this.$common.navTab('/pages/patient/patientIndex');
					break;
				case 'group':
					// 跳转到待分组页面
					uni.setStorageSync('patientParam', {type: 'group'});
					this.$common.navTab('/pages/patient/patientIndex');
					break;
				default:
					break;
			}
		},

		abnormal(item) {
				if (item) {
					this.gotongue(item)
					return
				}
				this.$common.navTo('/pages/home/<USER>')
			},

		// 处理健康数据点击事件
		gotongue(item) {
			this.$common.RequestData({
				url: this.$common.readLingual + item.id,
				data: {},
				method: 'get',
			}, res => {
				if (res.code == 200) {
					this.$common.navTo('/pages/patient/details')
					uni.setStorageSync('mbdetailsPatientId', item.patientId)
					uni.setStorageSync('mbmonitorId', item.monitorId)
				} else {
					this.$common.msg("数据异常，请稍后刷新重试~")
				}
			})
		},

		// 健康数据查看更多
		goToHealthDataMore() {
			this.$common.navTo('/pages/home/<USER>');
		},

		// 获取患者人群类型统计
		getCrowdTypeCount() {
			this.$common.RequestData({
				url: this.$common.getCrowdTypeCount,
				method: 'GET'
			}, res => {
				if (res.code === 200) {
					this.crowdTypeList = res.data;
				}
				this.pageLoading = false;
			});
		},
		
		// 下拉刷新
		reload() {
			this.init();
			this.$refs.guipage.endReload();
		},

		toWxLogin(type) {
			//type=login的时候，说明是历史账户安全登录，不需要微信登录逻辑

			if (uni.getStorageSync("logintype")) {return}
			// 微信登录逻辑
			if (!this.wxcode) {
				// alert('微信登录逻辑')
				let redirect_uri = window.location.href
				console.log('redirect_uri', redirect_uri)
				// 1000066--慢病正式环境
				// 1000018--公司测试环境
				// let uri =
				// 	'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx51f5aff0f538ed8b&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE&agentid=1000066#wechat_redirect'
				let uri =
					'https://open.weixin.qq.com/connect/oauth2/authorize?appid='+this.$common.appid+'&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE&agentid='+this.$common.agentid+'#wechat_redirect'
				window.location.href = uri.replace("REDIRECT_URI", redirect_uri)
			} else {
				this.toBinding()
			}
		},
		getRequestParams() {
			let url = location.href;
			let requestParams = {};
			if (url.indexOf('?') !== -1) {
				let str = url.substr(url.indexOf('?') + 1); //截取?后面的内容作为字符串
				console.log(str, '?后面的内容');
				let strs = str.split('&'); //将字符串内容以&分隔为一个数组
				console.log(strs, '以&切割的数组');
				for (let i = 0; i < strs.length; i++) {
					requestParams[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1]);
					// 将数组元素中'='左边的内容作为对象的属性名，'='右边的内容作为对象对应属性的属性值
				}
			}
			return requestParams;
		},
		toBinding() {
			// 向后台发起请求 携带code
			let queryParams = {
				code: this.wxcode
			}
			this.$common.RequestData({
				url: this.$common.toBinding,
				data: queryParams,
				method: "get"
			}, res => {
				uni.setStorageSync('mbwxUserId',res.data.wxUser)
				this.wxcode = ''
			}, null, err => {
				console.log(err);
			})
		},
		isWechat() {
			//获取user-agaent标识头
			var ua = window.navigator.userAgent.toLowerCase();
			//判断ua和微信浏览器的标识头是否匹配
			if (ua.match(/micromessenger/i) == 'micromessenger') {
				return true;
			} else {
				return false;
			}
		},
		isWorkWechat() {
			//获取user-agaent标识头
			var ua = window.navigator.userAgent.toLowerCase();
			//判断ua和微信浏览器的标识头是否匹配
			if ((ua.match(/micromessenger/i) == 'micromessenger') && (ua.match(/wxwork/i) == 'wxwork')) {
				return true;
			} else {
				return false;
			}
		},

		// checkDing() {
		// 	let self = this;
		// 	// 先判断是否是在钉钉中运行此应用
		// 	if (dd.env.platform != 'notInDingTalk') {
		// 		// self.$common.msg('在钉钉环境中。。')
		// 		//钉钉环境下未绑定钉钉用户的触发 关联绑定
		// 		if (!uni.getStorageSync('mbddUserId')) {
		// 			dd.ready(() => {
		// 				// self.$common.msg('钉钉sdk 准备就绪！')
		// 				dd.runtime.permission.requestAuthCode({
		// 					corpId: self.ddCorpId
		// 				}).then((result) => {
		// 					self.code = result.code;
		// 					// self.$common.msg('钉钉code获取：'  + self.code)
		// 					self.$common.RequestData({
		// 						url: self.$common.dingBindUser,
		// 						data: {
		// 							password: self.code,
		// 							userId: self.userInfo.userId
		// 						}
		// 					}, res => {
		// 						// self.$common.msg('钉钉请求接口返回：'  + JSON.stringify(res))
		// 						if (res.code === 200) {
		// 							uni.setStorageSync("mbddUserId", res.data.ddUserId)
		// 						}
		// 					});
		// 				}).catch(err => {
		// 					self.$common.msg('获取钉钉用户异常：' + JSON.stringify(err))
		// 				});
		// 			});
		// 		}
		// 	} else {
		// 		console.warn('请在钉钉中访问本应用!');
		// 	}
		// }
	}
}
</script>

<style scoped>
/* 顶部个人信息 */
.header-info {
	padding: 20rpx 15rpx 0rpx 20rpx;
	background: white;
	color: #333;
}

.header-info .gui-flex {
	align-items: center;
}

.demo {
	color: #333;
}

.fs-40 {
	font-size: 40rpx;
}

.fs-30 {
	font-size: 28rpx;
}

.gui-bold {
	font-weight: bold;
}

.mb-20 {
	margin-bottom: 20rpx;
}

.gui-flex {
	display: flex;
}

.gui-rows {
	flex-direction: row;
}

.pr-20 {
	padding-right: 20rpx;
}

.gui-space-between {
	justify-content: space-between;
}

.gui-text-small {
	font-size: 28rpx;
}

.yisheng {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 200rpx;
	height: 200rpx;
	flex-shrink: 0;
}

.doctor-avatar {
	width: 100%;
	height: 100%;
	object-fit: contain;
	max-width: 200rpx;
	max-height: 200rpx;
}

/* 卡片通用样式 */
.data-overview-card,
.todo-card,
.health-data-card,
.crowd-ranking-card {
	margin: 15rpx;
	padding: 30rpx;
	background: white;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.more-link {
	font-size: 28rpx;
	color: #3388FF;
}

/* 数据看板样式 */
.data-overview-card {
	background-image: url(../../static/images/databg-q.png);
	background-repeat: no-repeat;
	background-size: cover;
	border-radius: 30rpx;
	color: white;
	margin-top: 20rpx !important;
	/* margin-bottom: 20rpx;
	margin-left: 20rpx;
	margin-right: 20rpx; */
}

.data-overview-card .card-title {
	color: white;
	font-size: 34rpx;
}

.data-horizontal-grid {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.data-item-horizontal {
	text-align: center;
	color: white;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
}

.data-divider {
	width: 1rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.3);
	margin: 0 10rpx;
}

.data-number {
	font-size: 46rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.data-label {
	font-size: 28rpx;
	margin-top: 8rpx;
}

.data-today {
	font-size: 28rpx;
	opacity: 0.8;
	margin-bottom: 5rpx;
}

/* 待办事项样式 */
.todo-horizontal-grid {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.todo-item-horizontal {
	text-align: center;
	color: #333;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
	padding: 20rpx 0;
}

.todo-divider {
	width: 1rpx;
	height: 60rpx;
	background: #e0e0e0;
	margin: 0 10rpx;
}

.todo-number {
	font-size: 50rpx;
	font-weight: bold;
	color: #6A5ACD;
	margin-bottom: 10rpx;
}

.todo-label {
	font-size: 28rpx;
	color: #666;
}

.todo-goto {
	font-size: 20rpx;
	color: #6A5ACD;
	margin-top: 4rpx;
	text-align: center;
}

/* 待办事项和快捷入口容器样式 */
.todo-quickentry-container {
	display: flex;
	justify-content: space-between;
	margin: 15rpx;
	height: 250rpx;
}

.todo-card {
	flex: 0 0 68%; /* 待办事项占据70% */
	background: white;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	padding: 30rpx;
	margin: 0;
}

.quickentry-card {
	flex: 0 0 30%; /* 快捷入口占据30% */
	background: white;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
}

.quickentry-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	/* margin-bottom: 20rpx; */
	text-align: center;
	align-self: flex-start;
}

.quickentry-icon {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 20rpx 0;
}

.quickentry-icon image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.quickentry-label {
	font-size: 28rpx;
	color: #666;
	text-align: center;
	/* margin-top: 10rpx; */
}

/* 健康数据动态样式 */
.health-list {
	max-height: 400rpx;
	overflow-y: auto;
}

.health-item {
	display: flex;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	position: relative;
}

.health-item:last-child {
	border-bottom: none;
}

.health-status-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	margin-right: 15rpx;
	flex-shrink: 0;
}

.health-status-dot.normal-dot {
	background: #52c41a;
}

.health-status-dot.abnormal-dot {
	background: #ff4d4f;
}

.health-time {
	font-size: 28rpx;
	color: #999;
	margin-right: 15rpx;
	flex-shrink: 0;
	min-width: 160rpx;
}

.health-patient-name {
	margin-right: 15rpx;
	flex-shrink: 0;
	min-width: 120rpx;
}

.patient-name-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.health-content {
	flex: 1;
	margin-right: 15rpx;
}

.health-data-value {
	font-size: 28rpx;
	color: #333;
}

.abnormal-result {
	color: #ff4d4f;
}

.normal-result {
	color: #333;
}

/* 入组患者人群排行样式 */
.ranking-header {
	display: flex;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
	font-weight: bold;
	color: #666;
	background: #f8f9fa;
	/* margin: 0 -10rpx; */
	padding-left: 30rpx;
	padding-right: 30rpx;
}

.ranking-col-number {
	flex: 0 0 80rpx;
	text-align: center;
	font-size: 28rpx;
}

.ranking-col {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
}

.ranking-list {
	overflow-y: auto;
}

.ranking-item {
	display: flex;
	padding: 10rpx 30rpx;
	border-bottom: 1rpx solid #f8f9fa;
}

.ranking-item:last-child {
	border-bottom: none;
}

.ranking-item.even-row {
	background: #f8f9fa;
}

.ranking-number {
	flex: 0 0 80rpx;
	text-align: center;
	font-size: 28rpx;
	color: #666;
	font-weight: bold;
}

.ranking-name {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #333;
}

.ranking-count {
	flex: 1;
	text-align: center;
	font-size: 28rpx;
	color: #3388FF;
	font-weight: bold;
}

.no-data {
	text-align: center;
	padding: 60rpx 0;
	color: #999;
	font-size: 28rpx;
}
</style>

