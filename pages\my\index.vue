<template>
	<!-- <view style="position: fixed;top:0rpx;left:0rpx;width:100%;height: 100% ; background-image: url(../../static/images/bg-a.png); background-size: contain;background-repeat: no-repeat;background-size: 100% 100%;"> -->

	<view style="position: fixed;width:100%;height: 100% ;">
		<view >
			<!-- <view style="height: 15%;"></view> -->
			<view style="background-image: url(../../static/images/mybg.png);background-size: contain;background-repeat: no-repeat;background-size: 100% 100%;" class="gui-flex mt-30 w-100 gui-justify-content-center">
				<view class="avat">
					<img class="avatar" :src="avatar" />
				</view>
			</view>

			<view style="height: 6%;" class="gui-list gui-margin-top   gui-padding">
				<view class="gui-columns gui-wrap gui-space-around gui-align-items-center gui-space-between">
					<text class="mb-20 gui-text gui-color-white fs-40 gui-bold">{{userInfo.nickName || "-"}}</text>
					<text class="gui-text-small gui-color-white  ">{{userInfo.docTitle || "-"}}</text>
				</view>
			</view>

			<view
				style="background-color: white;height: 80vh;overflow: hidden;border-top-left-radius:50rpx;border-top-right-radius:50rpx  "
				class="mt-80">
				<view class="mx-40 gui-list gui-margin-top gui-padding">
					<view class="gui-list-items gui-border-b">
						<img class="iconwh" src="../../static/images/revise.png" />
						<view class="gui-list-body ">
							<navigator url="/pages/my/revise">
								<view class="gui-list-title">
									<text class=" text-a fs-36">修改密码
									</text>
								</view>
							</navigator>
						</view>
						<text class="gui-list-arrow-right gui-icons">&#xe601;</text>
					</view>
					<view class="gui-list-items gui-border-b">

						<img class="tc iconwh" src="../../static/images/signout.png" />

						<view class="gui-list-body " @click="out">
							<view class="gui-list-title ">
								<text class="text-a fs-36">退出登录</text>
							</view>
						</view>
						<text class="gui-list-arrow-right gui-icons">&#xe601;</text>
					</view>
				</view>
			</view>
			<!-- 引导授权手机号 -->
			<gui-modal ref="guimodal" title="退出提示">
				<view slot="content" class="gui-padding">
					<view class="pb-40">
						<view class="fs-36" style="text-align: center;">
							是否退出登录
						</view>
						<view class="gui-flex gui-align-items-center mt-20">
							<view class="mr-15 ">是否接收离线通知：</view>
							<radio-group class="gui-flex gui-rows" @change="radioChange" v-model="isCheck">
								<label class="gui-check-item gui-check-item-y">
									<radio style="transform:scale(0.8)"  value="1" :checked="isCheck == 1"></radio>
									<text class="gui-text gui-primary-color ml-15">是</text>
								</label>
								<label class="gui-check-item gui-check-item-y">
									<radio style="transform:scale(0.8)" value="2" :checked="isCheck == 2"></radio>
									<text class="gui-text gui-primary-color ml-15">否</text>
								</label>
							</radio-group>
						</view>
					</view>
				</view>
				<!-- 利用 flex 布局 可以放置多个自定义按钮哦  -->
				<view slot="btns" class="gui-flex gui-rows gui-space-between gui-border-t">
					<view class="modal-btns gui-flex1" @tap="close">
						<text class="modal-btns gui-color-gray" >取消</text>
					</view>
					<view class="modal-btns gui-flex1 gui-bg-blue" @tap="confirm">
						<text class="modal-btns gui-color-white" >确认</text>
					</view>
				</view>
			</gui-modal>
		</view>
	</view>
</template>
<script>
	import {
			mapMutations
		} from 'vuex';
	export default {
		data() {
			return {
				isCheck: 1, //是否勾选
				userInfo: uni.getStorageSync('mbuserInfo'),
				avatar: require("../../static/images/avatar-doctor.png"),
			}
		},
		methods: {
			...mapMutations([ 'closeSocket']),
			out : function () {
				this.$refs.guimodal.open();
			},
			close : function () {
				this.$refs.guimodal.close();
			},
			radioChange: function(e) {
				this.isCheck = e.detail.value;
			},
			confirm() {
				if (this.isCheck == 2) {
					this.$common.RequestData({
					  url: this.$common.logout,
					  data: {
						wxUser:uni.getStorageSync('mbwxUserId'),
					  },
					  method: "post"
					}, function(res) {
					})
				}
				const mbrememberpassword = uni.getStorageSync('mbrememberpassword')
				const mbusername = uni.getStorageSync('mbusername')
				uni.clearStorageSync()//清空缓存数据
				uni.setStorageSync("mbrememberpassword", mbrememberpassword);
				uni.setStorageSync("mbusername",mbusername);
				uni.setStorageSync("outMy",'outMy');
				uni.removeTabBarBadge({
					index: 1
				})
				this.outim()
				this.$common.navLaunch('/pages/login/index')
				// this.$refs.guimodal.close();
			},
			// 退出im
			outim(){
				try {
					this.$store.commit('closeSocket', this.imUser);
				} catch (e) {}
			}

		}
	}
</script>
<style scoped>
	page{
		background-color: #7585EA;
	}
	.modal-btns{line-height:88rpx; font-size:32rpx; text-align:center; width:200rpx;}
	.demo {
		width: 210rpx;
		height: 68rpx;
		line-height: 68rpx;
		text-align: center;
		margin: 10rpx;
	}

	.gui-columns {
		display: flex;
		flex-flow: column wrap;
	}

	.ucenter-face {
		width: 100rpx;
		height: 100rpx;
	}

	.ucenter-face-image {
		width: 100rpx;
		height: 100rpx;
	}

	.ucenter-line {
		height: 20rpx;
		background-color: #F6F7F8;
		margin: 16rpx 0;
	}

	.logoff {
		line-height: 88rpx;
		font-size: 28rpx;
	}

	.gui-list-title-text {
		line-height: 60rpx;
		font-size: 23rpx;
		font-weight: bold;
	}

	.iconwh {
		width: 7%;
		height: 7%;
	}

	.gui-list-arrow-right {
		color: #8ca1ef;
	}

	.gui-text-small {
		font-size: 32rpx;
		color: #8ca1ef;
	}

	.avat {
		width: 25%;
		height: 25%;
		border-radius: 50%;
	}

	.avatar {
		width: 100%;
		height: 100%;
		margin: auto 0;
	}

	.text-a {
		color: #727986;
	}
</style>
