<template>
	<view class="gui-padding">
		<view class="mt-40">
			<view class="gui-margin-top pb-15" v-if="formData.remark">
				<text class="ui-h6 gui-bold">医嘱备注：</text>
				<text class="fs-30">{{formData.remark || '无'}}</text>
			</view>
			<view>
				<view class="gui-flex gui-rows gui-space-between">
					<text class="gui-h6 gui-bold">上传舌面：</text>
				</view>
				<view class="gui-margin-top">
					<gui-upload-images @updaloding="updaloding" :progress="true" imgSize="1048576" :header="header" fileName="file"
						:uploadType="true" @uploaded="uploaded" @change="change" :btnName="btnName" :maxFileNumber="6"
						ref="uploadimgcom" :uploadServerUrl="uploadFuJianFile"></gui-upload-images>
					<!-- <gui-upload-images :progress="false" imgSize="8000000" errorText="大于8M" :header="header" fileName="file"
						@change="change" :btnName="btnName" :maxFileNumber="6" ref="uploadimgcom" @uploaded="uploaded"
						:uploadServerUrl="uploadFuJianFile"></gui-upload-images> -->
				</view>
				<view class="gui-form-item">
					<text class="gui-form-label gui-h6 gui-bold" style="width: 160rpx;">记录日期：</text>
					<view class="gui-form-body ">
						<gui-datetime @confirm="confirm4" :value="formData.measureTime" ref="timeBegin"
							:units="['年', '月', '日','时','分']" :isSecond='false'>
							<view class="gui-flex gui-rows gui-space-between fs-32">
								<text class="demo gui-icons">{{ formData.measureTime }}</text>
								<text class="gui-icons">&#xe601;</text>
							</view>
						</gui-datetime>
					</view>
				</view>
			</view>
			<view class="gui-margin-top">
				<text class="gui-h6 gui-bold">描 述：</text>
			</view>
			<view class="gui-bg-gray gui-margin-top">
				<textarea v-model="formData.lingualRemark" class="gui-text-area" style="font-size: 30rpx;"
					placeholder="请输入内容" />
			</view>
			<view v-if="itemlist.length > 0"
				class="bg-white w-100 pos-fixed box-size-border box-shadows py-20 px-40 gui-border-radius-small gui-flex gui-justify-content-center"
				style="height: 130rpx; bottom: 70rpx;left: 0;z-index: 9;">
				<!-- <view v-if="reSubmiting" @click="openShexiang(imgUrl)" class="gui-bg-blue text-white w-50 h-100 d-flex ai-center jc-center mr-20">开始 AI 舌象</view> -->
				<view v-if="reSubmiting" @click="reSubmit(2)"
					class="zhuti-bnt-bg text-white w-50 h-100 d-flex ai-center jc-center ml-20">{{reSubmitTitle}}</view>
			</view>
		</view>
		<gui-modal ref="guimodal1" closeBtnStyle="display:none;" :canCloseByShade="false" title="请选择要进行辨识的图片"
			title-style="font-size:20px;padding:20px; font-weight:600;">
			<view slot="content" class="gui-padding gui-bg-gray">
				<view style="padding: 15px;" class="gui-flex gui-wrap">
					<radio-group class="gui-flex gui-wrap" @change="radioChange">
						<label class="uni-list-cell uni-list-cell-pd" v-for="(item,index) in imgUrl" :key="index">
							<view class="mr-20 mb-10 gui-flex gui-columns gui-align-items-center"
								style="width: 200rpx;">
								<image :src="item.url" mode="" style="width: 200rpx;height: 200rpx;"></image>
								<radio class="mt-20" :value="item" />
							</view>
						</label>
					</radio-group>
				</view>
			</view>
			<!-- 利用 flex 布局 可以放置多个自定义按钮哦  -->
			<view slot="btns" class="gui-flex gui-rows gui-space-between">
				<view class="modal-btns gui-flex1" @tap="close1">
					<text class="modal-btns gui-color-gray" style="font-size: 18px;">取消</text>
				</view>
				<view class="modal-btns gui-flex1 gui-bg-blue" @tap="confirmModal">
					<text class="modal-btns  gui-color-white" style="font-size: 18px;">开始AI辨舌</text>
				</view>
			</view>
		</gui-modal>
	</view>
</template>
<script>
	import {
		addTaskPerform,
		getFollowTaskInfo,
		editTaskPerform,
		getPerform
	} from '@/api/patient.js'
	import utils from '@/tools/utils/utils.js'
	import {
		data
	} from '../../static/js/icon';
	export default {
		data() {
			return {
				id: '',
				taskType: '3',
				// 表单数据存储
				formData: {
					// 记录需要上传的图片数据
					lingualFiles: '',
					remark: '',
					lingualRemark: '',
					measureTime: this.$common.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}'),
				},
				// 原数组
				imgUrl: [],
				uploadFuJianFile: this.$common.commentImage,
				btnName: '',
				header: {
					"Authorization": 'Bearer ' + uni.getStorageSync('token')
				},
				patientId: '',
				itemlist: [], //上一个界面传过来的图片数据
				reSubmiting: true, //防重复提交
				reSubmitTitle: '提交完成',
				confirmImg: '',
				imgLists: [],
				updatting:true,//图片上传状态
			}
		},
		onLoad(options) {
			if (options) {
				this.patientId = options.patientId
				let baseFile = uni.getStorageSync('baseFile') ? uni.getStorageSync('baseFile') : null
				if (options.id) {
					getPerform(options.id).then(res => {
						this.formData = {
							id: res.data.id,
							remark: res.data.remark,
							measureTime: res.data.measureTime,
							lingualRemark: res.data.lingualRemark
						};
						let arr = res.data.lingualFiles.split(',')
						arr.map(item => {
							this.itemlist.push({
								url: String(item),
								progress: 0,
								name: '',
								baseFile: baseFile
							})
						})
						this.$refs.uploadimgcom.setItems(this.itemlist)
						this.reSubmitTitle = '提交修改'
					})
				} else {
					this.itemlist.push({
						url: String(options.tempFilePaths),
						progress: 0,
						name: options.name ? options.name : '',
						baseFile: baseFile
					})
				}
				if (baseFile) {
					uni.removeStorageSync('baseFile')
				}
			}
		},
		onReady() {
			this.init()
		},
		methods: {
			init() {
				if (this.itemlist.length > 0) {
					this.$refs.uploadimgcom.setItemsNo(this.itemlist)
				}
			},
			confirm4(res) {
				this.formData.measureTime = `${res[0]}-${res[1]}-${res[2]} ${res[3]}:${res[4]}`
				this.$forceUpdate()
			},
			clickImg(item) {
				wx.previewImage({
					urls: [item.img], //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
					current: '', // 当前显示图片的http链接，默认是第一个
					success: function(res) {},
					fail: function(res) {},
					complete: function(res) {},
				})
			},
			openShexiang() {
				// this.imgList = item.lingualFiles.split(",")
				// this.confirmDta = item;
				console.log('图片内容==', this.imgUrl)
				this.$refs.guimodal1.open();
			},
			radioChange(e) {
				this.confirmImg = e.detail.value
				console.log('选择的图片==', this.confirmImg)
			},
			close1: function() {
				this.$refs.guimodal1.close();
			},
			updaloding(e){//检查图片上传状态
				this.updatting = e;
			},
			uploaded(e) {//记录已经上传的图片
				this.imgLists = e;
			},
			// 记录选择图片时的待提交数据
			change: function(e) {
				// 数组类型
				this.imgUrl = e
				// let key = this.imgUrl.findIndex(imgdata => imgdata === e.url)
			},
			blobToFile(blob, fileName) {
				const file = new File([blob], fileName, {
					type: blob.type
				})
				return file
			},
			//预提交-需要先上传
			reSubmit(type) { //1是AI舌诊，2是其他
				// if (this.reSubmitTitle == '提交修改') {
				// 	console.log('提交修改', this.imgUrl)
				// 	let arrimg = [];
				// 	this.imgUrl.forEach((item, index) => {
				// 		if (item.progress == 100) {
				// 			arrimg.push(item.url)
				// 		}
				// 	})
				// 	setTimeout(() => {
				// 		if (arrimg.length) {
				// 			let arrr = arrimg.join(',')
				// 			this.submit(arrr, type) //提交表单
				// 		}
				// 	}, 500)
				// };
				if (this.imgUrl.length <= 0 || this.imgLists.length <= 0) {return}
				if(!this.updatting){
					// 先取出 url地址 赋值给arrs
					let arrs = [];
					this.imgLists.forEach((item, index) => {
						if (item.progress == 100) {
							arrs.push(item.url)
						}
					})
					setTimeout(() => {
						if (arrs.length) {
							let arr = arrs.join(',')
							this.submit(arr, type) //提交表单
						}
					}, 500)
				}
				return
				let that = this
				let c = 0;
				let tempFile = null;
				let blob = null;
				for (let key in that.imgUrl) {
					// console.log('key===',this.imgUrl[key].baseFile)
					if (that.imgUrl[key].baseFile) {
						blob = utils.base64ToBlob(that.imgUrl[key].baseFile)
						// console.log('进来图片压缩====',blob)
						tempFile = that.blobToFile(blob, that.imgUrl[key].name)
						// console.log('图片压缩后上传为文件格式==',tempFile)
						this.$common.H5uploadFile(tempFile, resx => {
							// console.log('图片压缩后上传为文件格式==',resx)
							c += 1; //上传成功一个+1
							if (c == that.imgUrl.length) {
								//所有图片已经传完
								arr += resx.data.url;
								that.submit(arr) //提交表单
							} else {
								arr += resx.data.url + ',';
							}
						});
					} else {
						console.log('未压缩的图片上传==')
						this.$common.uploadFile(this.imgUrl[key].url, resx => {
							c += 1; //上传成功一个+1
							if (c == that.imgUrl.length) {
								//所有图片已经传完
								arr += resx.data.url;
								that.submit(arr) //提交表单
							} else {
								arr += resx.data.url + ',';
							}
						},'上传中...');
					}

				}
			},
			// 提交动态
			submit(arr, type) {
				if (this.reSubmiting) {
					this.reSubmiting = false;
					this.formData.lingualFiles = arr;
					var obj = {
						...this.formData,
						measureTime: this.formData.measureTime + ':00',
						monitorId: 7,
						monitorName: '舌面像',
						patientId: this.patientId, // 就诊卡用户id
					}
					if (type) {
						obj.aiFlag = type ? '1' : '0'; //是否进行AI舌诊 1是AI舌诊
						obj.imgIndex = 0; //进行AI舌诊的图片下标，0开始
					}
					uni.showLoading({
						title: "请求中..."
					})
					if (this.reSubmitTitle == '提交修改') {
						editTaskPerform(obj).then(res => {
							if (res.code == 200) {
								// this.$common.navCloseTo('/pages/patient/details?patientId=' + this.patientId+'&monitorId=7')
								this.$common.navCloseTo('/pages/patient/details')
								uni.setStorageSync('mbdetailsPatientId', this.patientId)
								uni.setStorageSync('mbmonitorId', 7)
								uni.hideLoading();
							} else {
								this.$common.msg("上传失败，请重试")
								this.reSubmiting = true;
								uni.hideLoading();
							}
						}, fail => {
							uni.hideLoading();
							this.reSubmiting = true;
						})
					} else {
						addTaskPerform(obj).then(res => {
							if (res.code == 200) {
								// this.$common.navCloseTo('/pages/patient/details?patientId=' + this.patientId+'&monitorId=7')
								this.$common.navCloseTo('/pages/patient/details')
								uni.setStorageSync('mbdetailsPatientId', this.patientId)
								uni.setStorageSync('mbmonitorId', 7)
								uni.hideLoading();
							} else {
								this.$common.msg("上传失败，请重试")
								this.reSubmiting = true;
								uni.hideLoading();
							}
						}, fail => {
							uni.hideLoading();
							this.reSubmiting = true;
						})
					}

				}
			}
		}
	}
</script>
<style>
	.logo {
		height: 350rpx;
		width: 300rpx;
	}

	.gui-text-area {
		font-size: 26rpx;
		color: #2B2E3D;
		height: 150rpx;
		padding: 20rpx;
	}

	.demo-sub-btn {
		height: 100rpx;
		line-height: 100rpx;
		width: 320rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 10rpx;
		position: absolute;
		bottom: 60rpx;
		right: 40rpx;
	}

	.modal-btns {
		line-height: 88rpx;
		font-size: 26rpx;
		text-align: center;
		width: 200rpx;
	}
</style>
