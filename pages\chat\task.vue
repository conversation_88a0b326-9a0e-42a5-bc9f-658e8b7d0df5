<template>
	<gui-page :fullPage="true" :isLoading="pageLoading" ref="guiPage">
		<view slot="gBody" class="gui-flex1">
			<!-- 搜索框 -->
			<view class="header-search gui-bg-gray gui-border-box">
				<gui-search @clear="searchClear" @confirm="search" :placeholder="'请输入文章名称查询'"></gui-search>
			</view>
			<!-- 主体区域 -->
			<view class="gui-flex gui-rows gui-space-between" v-if="mainCate.length > 0">
				<!-- 左侧分类列表 -->
				<scroll-view :scroll-y="true" :show-scrollbar="false" 
				:scroll-with-animation="true" :scroll-into-view="leftTo" 
				class="gui-cate-left" :style="{height:mainHeight+'px'}">
					<view v-if="taskStatus==1" class="gui-cate-left-item gui-border-box" 
					v-for="(item, index) in mainCate" :key="index" 
					:class="[currentCateIndex == item.dictValue ? 'gui-cate-left-current' : '']" 
					>
						<text class="gui-border-l" 
						:class="['gui-cate-left-item-title','gui-block-text', currentCateIndex == item.dictValue ? 'gui-cate-left-current' : '']" 
						@tap="changCate(item.dictValue)">{{item.dictLabel}}</text>
					</view>
					<view v-if="taskStatus==3" class="gui-cate-left-item gui-border-box"
					v-for="(item, index) in mainCate" :key="index" 
					:class="[currentCateIndex == item.id ? 'gui-cate-left-current' : '']" 
					>
						<text class="gui-border-l" 
						:class="['gui-cate-left-item-title','gui-block-text', currentCateIndex == item.id ? 'gui-cate-left-current' : '']" 
						@tap="changCate(item.id)">{{item.channelName||'456'}}</text>
					</view>
				</scroll-view>
				<!-- 右侧列表 -->
				<scroll-view 
				:scroll-into-view="productListTo" :show-scrollbar="false" 
				:scroll-with-animation="true" :scroll-y="true" 
				class="gui-cate-right" 
				:style="{height:mainHeight+'px'}">
					
					<!-- 循环展示分类及分类对应的产品列表@click="productsClick(item.id)" -->
					<view class="gui-margin-top"  v-if="products.length > 0"
					v-for="(item, index) in products" :key="index" >
						<!-- 循环展示产品 -->
						<view class="gui-flex gui-rows gui-nowrap gui-cate-product-list gui-border-b" >
							<view class="gui-cate-pbody gui-flex gui-rows gui-space-between gui-align-items-center">
								<view class="gui-text gui-primary-color gui-block-text ellipsis-2">{{taskStatus==1?item.questionName:item.articleName}}</view>
								<!-- <view class="gui-text gui-primary-color gui-block-text ellipsis-2">{{item.articleName}}</view> -->
								<view class="gui-flex pr-40">
									<view v-if="productArr.find((i) => i.id == item.id)" class="" @click="delProduct(item)">
										<text style="font-size: 46rpx;" class=" gui-icons gui-color-gray">&#xe636;</text>
									</view>
									<view v-else="productArr.length == 0 " class="gui-bg-zdy gui-flex gui-justify-content-center gui-align-items-center p-10" style="border-radius: 50rpx;height: 40rpx;width: 40rpx;" @click="addProduct(item)">
										<text class=" gui-icons fs-24 gui-color-white">&#xe6c7;</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view v-if="products.length <= 0">
						<gui-empty>
							<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
								<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
							</view>
							<text slot="text" 
							class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
						</gui-empty>
					</view>
				</scroll-view>
			</view>
			<!-- 兑换记录 -->
			<view class="record">
				<view class="gui-flex gui-rows w-100 h-100 gui-align-items-center">
					<view class="pl-90 w-40">
						<view style="position: relative;z-index: 9999999;" @click="popupOpen">
							<text v-if="productArr.length>0" class="gui-badge gui-bg-red gui-color-white" style="z-index: 9999999;position: absolute; left: 55rpx; top: -5rpx; z-index: 1;">{{productArr.length}}</text>
							<text style="z-index: 9999999;" class=" gui-icons fs-60 gui-color-orange">&#xe60a;</text>
						</view>
					</view>
					<view class="gui-flex gui-rows gui-justify-content-center gui-align-items-center w-60 h-100 gui-bg-zdy  fs-30" style="border-radius: 80rpx;">
						<!-- <view class="gui-color-white" @click="todetail()">预约记录</view>
						<view class="gui-color-gray px-20">|</view> -->
						<view class="gui-color-white" @click="toshop()">确定发送</view>
					</view>
				</view>
			</view>
			<view v-if="mainCate.length <= 0">
				<gui-empty>
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
					</view>
					<text slot="text" 
					class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#B2B2B2;">暂无数据</text>
				</gui-empty>
			</view>
			<!-- 底部弹出 -->
			<gui-popup ref="guipopup" position="bottom" style="z-index: 88;">
				<view class="gui-relative gui-box-shadow gui-bg-gray pt-60 px-100" style="width: 100%; min-height: 100rpx; border-top-left-radius: 40rpx !important;border-top-right-radius: 40rpx !important;">
					<view class="mt-20" v-for="item in productArr">
						<view class="mb-20 pb-20 gui-flex gui-space-between gui-align-items-center" style="border-bottom: 1px solid #e6e6e6;">
							<view class="fs-30 ellipsis-2">{{taskStatus==1?item.questionName:item.articleName}}</view>
							<view class="" @click="delProduct(item)">
								<text class=" gui-icons fs-36 gui-color-gray">&#xe636;</text>
							</view>
						</view>
					</view>
					<view style="height: 160rpx;"></view>
					<!-- iphone 底部操作按钮躲避 -->
					<gui-iphone-bottom></gui-iphone-bottom>
					<!-- 关闭按钮 -->
					<text class="gui-block-text demo-close gui-icons gui-color-orange gui-absolute-rt" style="top: 8rpx; right: 20rpx;"
					@click="popupClose" >&#xe78a;</text>
				</view>
			</gui-popup>
		</view>
	</gui-page>
</template>
<script>
	import { setTask } from '@/api/patient.js'
var graceJS = require('@/GraceUI5/js/grace.js');
var cateChangeData = require('@/GraceUI5/demoData/cateChange.js');
	import { missionaryType,questionnaireType,mbArticleList,questionnaireLists,sandTask,getTaskIdsByBatchId,imFlush,imSendMsg,channelList  } from '@/api/im.js'
export default {
	data() {
		return {
			img : "https://images.unsplash.com/photo-1660505465468-c898ea7ff674?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHx0b3BpYy1mZWVkfDQ2fHhqUFI0aGxrQkdBfHxlbnwwfHx8fA%3D%3D&auto=format&fit=crop&w=200&q=90",
			// 全屏加载，当数据请求完毕时关闭
			pageLoading : true,
			// 核心区域高度
			mainHeight  : 300,
			// 左侧分类定位
			leftTo      : 'cate',
			// 标识切换时左侧点击触发的
			isLeftTap   : false,
			// 左侧分类数据
			mainCate    : [],// 当前分类
			currentCateIndex : 0,
			// 延迟执行防止卡顿
			scrollTimer : null,
			// 产品列表滚动定位
			productListTo: '',
			products:[],
			productArr:[],
			taskStatus:1,//类型：1：问卷 3：宣教
			batchId:null,
			groupIndex:null,
			patientId:'',
		}
	},
	onLoad:function(options){
		this.groupIndex = options.groupIndex?options.groupIndex :'';
		
		this.patientId = options.patientId//患者详情-下达干预
		// 获取页面主体高度从而得出主体高度
		graceJS.getRefs('guiPage', this, 0, (ref)=>{
			ref.getDomSize('guiPageBody', (e)=>{
				// 02. 导航的高度
				// 可以 使用 graceJS.select() 函数动态获取， 也可以使用 uni.upx2px 转换已知高度
				this.mainHeight  = e.height-150;
				setTimeout(()=>{
					this.pageLoading = false;
				},1000)
			});
		});
	},
	beforeDestroy() {
	  // 销毁定时器
	  if(this.intervalItem) {
	    clearInterval(this.intervalItem)
	  }
	},
	onShow() {
		this.$set(this,'taskStatus',uni.getStorageSync('mbtaskStatus'))
		this.getListType(this.taskStatus)
		console.log('分类的类型==',this.taskStatus)
	},
	methods: {
		search(e){//搜索
			// console.log('搜索内容==',e)
			this.getList(this.currentCateIndex,this.taskStatus,e)
		},
		searchClear(e){
			this.getList(this.currentCateIndex,this.taskStatus,e)
		},
		//底部弹出模式
		popupOpen(){
			if (this.productArr.length <= 0) {return this.$common.msg('请选择发送内容')}
			this.$refs.guipopup.open();
		},
		popupClose(){this.$refs.guipopup.close();},
		addProduct(value,key){//添加项目
			if (this.productArr.length > 0) {
				const map = new Map();
				this.productArr.push(value)
				for (let item of this.productArr) {
					//has方法可以判断Map对象中是否存在指定元素,有则返回true，否则返回false
					//set方法可以向Map对象添加新元素 map.set(key, value)
					//values方法可以返回Map对象值的遍历器对象
				    if (!map.has(item.id)) {
				        map.set(item.id, item);
				    }else{
						this.$common.msg(item.name+'发送内容已存在不可重复添加')
					}
				};
				this.productArr = [...map.values()];
			} else {
				this.productArr.push(value)
			}
		},
		delProduct(val,key){//删减项目
			this.productArr.forEach((item,index,array)=>{
			//item为遍历的当前元素，index为当前索引，array为正在操作的数组
			  if(item.id == val.id){
				array.splice(index,1)
			  }
			});
			if (this.productArr.length <= 0) {
				this.popupClose()
			}
		},
		todetail(){
			// this.$common.navTo("/pages/homeAc/treatment/record")
		},
		toshop(){
			if (this.productArr.length <= 0) {
				return this.$common.msg("请先发送的内容")
			} 
			this.pageLoading =true;
			var that = this
			console.log('选择的数据===',that.productArr)
			var articleList = that.productArr.map(item=>{
			  var copy = {id: item.id, articleName: item.articleName, questionName: item.questionName}
			  return copy
			})
			console.log('选择的数据=处理',articleList)
			var sandObj = {
				sendTime:"",//发送时间
				sendType:1,//推送方式1、立即推送，2指定日期, 3不推送
				infoType:1,//干预种类1.患者列表干预, 2.随访列表干预, 3.自动推送,4防疫干预,5监测干预,6康养干预[治疗干预],
				taskType:this.taskStatus==1?1:2,//干预类型 1问卷，2宣教，
				pathStatus:"1",//传1就行
				programmeType:"1",//干预方案类型 1.干预方案 2.路径方案中的干预方案
				programmeTypeId:"",//方案类型id
				downEntry:uni.getStorageSync('mbtasktype')?'1':'',//下达入口(1,患者随访,2,诊后计划)
				articleInfos:articleList,//宣教集合
				questionnaires:articleList,//问卷集合
				followInfos:[{
					memberId:uni.getStorageSync('mbhuanzInfo').patientId?null:uni.getStorageSync('mbhuanzInfo').memberId,
					patientId:this.patientId?this.patientId:uni.getStorageSync('mbhuanzInfo').patientId
				}],//患者信息对象
			}
			console.log('发送数据====',sandObj)
			if (uni.getStorageSync('mbtasktype')) {//患者详情-下达干预问卷、宣教
				setTask(sandObj).then(res =>{
					if (res.code == 200) {
						that.productArr = [];
						that.$common.navBack(1)
					} else{
						that.pageLoading =false;
						that.$common.msg('发送失败，请重新选择！')
					}
						that.pageLoading =false;
					
				})
			} else{//在线咨询部分的问卷、宣教
				sandTask(sandObj).then(res =>{
					if (res.code == 200) {
						that.batchId = res.data
						this.intervalItem = setInterval(function() {
						  that.check(res.data)
						}, 1500);
					} else{
						that.pageLoading =false;
						that.$common.msg('发送失败，请重新选择！')
					}
				})
			}
		
		},
		check(e) {
		  const that = this;
		  if (this.doCheck == true) {return false;} // 防止重发查询
		  this.doCheck = true
		  getTaskIdsByBatchId({batchId: e?e.batchId:that.batchId.batchId}).then(res=>{
		    this.doCheck = false
		    if (res.data && res.data.length>=this.productArr.length) {
				console.log('res.data.length',res.data)
				clearInterval(that.intervalItem)
				that.intervalItem = null
				res.data.map(item=>{
				  imSendMsg({
					receiverType: '1', //接收方类型 1医生，2患者 -用于离线消息推送判断
				  	senderUid:uni.getStorageSync('mbhuanzInfo').userUid,
				  	receiverUid:uni.getStorageSync('mbhuanzInfo').friendUid,
					memberId:uni.getStorageSync('mbhuanzInfo').memberId,
				  	groupIndex:this.groupIndex,
				  	contentType:'task',
					type:'msg',
				  	content:JSON.stringify(item)
				  }).then(res =>{
					  if (res.code == 200) {
					  	// imFlush(res.data).then(res =>{})
					  } else{
						that.pageLoading =false;
					  	that.$common.msg('发送失败')
						return
					  }
				  })
				})
				setTimeout(function() {
					that.$common.navBack(1)
					that.productArr = [];
					uni.removeStorageSync('mbtaskStatus')
					that.pageLoading =false;
				}, 100);
		      // that.loadingFun.close();
		      // that.msgSuccess('添加任务成功')
		      // that.$emit('closes', 'yes')
		      // that.queInit()
		    }
		  })
		},
		getListType(type){
			// console.log('分类的类型==',type)
			if (type==1) {//1：问卷 3：宣教
				questionnaireType().then(res => {
					if (res.data.length >0) {
						// this.mainCate = [{dictLabel:"全部",dictCode:0}].concat(res.data);
						this.mainCate = res.data;
						this.currentCateIndex = res.data[0].dictValue;
						this.getList(res.data[0].dictValue,type)
					}
				})
			} else{
				channelList({//position：0宣教 1科普
					position:0
				}).then(res => {
					// console.log('宣教分类==',res.data)
					if (res.data.length >0) {
						this.mainCate = res.data;
						this.currentCateIndex = res.data[0].id;
						this.getList(res.data[0].id,type)
					}
				})
			}
		},
		getList(e,type,name){
			if (type == 1) {
				questionnaireLists({questionType:e?e:this.currentCateIndex,status:type,questionName:name}).then(res =>{
					this.products = res.rows
				})
			} else{
				mbArticleList({missionaryClass:e?e:this.currentCateIndex,status:type,articleName:name}).then(res =>{
					this.products = res.rows
				})
			}
		},
		changCate : function (e) {
			var cateid = e
			console.log('选择===',e)
			this.currentCateIndex = cateid;
			this.getList(this.currentCateIndex,this.taskStatus);
		}
	}
}
</script>
<style scoped>
	.record{
		z-index: 999999;
		position: absolute;
		bottom: 80rpx;
		background-color: #eeeeee;
		border-radius: 80rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		width: 680rpx;
		height: 80rpx;
		right:40rpx
	}
.header-search{padding:20rpx 80rpx; width:750rpx;}
.gui-cate-left{width:200rpx; background-color:#F6F7F8;}
.gui-cate-right{width:520rpx; overflow:hidden;}
.gui-cate-left-item{height:100rpx; padding:35rpx 0; font-size:26rpx;}
.gui-cate-left-item-title{line-height:32rpx; font-size:28rpx; border-color:#F6F7F8; border-left-width:8rpx; text-align:center;}
.gui-cate-left-current{border-color:#7784eb !important; background-color:#FFFFFF; color:#7784eb; font-weight:bold;}
.gui-cate-right-title{line-height:80rpx;}
.gui-cate-product-list{padding-bottom:30rpx;padding-right:25rpx;}
.gui-cate-pimg{width:180rpx;}
.gui-cate-pbody{
	margin-left:30rpx; width:100rpx; flex:1; padding-top:-20rpx;
}
.gui-block-text{
	font-size: 34rpx;
}
.gui-cate-price{font-size:32rpx; line-height:60rpx;}
.demo-close{width:70rpx; height:70rpx; line-height:70rpx; opacity:0.88; text-align:center; font-size:50rpx;}
</style>
