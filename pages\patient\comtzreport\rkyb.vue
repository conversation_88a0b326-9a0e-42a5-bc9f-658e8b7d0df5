<template>
	<!-- 中医饮食指导（儿科一般指导） -->
	<view class="fs-32">
		<view class="my-20 fs-32">
			<view class="d-flex mb-20">
				<view>姓名：{{info.name || '-'}}</view>
				<view class="ml-120">性别：{{info.sex == 1 ? "男" : "女"}}</view>
			</view>
			<view>时间：{{obj.create_time ? $common.parseTime(obj.create_time) : '-'}}</view>
		</view>
		<view class="d-flex">
			<view class="tle">指导前</view>
			<view class="gui-table gui-border-l gui-border-t" style="width: 92%;">
				<view class="gui-tbody gui-flex gui-rows gui-nowrap"v-for="(item,index) in obj.guid_before_json" :key="'a'+index">
					<text class="gui-td gui-td-text gui-text-center gui-border-r gui-border-b">{{item.beforeTime || '-'}}</text>
					<text class="gui-td gui-td-text gui-border-r gui-border-b">{{item.beforeText || '无'}}</text>
				</view>
			</view>
		</view>
		<view class="d-flex">
			<view class="tle">指导后</view>
			<view class="gui-table gui-border-l gui-border-t" style="width: 92%;">
				<view class="gui-tbody gui-flex gui-rows gui-nowrap"v-for="(item,index) in obj.guid_after_json" :key="'a'+index">
					<text class="gui-td gui-td-text gui-text-center gui-border-r gui-border-b">{{item.afterTime || '-'}}</text>
					<text class="gui-td gui-td-text gui-border-r gui-border-b">{{item.afterText || '无'}}</text>
				</view>
			</view>
		</view>
		<view class="d-flex">
			<view class="tle">饮食指导</view>
			<view class="gui-table gui-border-l gui-border-t" style="width: 92%;">
				<view class="gui-tbody gui-flex gui-rows gui-nowrap">
					<view v-html="obj.guid_ys" style="width: 100%;"></view>
					<!-- <text class="gui-td gui-td-text gui-border-r gui-border-b">{{obj.guid_ys || '无'}}</text> -->
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// import {getInfoMedical} from '@/api/home.js'
	export default {
	  name: 'ysjy',
		props:['templateId','visitRecordId','templateDictKey','info','patientId'],
		data(){
			return{
				cardObj:uni.getStorageSync('mbcardObj'),
				obj:{}
			}
		},
		watch:{
			// templateDictKey(news,olds){
			// 	if( news == 26 || news == 29 || news == 30){
			// 		this.getDetail();
			// 	}
			// }
			templateDictKey:{
				handler (newLength, oldLength) {
					if( newLength == 26 || newLength == 29 || newLength == 30 ){
						this.getDetail();
					}
				},
				immediate: true
			}
		},
		methods:{
			// 获取建议详情
			getDetail(){
				let i = Number(this.templateDictKey==26?1:this.templateDictKey==29?2:this.templateDictKey==30?3:1)
				this.$common.RequestData({
					url: this.$common.getInfoMedical,
					data:{
						templateId:this.templateId,
						templateKey: Number(this.templateDictKey),
						type:i
					},
					method: "post"
				}, res => {
					res.data.guid_before_json = res.data.guid_before_json ? JSON.parse(res.data.guid_before_json) : [];
					res.data.guid_after_json = res.data.guid_after_json ? JSON.parse(res.data.guid_after_json) : [];
					res.data.guid_ys = res.data.guid_ys.replace(/<table([\s\w"-=\/\.:;]+)/ig, '<table$1 style="border-top: 1px solid #DDD;border-left: 1px solid #DDD;"')
										.replace(/<td([\s\w"-=\/\.:;]+)/ig, '<td$1 class="td"')
										.replace(/<td\s*/ig, '<td style="border-bottom: 1px solid #DDD;border-right: 1px solid #DDD;"')
										.replace(/<table\s*/ig, '<table style="border-top: 1px solid #DDD;border-left: 1px solid #DDD;"')
										.replace(/<th\s*/ig, '<th style="border-bottom: 1px solid #DDD;border-right: 1px solid #DDD;"')
										.replace(/<tbody\s*/ig, '<tbody class="tbody" ')
					this.obj = res.data;
				},true,fail=>{

				})
				// let i = Number(this.templateDictKey==26?1:this.templateDictKey==29?2:this.templateDictKey==30?3:1)
				// getInfoMedical({
				// 	templateId:this.templateId,
				// 	templateKey: Number(this.templateDictKey),
				// 	type:i
				// }).then(res=>{
				// 	res.data.guid_before_json = res.data.guid_before_json ? JSON.parse(res.data.guid_before_json) : [];
				// 	res.data.guid_after_json = res.data.guid_after_json ? JSON.parse(res.data.guid_after_json) : [];
				// 	this.obj = res.data;
				// })
			}
		}
	}
</script>

<style>
	.tle {
		width: 10%;
		border: 1px solid #f2f3f4;
		text-align: center;
		writing-mode: vertical-lr;/* 从左向右 从右向左是 writing-mode: vertical-rl;*/
		writing-mode: tb-lr;/*IE浏览器的从左向右 从右向左是 writing-mode: tb-rl；*/
		letter-spacing: 10rpx;
		line-height: 70rpx;
		/* font-size: 28rpx; */
	}

	.gui-td {
		width: 100rpx;
		flex: 1;
		overflow: hidden;
		padding: 20rpx 10rpx;
		display: flexbox;
	}

	.gui-td-text {
		line-height: 40rpx !important;
		/* font-size: 24rpx; */
	}
</style>
