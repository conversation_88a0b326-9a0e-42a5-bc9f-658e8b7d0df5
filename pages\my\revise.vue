<template>
	<view class="px-40  py-20">
	<form>

				<view  style="line-height: 100rpx;" class="gui-form-item  gui-flex gui-align-items-center gui-justify-content-center mt-20" >

					<text class="fs-30 mt-20">	<text  class="gui-color-red">&lowast;</text>旧密码：</text>
					<view class="gui-form-body">
						<input type="text" class="gui-form-input"
						v-model="oldPassword" name="oldPassword" placeholder="请输入旧密码" />
					</view>
				</view>
				<view class="gui-form-item  mt-60">
					<text class="fs-30"><text class="gui-color-red">&lowast;</text>新密码：</text>
					<view class="gui-form-body">
						<input type="text" class="gui-form-input"
						v-model="newPassword" name="newPassword" placeholder="请输入新密码" />
					</view>
				</view>

				<view class="gui-form-item mt-60">

					<text class="gui-form-label"><text class="gui-color-red">&lowast;</text>确认新密码：</text>
					<view class="gui-form-body">
						<input type="text" class="gui-form-input"
						v-model="againPassword" name="againPassword" placeholder="请再次确认新密码" />
					</view>
				</view>
				<view class="gui-flex gui-justify-content-center mt-80 ">
					<button type="default"  @tap="index()" class=" gui-flex gui-wrap gui-justify-content-center gui-button gui-bg-blue gui-noborder">
						<text  class="gui-color-white gui-button-tex fs-36">保存</text>
					</button>
				</view>


				</form>
				</view>
</template>

<script>
	export default {
		data() {
			return {
				oldPassword:"",
				newPassword:"",
				againPassword:'',

			}
		},
		methods: {
			// // 监听输入框输入事件
			// inputting : function (e) {
			// 	var name2Val = e.detail.value;
			// 	console.log(name2Val);
			// },
			index(){
				var str = /^(?=.*[0-9])(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[!#@*&.])(?=.*[^a-zA-Z0-9])/;
				console.log('密码校验==',str.test(this.newPassword))
				if(!this.oldPassword){return this.$common.msg("请输入旧密码")}
				if(!this.newPassword){return this.$common.msg("请输入新密码")}
				if (this.newPassword.length<8) {return this.$common.msg("密码长度需至少为8位")}
				if (!str.test(this.newPassword)) {return this.$common.msg("请输入密码包含有数字、小写字母、大写字母、特殊字符4种")}
				if(!this.againPassword){return this.$common.msg("请确认新密码")}
				if(this.againPassword != this.newPassword){return this.$common.msg("请确认两次新密码输入一致")}
				this.$common.RequestData({
					url:this.$common.updatePwd +'?oldPassword=' + this.oldPassword + '&newPassword=' + this.newPassword,
					data:{},
					method:"post"
				},res=>{
					if (res.code == 200) {
						this.$common.msg("修改成功","success")
						setTimeout(()=>{
							uni.clearStorageSync();
							this.$common.navLaunch("/pages/login/index")
						},1000)
					}
					
				})
			}
		}
	}
</script>

<style>
.gui-form-item{height: 80rpx; flex-direction:row; flex-wrap:nowrap; align-items:center;} // 表单项目
.gui-form-label{width: 180rpx; height:100rpx; font-size:28rpx; line-height:100rpx; } // 标题
.gui-form-input{height:50px; border:2rpx solid black;border-radius: 15rpx;} // 单行输入
.gui-button{width:200px;  height:50px; line-height:50rpx; border-radius:20rpx;align-content: space-around;}
/* 修改密码 */
input{
	padding-left: 20rpx;
	/* width: 270px; */
}
>>>.gui-form-body{
	margin-left: unset;
}
</style>
