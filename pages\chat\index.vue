<template>
	<view class="gui-relative">
		<view class="file" @click="openGuipopup()">咨询码</view>
		<view class="w-100 gui-bg-white" style="position: absolute;top: 0rpx; z-index: 9;">
			<!-- 头部信息 -->
			<view class="gui-flex mt-20 pb-10 px-40 fs-36 gui-space-around" >
				<view class="pb-5 px-10 gui-flex gui-columns gui-align-items-center" @click="changeData(1)" :class="changeDataType==1?'changeDataIon':''">
					<view class="">未回复</view>
					<view :class="changeDataType==1?'nav-active-line':'nav-active-line-i'"></view>
				</view>
				<view class="pb-5 px-10 gui-flex gui-columns gui-align-items-center" @click="changeData(2)" :class="changeDataType==2?'changeDataIon':''">
					<view class="">已回复</view>
					<view :class="changeDataType==2?'nav-active-line':'nav-active-line-i'"></view>
				</view>
			</view>
			<view class="mt-20 search-warp gui-border-box  gui-flex gui-space-between gui-align-items-center px-20 pb-20" style="border-bottom: 1rpx solid #e1e0e0;">
				<gui-search placeholder="姓名搜索" @inputting="inputting" @confirm="inputConfirm()" :kwd="patientName"
					class="l-border" @clear="clearinit"></gui-search>
				<text @tap="inputConfirm"
					class="l-button gui-padding gui-border-radius-large" slot="default">搜 索</text>
			</view>
		</view>
		<view class="pt-200">
			<scroll-view scroll-y="true" style="height: 85vh;">
				<view class="cu-list menu-avatar mt-10">
					<view class="cu-item" @click="toConsult(item)" v-for="(item,index) in sessionList" :key="index">
						<view class="gui-relative">
							<image v-if="item.uface && item.uface.indexOf('thirdwx.qlogo') != -1"
								:src="'../../static/images/wx.png'" style="width: 100rpx; height: 100rpx;left: -620rpx;"
								mode=""></image>
							<image v-else @error="imageError" :src="item.uface?item.uface:'../../static/images/wx.png'"
								style="width: 100rpx; height: 100rpx;left: -620rpx;border-radius: 500rpx;" mode=""></image>
						</view>
						<!-- <view class="cu-avatar round lg" :style="'background-image:url('+ item.uface + ');'"></view> -->
						<view class="content">
							<view class="flex flex-wrap">
								<view class="basis-lg">
									<view class="text-black text-df">
										<p class="first-p fs-32">
											<!--
												就诊卡的信息
												patientName  名字
												patientage   年龄
												patientgender  性别
					
												用户信息
												age 年龄
												gender 性别
												uname  名字
					
											 -->
											<span> {{ item.patientName && `${item.patientName}` ||  `${item.uname}`}}</span>
											(
											{{item.gender === '1' ? '男' :item.gender === '2'?'女':'未知'}}
											{{ item.patientAge && ` , ${Math.floor(Number(item.patientAge))}岁` || '- -' }}
											)
										</p>
									</view>
								</view>
								<view class="basis-sm">
									<view class="text-right margin-right text-gray text-sm">
										{{$common.parseTime(item.lastTime,'{m}-{d} {h}:{i}')}}
									</view>
									<!-- <image src="/static/img/yuan.png" mode="" class="nav_img">在线</image> -->
								</view>
							</view>
							<!-- 信息 与 条数 -->
							<view class="text-grey text-sm margin-top-xs d-flex ai-center jc-between">
								<view v-if="item.lastContent && item.contentType == 'img'" class="text-cut ellipsis-1 flex-1">
									[图片消息]</view>
								<view v-else-if="item.lastContent && item.contentType == 'voice'"
									class="text-cut ellipsis-1 flex-1">[语音消息]</view>
								<view v-else-if="item.lastContent && chooseMsgType(item.lastContent) === 'task'"
									class="text-cut ellipsis-1 flex-1">{{JSON.parse(item.lastContent).questionnaire_name || ''}}
								</view>
								<view v-else class="text-cut ellipsis-1 flex-1">{{item.lastContent}}</view>
								<view style="min-width: 70rpx;text-align: right;margin-right: 30rpx;">
									<text v-if="item.unRead > 0" class="gui-badge demo gui-bg-red gui-color-white"
										style="padding:4rpx 13rpx;">{{item.unRead}}</text>
								</view>
								<view class="">
									<!-- <image v-if="online" src="/static/img/yuan.png" mode="" class="nav_img">已下线</image> -->
									<!--						<image src="/static/img/yuan1.png" mode="" class="nav_img"><span style="	   line-height: 25rpx !important;">在线</span></image>-->
								</view>
							</view>
					
						</view>
					</view>
					
					<view style="padding:50rpx 0;">
						<gui-empty v-if="sessionList.length <= 0">
							<!-- 判断是否有信息-->
							<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
								<!-- 请根据您的项目要求制作并更换为空图片 -->
								<image class="gui-empty-img" src="/static/kong.png"></image>
							</view>
							<text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top"
								style="color:#9DABFF;">暂无会话</text>
						</gui-empty>
					</view>
				</view>
			</scroll-view>
			
		</view>
		<!-- 居中弹出 -->
		<gui-popup ref="guipopup" :width="'680rpx'" :bgColor="'rgba(0, 0, 0, 0.9)'">
			<view class="gui-relative gui-box-shadow ">
				<!-- <text
					class="gui-block-text demo-close gui-icons gui-color-gray gui-absolute-rt mt-30 mr-30 fs-60"
					@tap.stop="closeGuipopup">&#xe78a;</text> -->
				<view class="gui-bg-white gui-border-radius">
					<view class="py-20 px-30" v-if="wxQrcodeUrl">
						<image :src="wxQrcodeUrl" style="height: 620rpx;width: 620rpx;" mode=""></image>
					</view>
					<view class="text-center pb-40 fs-32 gui-bold" v-if="imUser.nickName">
						<view class="">【 {{doctorInfo.nickName || ''}}】 {{doctorInfo.docTitle}}</view>
						<view class="">{{doctorInfo.dept.deptName}}</view>

					</view>
				</view>
				<text class="gui-block-text demo-close gui-icons gui-color-white fs-60 mt-20"
					style="text-align: center; font-size: 80rpx;" @tap.stop="closeGuipopup">&#xe78a;</text>
			</view>
		</gui-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from 'vuex';


	export default {
		data() {
			return {
				changeDataType: 1,
				wxQrcodeUrl: '', //医生太阳码
				patientName: '', //查询字段
				usertype: uni.getStorageSync('mbuserType'), //显示的用户
				// 是否在线
				online: true,
				navList: [{
					id: 1,
					title: '咨询中',
					value: 0
				}, {
					id: 2,
					title: '咨询结束',
					value: 0
				}],
				TabCur: 0,
				scrollLeft: 0,
				doctorInfo: uni.getStorageSync('mbuserInfo'),
				imUser: {
					userId: uni.getStorageSync('mbuserInfo').userId, // 系统用户id 空
					userUid: '', //链接im返回的uuid
					userType: '1', // 用户类型  1：医生  2：患者
					uname: uni.getStorageSync('mbuserInfo').userName, // 用户名
					nickName: uni.getStorageSync('mbuserInfo').nickName, // 昵称
					uface: uni.getStorageSync('mbuserInfo').avatar // 头像
				},
				sessionList: [],
				timer: null, //定时器
				userUid: uni.getStorageSync("mbuserUid")
			}
		},
		computed: {
			...mapState(['graceIMMsgsCH', 'getMsgReadedNum', 'graceIMStatus', 'graceIMUID', 'graceIMMsgs'])

		},
		watch: {
			userUid() {
				console.log('我有数据了')
			},
			graceIMMsgs() {
				//监控消息变化获取列表和提示信息
				this.mySessionList();
				// this.getMsgReaded();
			},
			graceIMMsgsCH() {
				this.mySessionList();
			}
		},
		// created: function() {
		// 	try {
		// 		this.$store.dispatch('graceIMConnect', this.imUser);
		// 	} catch (e) {}
		// },
		onLoad() {
			console.log(this.graceIMStatus)
			let self = this;
			this.$common.checkLogin(res => {
				if (res === 0) {
					//跳转登录页面
					self.$common.navLaunch('/pages/login/index')
				}
			});
		},
		onShow() {
			var that = this
			if (!uni.getStorageSync("mbuserUid") || !this.graceIMUID) {
				//发起IM登录
				this.$store.dispatch('graceIMConnect', this.imUser).then(res => {
					that.mySessionList();
					that.$store.commit('getMsgReaded')
				});

			} else {
				that.mySessionList();
				that.$store.commit('getMsgReaded')
			}

			//底部标题		 5秒获取用户发来的信息
			// this.timer = setInterval(() => {
			// 	that.getMsgReaded();
			// }, 5000);
		},

		onUnload() {
			console.log('关闭页面')
			// this.clearTimer();
		},
		onHide() {
			console.log('关闭页面11111')
			// this.clearTimer();
		},

		methods: {
			changeData(e) { //状态选择 1、未回复 ， 2、已回复
				this.changeDataType = e;
				this.sessionList = [];
				this.mySessionList();
			},
			openGuipopup() {
				this.$common.RequestData({
					url: this.$common.currentDoctor,
					data: {},
					method: 'get',
				}, res => {
					if (res.code == 200) {
						this.wxQrcodeUrl = this.$common.domain + res.data.qrcodePath,
							this.$refs.guipopup.open();
					} else {
						this.$common.msg("图片获取失败，稍后重试~")
					}

				})
			},
			closeGuipopup() {
				this.$refs.guipopup.close();
				this.wxQrcodeUrl = ''
			},
			//搜索输入内容
			inputting(e) {
				this.patientName = e;
				// this.init();
			},
			inputConfirm() { //输入框的搜索
				this.mySessionList()
			},
			// 清除搜索文字
			clearinit() {
				this.patientName = ''
				this.inputConfirm();
			},
			...mapState(['graceIMConnect', 'getMsgReaded', 'getFriendList']),

			clearTimer() {
				if (this.timer) {
					clearInterval(this.timer);
					this.timer = null;
				}
			},
			// 判断信息类型
			chooseMsgType(str) {
				let imgZZ = new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/);
				try {
					let jsonData = JSON.parse(str);
					if (typeof jsonData === 'number') { // 数字类型
						return 'str';
					}
					return 'task';
				} catch (error) {
					if (imgZZ.test(str)) { // 图片类型
						return 'img';
					} else {
						return 'str';
					}
				}
			},

			// 获取会话列表
			mySessionList: function() {
				this.$common.RequestDataNo({
					url: this.$common.mySessionList,
					method: 'POST',
					data: {
						userUid: uni.getStorageSync("mbuserUid"),
						types: this.navList[this.TabCur].id,
						isReply:this.changeDataType==1?0:this.changeDataType==2?1:'',
						patientName: this.patientName
					}
				}, res => {
					console.log("获取会话列表中", res)
					res.data.map(item => {
						item.gender = item.patientGender
					})
					this.sessionList = res.data;
				}, '', false)

			},
			toConsult(item) {
				// this.$common.openDyMsg(); //订阅
				// console.log("每项点击",item)
				if (item.types == 0) { // 点击新问诊，修改订单状态
					this.$common.RequestDataNo({
						url: this.$common.setEndHh,
						data: {
							id: item.orderId,
							status: 1
						}
					}, res => {

					})
				}
				let imObj = {
					groupIndex: item.groupIndex, // 组ID
					friendName: item.uname, // 患者姓名
					avatar: item.uface, //患者头像
					// openid:uni.getStorageSync("mbhuanzInfo").,
					openid: item.uniappOpenId, //item.openid//患者openid    需要接口提供并修改再更新系统
					friendUid: item.friendUid, // 患者UUid
					orderId: item.orderId, // 订单id
					types: item.types, // 订单状态
					userUid: uni.getStorageSync("mbuserUid"), // 当前用户UUid
					// 标题头使用
					age: item.patientAge = null ? item.age : item.patientAge,
					name: item.patientName = null ? item.uname : item.patientName,
					gender: item.patientGender,
					patientId: item.patientId,
					memberId: item.memberId
					// gender:item.patientGender
				};

				// console.log("点击每一项",imObj)
				// this.$common.navTo('./consult?imObj=' + encodeURIComponent(JSON.stringify(imObj)));
				this.$common.navTo('./consult');
				uni.setStorageSync('mbimObj', imObj)
			},
			tabSelect(e) {
				this.TabCur = e.currentTarget.dataset.id;
				this.scrollLeft = (e.currentTarget.dataset.id - 1) * 60
				this.mySessionList();
			}

		},

	}
</script>

<style scoped>
	.nav-active-line {
		margin-top: 5rpx;
		width: 80rpx;
		height: 6rpx;
		background: #7784eb;
	}
	
	.nav-active-line-i {
		margin-top: 5rpx;
		width: 80rpx;
		height: 6rpx;
		background: #fff
	}
	.changeDataIon {
		font-weight: bold;
		color: #7784eb;
	}

	.file {
		width: 100rpx;
		height: 100rpx;
		background: linear-gradient(to bottom, #8190ff, #7784eb);
		color: #fff;
		border-radius: 100%;
		display: flex;
		justify-content: center;
		position: fixed;
		bottom: 190rpx;
		right: 30rpx;
		align-items: center;
		z-index: 999;
	}

	page {
		height: 100%;
	}

	.l-border {
		width: 80%;
		border: 1rpx solid #a6a6a7;
	}

	.l-button {
		background: #7784eb;
		text-align: center;
		color: #fff;
		width: 70rpx;
		height: 65rpx;
		line-height: 65rpx;
		font-size: 28rpx;
	}

	.nav .cu-item {
		position: relative;
		margin: 0 90rpx;
	}

	.nav .cu-item.cur {
		position: relative;
		border-bottom: none;
	}

	.nav .cu-item.cur::after {
		content: "";
		position: absolute;
		left: 50%;
		bottom: 5rpx;
		transform: translate(-50%, 0);
		width: 40%;
		height: 8rpx;
		background: #fff;
		display: block;
		border-radius: 10rpx;
	}

	.cu-list.menu-avatar>.cu-item .content {
		width: calc(100% - 150rpx);
	}

	.nav_img {
		display: inline-block;
		width: 50rpx;
		height: 50rpx;

	}

	.nav_img span {
		line-height: 25rpx !important;
	}
</style>