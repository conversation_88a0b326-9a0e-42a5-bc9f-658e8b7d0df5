{"_from": "recorder-core", "_id": "recorder-core@1.3.25011100", "_inBundle": false, "_integrity": "sha512-trXsCH0zurhoizT4Z22C0OsM0SDOW+2OvtgRxeLQFwxoFeqFjDjYZsbZEZUiKMJLhBvamI4K7Ic+qZ2LBo74TA==", "_location": "/recorder-core", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "recorder-core", "name": "recorder-core", "escapedName": "recorder-core", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/recorder-core/-/recorder-core-1.3.25011100.tgz", "_shasum": "128ee11f2d24547b82605a5383274226b3a20a65", "_spec": "recorder-core", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "xiangyuecn"}, "bugs": {"url": "https://github.com/xiangyuecn/Recorder/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Recorder库: html5 js 录音 mp3 wav ogg webm amr g711a g711u 格式，支持pc和Android、iOS部分浏览器、Hybrid App（提供Android iOS App源码）、微信，提供ASR语音识别转文字 H5版语音通话聊天示例 DTMF编码解码", "homepage": "https://github.com/xiangyuecn/Recorder", "keywords": ["recorder", "recordapp", "record", "html录音", "h5录音", "html5", "mp3", "wav", "ASR", "语音识别", "语音转文字", "DTMF", "recording", "webrtc"], "license": "MIT", "main": "src/recorder-core.js", "name": "recorder-core", "repository": {"type": "git", "url": "git+https://github.com/xiangyuecn/Recorder.git"}, "version": "1.3.25011100"}