import Vue from 'vue'
import App from './App'

// 引入 im
import store from './store'
Vue.prototype.$store = store;

// 注册
import common from '@/common.js';
import grace from '@/GraceUI5/js/grace.js';

Vue.prototype.$common = common;
Vue.prototype.$grace = grace;

Vue.config.productionTip = false
App.mpType = 'app'

Vue.prototype.$common = common;
Vue.prototype.$appVersion='v1.1.6 20220415001';
// 医院正式环境用001三位数结尾，公司测试环境用01两位数结尾
Vue.prototype.$versionCode=20220415001;
Vue.prototype.appName='ydyz_app';

const app = new Vue({
    ...App
})
app.$mount()