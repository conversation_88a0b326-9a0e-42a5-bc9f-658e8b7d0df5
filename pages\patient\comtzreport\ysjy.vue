<template>
	<!-- 中医4P饮食健康管理建议-饮食 -->
	<view class="fs-32">
		<view class="my-20 fs-32">
			<view class="d-flex mb-20">
				<view>姓名：{{info.name || '-'}}</view>
				<view class="ml-120">性别：{{info.sex == 1 ? "男" : "女"}}</view>
			</view>
			<view>时间：{{obj.create_time ? $common.parseTime(obj.create_time) : '-'}}</view>
		</view>
		<view v-for="(item,index) in list" :key="index" class="d-flex">
			<view class="tle">{{item.key}}</view>
			<view class="gui-table gui-border-l gui-border-t" style="width: 92%;">
				<view class="gui-tbody gui-flex gui-rows gui-nowrap" v-for="(item2, index2) in item.list" :key="index2">
					<text class="gui-width2 gui-td-text gui-text-center gui-border-r gui-border-b">{{item2.key || '无'}}</text>
					<text class="gui-width3 gui-td-text gui-text-center gui-border-r gui-border-b">{{item2.value || ''}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// import {dpMedical} from '@/api/home.js'
	export default {
	  name: 'ysjy',
		props:['visitRecordId','templateDictKey','patientId','info'],
		data(){
			return{
				list:[],
				obj:{}
			}
		},
		watch:{
			// templateDictKey(news,olds){
			// 	console.log('我进来了呵呵呵')
			// 	if( news == 1 ){
			// 		this.getDetail();
			// 	}
			// }
			templateDictKey:{
				handler (newLength, oldLength) {
					if( newLength == 1 ){
						this.getDetail();
					}
				},
				immediate: true
			}
		},
		methods:{
			// 获取建议详情
			getDetail(){
				this.$common.RequestData({
					url: this.$common.dpMedical,
					data:{
						patienId:this.patientId,
						id:this.visitRecordId,
						dictValue:this.templateDictKey
					},
					method: "post"
				}, res => {
					this.obj = res.data;
					this.getInit();
				},true,fail=>{
					
				})
				// dpMedical({
				// 	patienId:this.cardObj.patientId,
				// 	id:this.visitRecordId,
				// 	dictValue:1
				// }).then(res=>{
				// 	this.obj = res.data;
				// 	this.getInit();
				// })
			},
			getInit(){
				let keyList = [
					{label:"烹饪方式",value:"cooking_advice"},
					{label:"蔬菜",value:"vegetables_advice"},
					{label:"水果",value:"fruit_advice"},
					{label:"坚果豆类",value:"nut_advice"},
					{label:"谷类杂粮",value:"cereals_advice"},
					{label:"肉类乳类",value:"meat_advice"},
					{label:"菌类",value:"fungus_advice"},
					{label:"酒·茶",value:"tea_advice"},
					{label:"药膳",value:"diet_advice"},
					{label:"药茶",value:"dtea_advice"},
				];
				let arr = [];
				let obj = this.obj;
				keyList.forEach(item=>{
					if(obj[item.value]){
						let m1 = "";
						let m2 = "";
						let m3 = "";
						for(let key in obj[item.value]){
							if(obj[item.value][key].answer == 1) m1 = m1 + key + "；"; 
							if(obj[item.value][key].answer == 2) m2 = m2 + key + "；";
							if(obj[item.value][key].answer == 3) m3 = m3 + key + "；";
						}
						if(item.value == 'cooking_advice'){
							arr.push({key:item.label+"推荐",list:[
								{key:"多选用",value:m1},
								{key:"少选用",value:m2},
								{key:"不选用",value:m3},
								{key:"补充说明",value:obj[item.value]["备注"] ? obj[item.value]["备注"] : "-"},
							]})
						} else if(item.value == 'diet_advice' || item.value == 'dtea_advice'){
							arr.push({key:item.label+"推荐",list:[
								{key:"推荐",value:m1},
								{key:"补充说明",value:obj[item.value]["备注"] ? obj[item.value]["备注"] : "-"}
							]})
						} else {
							arr.push({key:item.label+"推荐",list:[
								{key:"多吃",value:m1},
								{key:"正常吃",value:m2},
								{key:"不吃",value:m3},
								{key:"补充说明",value:obj[item.value]["备注"] ? obj[item.value]["备注"] : "-"},
							]})
						}
					}
				})
				this.list = arr;
			}
		}
	}
</script>

<style>
	.tle {
		width: 10%;
		border: 1px solid #f2f3f4;
		text-align: center;
		writing-mode: vertical-lr;/* 从左向右 从右向左是 writing-mode: vertical-rl;*/
		writing-mode: tb-lr;/*IE浏览器的从左向右 从右向左是 writing-mode: tb-rl；*/
		letter-spacing: 10rpx;
		line-height: 70rpx;
		/* font-size: 28rpx; */
	}
	.gui-width2{
		flex: 1;
		overflow: hidden;
		padding: 20rpx 10rpx;
		display: flexbox;
		width: 50rpx;
	}
	.gui-width3{
		width: 150rpx;
		flex: 2;
		overflow: hidden;
		padding: 20rpx 10rpx;
		display: flexbox;
	}
	
	.gui-td-text {
		line-height: 40rpx !important;
		/* font-size: 32rpx; */
	}
</style>
