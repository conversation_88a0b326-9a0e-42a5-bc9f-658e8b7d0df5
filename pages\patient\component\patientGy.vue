<template>
	<view class="gui-relative gui-box-shadow gui-bg-white fs-32">
		<view class="" style="height: 70vh;">
			<view class="px-40 pt-20">
				<view class="w-100 gui-flex gui-align-items-center gui-justify-content-center gui-bold fs-36">
					下达监测任务
				</view>
				<view class="gui-form-item gui-border-b">
					<view class="gui-form-label fs-32" style="width: 150rpx;"><text class="gui-color-red">*</text>监测项目:</view>
					<view class="gui-form-body">
						<picker mode="selector" :range="typeArr" @change="typeChange" range-key="indicators">
							<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
								<text class="gui-text fs-32" :style="typeIndex==0?'color: #bebebe;':''">{{typeArr[typeIndex].indicators}}</text>
								<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
							</view>
						</picker>
					</view>
				</view>
				<view class="">
					<view class="gui-form-item gui-border-b">
						<view class="gui-form-label fs-32" style="width: 150rpx;"><text class="gui-color-red">*</text>监测频率:</view>
						<view class="gui-form-body py-10">
							<view class="gui-flex gui-align-items-center w-100 ">
								<view class="gui-flex gui-align-items-center">
									<view class="mr-10 fs-32">每</view>
									<picker class="gui-border px-10" mode="selector" :range="dwArr" @change="dwChange" range-key="dictLabel">
										<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
											<text class="gui-text fs-32 mr-10">{{dwArr[dwIndex]}}</text>
											<text class="gui-form-icon gui-icons gui-text-center gui-color-gray" style="width: 30rpx;">&#xe603;</text>
										</view>
									</picker>
								</view>
								<view class="gui-flex gui-align-items-center">
									<view class="fs-32 mx-10" style="width: 64rpx;">监测</view>
									<picker class="gui-border px-10 w90" mode="selector" :range="plArr" @change="plChange" range-key="dictLabel">
										<view class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
											<text class="gui-text fs-32">{{plArr[plIndex]}}</text>
											<text class="gui-form-icon gui-icons gui-text-center gui-color-gray" style="width: 30rpx;">&#xe603;</text>
										</view>
									</picker>
									<!-- <view class="ml-10">次</view> -->
								</view>
								<view class="gui-flex gui-align-items-center">
									<view class="fs-32 mx-10">共</view>
									<input v-model="zqDate" class="gui-border px-10 h60" type="number" @input="val=>{zqFormday(val,dwIndex)}"/>
									<view class="fs-32 ml-10">{{dwArr[dwIndex]}}</view>
								</view>
							</view>
							<!-- <view class="gui-flex gui-align-items-center mt-15 w-70">
								<view class="w-50">共</view>
								<input v-model="zqDate" class="gui-border mx-10 h60" type="number" style="padding-left: 20rpx;" @input="val=>{zqFormday(val,dwIndex)}" />
								<view v-show="dwIndex>0">{{dwArr[dwIndex]}}</view>
							</view> -->
						</view>
					</view>
					<view class="gui-form-item gui-border-b" v-show="dwIndex>0 && sjdLists.length>0">
						<view class="gui-form-label fs-32" style="width: 160rpx;"><text class="gui-color-red">*</text>日期点：</view>
						<view class="gui-form-body">
							<view class="gui-flex gui-align-items-center">
								<view class="mr-5 fs-32">第</view>
								<view class="" v-for="(item,index) in sjdLists" :key="index">
									<view class="gui-flex gui-align-items-center">
										<input :min="1" :max="Number(dwIndex) === 3?28:7" v-model="item.value" style="height:60rpx; padding-left: 10rpx;" class="gui-border mx-10" @input="val=>{jcFormday(val,index)}"
											type="number" />
										<text
											v-if="Number(dwIndex) != 3">{{index == sjdLists.length-1 ? '天' : '或'}}</text>
										<text
											v-if="Number(dwIndex) == 3">{{index == sjdLists.length-1 ? '日' : '或'}}</text>
									</view>
								</view>

							</view>
						</view>
					</view>
					<view class="gui-form-item">
						<view class="gui-form-label fs-32" style="width: 150rpx;"><text class="gui-color-red">*</text>执行时间:</view>
						<view class="gui-form-body mt-20">
							<gui-stags checkedBg="gui-bg-zdy" :tags="leixs" :padding="35" :size="30" :lineHeight="2"
								@change="sjChange" type="checkbox"></gui-stags>
						</view>
					</view>
					<!-- <view class="gui-form-item gui-border-b">
						<text class="gui-form-label fs-32" style="width: 150rpx;"><text class="gui-color-red">*</text>执行周期:</text>
						<view class="gui-form-body">
							<gui-datetime-between @confirm="dateConfirm" :startValue="formData.startDate" :endValue="formData.endDate"
								:isTime="false">
								<view class="gui-block-text" :style="formData.startDate.includes('请选择')?'color: #bebebe;':''">
									<text class="gui-icons mr-10">&#xe6d1;</text>
									{{formData.endDate == ''?formData.startDate:formData.startDate+' ~ '+formData.endDate}}
								</view>
							</gui-datetime-between>
						</view>
					</view> -->
				</view>
			</view>
			<view style="position: absolute; bottom: 0rpx;" class="w-100 gui-flex gui-space-between fs-36">
				<view @tap.stop="gyclose"
					class="w-50 gui-flex gui-align-items-center gui-justify-content-center py-30 gui-bg-gray">取消</view>
				<view @click="submitRenq"
					class="w-50 gui-flex gui-align-items-center gui-justify-content-center zhuti-bnt-bg gui-color-white py-30">
					保存下达</view>
			</view>
		</view>
		<gui-iphone-bottom></gui-iphone-bottom>
	</view>
</template>
<script>
	import { monitorSet } from '@/api/patient.js'
	export default {
		props: {
			taskTypeArr: {
				type: [Object, Array],
				default () {
					return []
				}
			},
			patientId:{
				type: String,
				default: ''
			}
		},
		watch:{
			taskTypeArr:{
				handler(newValue,oldValue){
					if (newValue) {
						this.typeArr = newValue
						this.$forceUpdate();
					}
				},
				deep:true,
				// immediate:true
			}
		},
		data() {
			return {
				typeArr: [ //任务类型5血压7血糖8血氧9体温10呼吸11舌面象12身高体重
					{
						id:5,
						indicators:"血压",
						indicatorsName:"1",
						timingPointList:[],//时机点
					}
				],
				typeIndex: 0, //任务类型选项
				dwArr: ['天', '周', '月'], //单位
				dwIndex: 0, //单位选项
				plArr: ['1次', '2次', '3次'], //频率
				plIndex: 0, //频率选项
				sjdLists: [], //监测频次-日期点
				startValue2: "请选择执行周期",
				endValue2: '',
				leixs: [
					// {
					// 	id: 1,
					// 	text: "标签1",
					// 	checked: false
					// }
				],//时机点
				sjArr:[],//当前选择时机点
				formData: {
					dates:'',
					monitorDays:'1',
					monitorId:'',
					monitorName:'',
					monitorUnit:'',
					patientId:'',
					times:'',
					startDate:"请选择执行周期",
					endDate:'',
				},
				zqDate: null,
				}
		},
		computed: {

		},
		methods: {
			typeChange(e) { //任务类型选择
				this.typeIndex = e.detail.value;
				this.leixs = [];
				this.sjArr = []
				let arr = this.typeArr[this.typeIndex].timingPointList;
				if (arr.length>0) {
					arr.forEach(item =>{
						this.leixs.push({
							id: item.id,
							text:item.timingPoint,
							checked: false
						})
					})
				}
				this.$forceUpdate();
			},
			dwChange(e) { //监测频率-单位
				this.dwIndex = e.detail.value;
				this.plIndex = 0;
				this.sjdLists = [];
				//this.zqDate = 1;
				if (e.detail.value > 0) {
					// 重置日期点
					this.sjdLists.push({
						value: ""
					});
				}
				if (this.zqDate) {
					this.getDates(this.zqDate,this.dwIndex)
				}
			},
			plChange(e) { //监测频率-次数
				this.plIndex = e.detail.value;
				this.sjdLists = [];
				if(this.dwIndex>=1){
					for (let i = 0; i < parseInt(e.detail.value+1); i++) {
						this.sjdLists.push({
							value: ""
						});
					}
				}
				if (this.zqDate) {
					this.getDates(this.zqDate,this.dwIndex)
				}
			},
			zqFormday(e,dwIndex){//-监测频率-执行周期
				//dwIndex 1天 2周 3月
				//if (this.dwIndex==0) {return this.$common.msg('请先选择执行单位','error',500)}
				if (!e.detail.value || Number(e.detail.value) == 0) {
					this.$common.msg('请输入大于0的有效数字','error',2000);
					this.$set(this,'zqDate',null)
					this.zqDate = null
					this.$forceUpdate()
					return
				}
				let str = /^\d+$/.test(e.detail.value)
				
				if (str) { 
					this.getDates(e.detail.value,dwIndex)
				}else{
					this.formData.startDate = '请选择执行周期';
					this.formData.endDate = '';
				}
			},
			getDates(zqNumber,dwIndex){//计算周期时间
				if (zqNumber) {
					//计算天数和周
					let timeDiff = dwIndex==1?(Number(zqNumber)*7):Number(zqNumber)
					let yesterday = new Date().getTime()+24*60*60*1000
					let dataDay = (timeDiff*3600 * 1000 * 24)+(new Date().getTime()+60*60*1000)
					this.formData.startDate = this.$common.parseTime(new Date(yesterday),'{y}-{m}-{d}');
					this.formData.endDate = this.$common.parseTime(new Date(dataDay),'{y}-{m}-{d}');
					if (dwIndex==2) {//月份
						let noMonth = new Date().getMonth()
						let year = new Date().getFullYear();
						let month = Number(noMonth+1) + Number(zqNumber)
						let today = this.$common.parseTime(new Date(),'{d}')
						if (month > 12) {
						    year++; // 如果现在是12月，则年份加1
							month = month-12
						}
						this.formData.endDate = month<10?`${year}-${0}${month}-${today}`:`${year}-${month}-${today}`;
					}
				}else{
					this.formData.startDate = '请选择执行周期';
					this.formData.endDate = '';
				}
			},
			//健康监测频次-日期点
			jcFormday(e,index) {
				var number = Number(e.detail.value)
				if (this.dwIndex == 1) {
					if (number < 0 || number == 0 || number > 7) {
						// this.sjdLists[index].value = '';
						this.$set(this.sjdLists[index],'value',null)
						this.$common.msg('请输入大于0且小于7的整数日期点！','error',1000);
						this.$forceUpdate()
						return ;
					}
				}
				if (this.dwIndex == 2) {
					if (number < 0 || number == 0 || number > 30) {
						this.sjdLists[index].value = '';
						 this.$common.msg('请输入大于0且小于30的整数日期点！','error',1000);
						 this.$forceUpdate()
						 return;
					}
				}
			},
			sjChange(e,data) {
				this.sjArr = []
				e.map(item=>{
					this.sjArr.push(data[item].id);
				})
			},
			// dateConfirm(res) { //执行周期
			// 	this.formData.startDate = res[0][0] + '-' + res[0][1] + '-' + res[0][2];
			// 	this.formData.endDate = res[1][0] + '-' + res[1][1] + '-' + res[1][2];
			// 	let timeDiff = Math.abs(new Date(this.formData.endDate) - new Date(this.formData.startDate))
			// 	var dataDay = timeDiff / (3600 * 1000 * 24);
			// 	if (this.dwIndex == 2 && dataDay < 7) {
			// 	  this.$common.msg('请选择日期大于7天','error',500)
			// 	  this.formData.startDate = '请选择执行周期';
			// 	  this.formData.endDate = '';
			// 	}

			// },
			gyclose() {
				this.$emit('gyclose',true)
			},
			isValidStringInArray(array) {//数组内容有效值判断
				let strValue = true
				array.forEach(item =>{
					if (!item.value) {
						strValue = false
					}
				})
				return strValue
			},
			submitRenq() {
				if (this.typeIndex <= 0) {
					return this.$common.msg('请选择监测项目！')
				}
				if (!this.zqDate || this.zqDate<= 0) {
					return this.$common.msg('请选择监测频率！')
				}
				if (this.dwIndex >= '1' && this.plIndex >= 0 ) {
					this.formData.dates = this.sjdLists.map(item => parseInt(item.value))
					if (!this.isValidStringInArray(this.sjdLists) || !this.formData.dates.length) {
						this.formData.dates = []
						this.$common.msg('请正确输入日期点！','error',1000)
						return 
					}
				}
				if (!this.sjArr.length) {
					return this.$common.msg('请选择执行时间！')
				}
				if (this.dwIndex=='0' && (this.sjArr.length != this.plIndex+1)) {
					return this.$common.msg('请选择'+(this.plIndex+1)+'个执行时间！')
				}
				if (this.dwIndex !='0' && this.sjArr.length != 1) {
					return this.$common.msg('请选择1个执行时间！')
				}
				if (!this.formData.endDate) {
					return this.$common.msg('请选择执行周期！')
				}
				let setForm = {
					monitorDays:this.plIndex+1,
					monitorId:this.typeArr[this.typeIndex].indicatorsName,
					monitorName:this.typeArr[this.typeIndex].indicators,
					monitorUnit:this.dwIndex+1,
					dates:this.dwIndex>0?this.formData.dates.join(','):'',
					patientId:this.patientId,
					times:this.sjArr.join(','),
					startDate:this.formData.startDate,
					endDate:this.formData.endDate,
				}
				monitorSet(setForm).then(res =>{
					if (res.code == 200) {
						uni.setStorageSync("mbtasktype",5)
						this.$emit('submitgy',true)
					} else{
						this.$common.msg('保存失败，请重新保存。')
					}
				})
				
			},
		}
	}
</script>
<style scoped>

</style>