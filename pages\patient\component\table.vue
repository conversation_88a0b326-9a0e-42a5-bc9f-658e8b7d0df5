<template>
	<view class="">
		<view class="gui-table gui-border-l gui-border-t">
			<view class="gui-theader gui-flex gui-rows gui-nowrap gui-bg-gray">
				<text class="gui-td  gui-border-b gui-td-text gui-bold gui-text-center">项目名称</text>
				<text class="gui-td  gui-border-b gui-td-text gui-bold gui-text-center">检验结果</text>
				<text class="gui-td  gui-border-b gui-td-text gui-bold gui-text-center">参考值</text>
				<view class="gui-td  gui-border-b gui-text-center gui-flex gui-nowrap gui-justify-content-center gui-align-items-center">
					单位
				</view>
			</view>
			<!-- ,{'redtbody':item.refSign.indexOf('低') >-1 || item.refSign.indexOf('高') >-1} -->
			<view :class="['gui-tbody gui-flex gui-rows gui-nowrap gui-bg-gray',{'redtbody':item.refSign == -1 || item.refSign == 1}]"
			v-for="(item, index) in arr" :key="index">
				<text class="gui-td gui-td-text gui-text-center  gui-border-b">{{item.itemName}}</text>
				<text class="gui-td gui-td-text gui-text-center  gui-border-b">
					{{item.itemResult}}
					<image v-if="item.refSign && item.refSign == 1" class="icon" src="../../../static/images/top.png"/>
					<image v-if="item.refSign && item.refSign == -1" class="icon bottom" src="../../../static/images/top.png"/>
				</text>
				<text class="gui-td gui-td-text gui-text-center  gui-border-b">{{item.itemValue}}</text>
				<text class="gui-td gui-td-text gui-text-center  gui-border-b">{{item.itemUnit}}</text>
			</view>
		</view>
	</view>
</template>
<script>
export default {
	props:{
		arr:{
			type:[Object,Array],
			default(){
				return []
			}
		}
	},
	data() {
		return {

		}
	},
	computed:{

	},
	methods: {
		ageOrder : function (orderBy) {
			// 实现排序的方式 :
			// 01. 您可以将排序信息发送给服务器端，排序后在从新渲染页面
			// 02. 前端 js 对数组进行排序
			if(orderBy == 2){
				var result = this.students.sort(function(a,b){
				  return b[3]-a[3];
				});
			}else{
				var result = this.students.sort(function(a,b){
				  return a[3]-b[3];
				});
			}
			this.students = result;
		}
	}
}
</script>
<style scoped>
	.bottom{
		transform:rotate(180deg);
	}
	.icon{
		width: 30rpx;
		height: 30rpx;
	}
	.redtbody{
		color: #d81e06;
	}
/* 普通表格 */
.gui-td{width:100rpx; flex:1; overflow:hidden; padding:20rpx 10rpx; display:flexbox;}
.gui-td-text{
	line-height:40rpx !important;
	font-size:24rpx;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}
</style>
