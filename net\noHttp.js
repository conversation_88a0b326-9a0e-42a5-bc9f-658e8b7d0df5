import Config from "../common.js"
import tools from '@/tools/utils/jsdecrypt.js'

const Http = function(url, method, data = {}, header = {}, config = {}) {
	return new Promise((resolve, reject) => {
		const baseUrl = Config.domain;
		if (url.startsWith('/')) {
			url = `${baseUrl}${url}`
		} else {
			url = `${baseUrl}/${url}`
		}
		try {

		} catch (e) {

		}
		uni.request({
			url,
			method,
			data,
			header: {
				"Content-Type": "application/json; charset=UTF-8",
			},
			...config,
			success(res) {
				if (res.header['otherstr']) {
					res.data = tools.decryptData(res.data, res.header.otherstr)
				}
				console.log('http URL', url)
				console.log('http data', res)
				if (res.data.code == 0 || res.data.code == 200) {
					resolve(res.data)
				} else {
					resolve(res.data)
					Config.msg(res.data.msg ? res.data.msg : res.data.msg);
				}
			},
			fail(err) {
				reject(err)
			}
		})
	})
}
Http.get = (url, data = {}, header = {}, config = {}) => Http(url, 'get', data, header, config);
Http.put = (url, data = {}, header = {}, config = {}) => Http(url, 'put', data, header, config);
Http.post = (url, data = {}, header = {}, config = {}) => Http(url, 'post', data, header, config);
Http.head = (url, data = {}, header = {}, config = {}) => Http(url, 'head', data, header, config);
Http.patch = (url, data = {}, header = {}, config = {}) => Http(url, 'patch', data, header, config);
Http.delete = (url, data = {}, header = {}, config = {}) => Http(url, 'delete', data, header, config);
Http.options = (url, data = {}, header = {}, config = {}) => Http(url, 'options', data, header, config);

export default Http;
