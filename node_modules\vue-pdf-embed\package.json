{"_from": "vue-pdf-embed@1.2.1", "_id": "vue-pdf-embed@1.2.1", "_inBundle": false, "_integrity": "sha512-4uUm4wxaEGT9cS1cyuagAmMJjBxfQXWG1MvdGPesD3CiXhhSp4i0VMUCYwhFXtZ5+QqWv4mXbfLJ29Wpt+Qcuw==", "_location": "/vue-pdf-embed", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-pdf-embed@1.2.1", "name": "vue-pdf-embed", "escapedName": "vue-pdf-embed", "rawSpec": "1.2.1", "saveSpec": null, "fetchSpec": "1.2.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue-pdf-embed/-/vue-pdf-embed-1.2.1.tgz", "_shasum": "013ce78005b548eb5c74f2026afc00b633f00e3d", "_spec": "vue-pdf-embed@1.2.1", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hrynko"}, "bugs": {"url": "https://github.com/hrynko/vue-pdf-embed/issues"}, "bundleDependencies": false, "deprecated": false, "description": "PDF embed component for Vue 2 and Vue 3", "devDependencies": {"@babel/core": "^7.14.2", "@babel/preset-env": "^7.14.2", "@vue/compiler-sfc": "^3.0.11", "babel-jest": "^26.6.3", "babel-loader": "^8.2.2", "copy-webpack-plugin": "^9.1.0", "css-loader": "^5.2.6", "eslint": "^7.27.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^7.10.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.3.2", "husky": "^8.0.0", "jest": "^26.6.3", "lint-staged": "^13.2.2", "pdfjs-dist": "^2.9.359", "prettier": "^2.3.0", "sass": "^1.49.8", "sass-loader": "^12.6.0", "terser-webpack-plugin": "^5.3.1", "vue": "^2.6.12", "vue-jest": "^4.0.1", "vue-loader": "^15.9.7", "vue-loader-next": "npm:vue-loader@^16.2.0", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.6.12", "webpack": "^5.37.1", "webpack-cli": "^4.7.0", "webpack-dev-server": "^3.11.2", "webpack-merge": "^5.8.0", "worker-loader": "^3.0.8"}, "files": ["dist/*", "src/*", "types/*"], "homepage": "https://github.com/hrynko/vue-pdf-embed#readme", "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "pdf"], "license": "MIT", "main": "dist/vue3-pdf-embed.js", "name": "vue-pdf-embed", "peerDependencies": {"vue": "^2.x || ^3.x"}, "repository": {"type": "git", "url": "git+https://github.com/hrynko/vue-pdf-embed.git"}, "scripts": {"build": "webpack build --progress", "lint": "eslint src demo --ext js,vue --fix", "prepare": "husky install && webpack build", "serve": "webpack serve --config webpack.dev.config.js --hot", "test": "jest"}, "types": "types/vue3-pdf-embed.d.ts", "version": "1.2.1"}