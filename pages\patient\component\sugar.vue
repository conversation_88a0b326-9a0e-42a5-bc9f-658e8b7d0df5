<template>
	<view class="gui-margin" style="height: auto;">
		<!-- 局部选项卡 使用 切换导航 + swiper 实现 -->
		<view style="padding:20rpx 0; margin-top:0rpx; ">
			<guiSwitchNavigation3 :items="tabs" :size="120" activeDirection: :currentIndex="currentIndex"
				@change="navchange">
			</guiSwitchNavigation3>
		</view>
		<!-- 轮播项目数量对应 上面的选项标签 -->
		<swiper class="gui-bg-gray" style="margin-top: 0rpx;height:700rpx; max-height:700rpx;" :current="currentIndex"
			@change="swiperChange">
			<!-- 血糖 -->
			<swiper-item style="height:700rpx;min-height:700rpx;">
				<view style="height: 100%;">
					<view class="bg-sty bg-img1" style="height: 60%;">
						<view
							class="gui-color-white gui-flex gui-columns gui-align-items-center gui-justify-content-center">
							<view class="mt-60">
								<text class="fs-50">{{ healthData.sugar.latestSugar || 0 }}</text>
								<text>mmol/l| {{ healthData.sugar.latestTime || '00:00' }}</text>
							</view>
							<!-- <view
                  class="gui-flex gui-align-items-center gui-justify-content-center icons-jia fs-50 mt-30"
                  @click="add()">
                <text>+</text>
              </view> -->
						</view>
					</view>
					<view class="mt-10 pb-20" style="height: 40%;max-height: 40%;">
						<qiun-data-charts type="mix" :opts="zeroOpts" :chartData="zeroChartData" />
					</view>
				</view>
			</swiper-item>

			<!-- 计步 -->
			<swiper-item style="height:700rpx;min-height:700rpx;">
				<view style="height: 100%;">
					<view class="bg-sty bg-img2" style="height: 60%;">
						<view class="gui-color-white">
							<text class="icons-m gui-icons">&#xe61e;</text>
							<view class="yuan">
								<view
									class="yuan-h gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.threeStep.connection ? '已连接' : '未连接' }}</view>
									<view class="mt-30">
										{{
                      healthData.threeStep.currentDayTotalSteps && `${healthData.threeStep.currentDayTotalSteps || 0}步` || '-&#8194;-'
                    }}
									</view>
								</view>
							</view>
							<view class="gui-flex gui-rows gui-space-around mt-20">
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.threeStep.targetSteps || 0 }}步</view>
									<view>目标</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.threeStep.mileage || 0 }}公里</view>
									<view>里程</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.threeStep.heatConsumption || 0 }}大卡</view>
									<view>消耗热量</view>
								</view>
							</view>
						</view>
					</view>
					<view class="mt-10 pb-20" style="height: 40%; max-height: 40%;">
						<qiun-data-charts type="line" :opts="opts" :chartData="chartData" />
					</view>
				</view>
			</swiper-item>

			<!-- 心率 -->
			<swiper-item style="height:700rpx;min-height:700rpx;">
				<view style="height: 100%;">
					<view class="bg-sty bg-img3" style="height: 60%;">
						<view class="gui-color-white">
							<text class="icons-m gui-icons">&#xe61e;</text>
							<view class="yuan">
								<view
									class="yuan-h gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.threeHeart.connection ? '已连接' : '未连接' }}</view>
									<view class="mt-20">
										{{ measureStatus && '测量中' || `${healthData.threeHeart.currentHeart || 0}次/分` || '-&#8194;-' }}
									</view>
									<view
										class="icons-bg gui-flex gui-align-items-center gui-justify-content-center icons-jia fs-24 mt-10"
										@click="measure()" v-if="!measureStatus">
										<text>测量</text>
									</view>
								</view>
							</view>
							<view class="gui-flex gui-rows gui-space-around mt-20">
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.threeHeart.averageHeart || 0 }}次/分</view>
									<view>平均</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.threeHeart.minHeart || 0 }}次/分</view>
									<view>最低心率</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.threeHeart.maxHeart || 0 }}次/分</view>
									<view>最高心率</view>
								</view>
							</view>
						</view>
					</view>
					<view class="mt-10 pb-20" style="height: 40%; max-height: 40%;">
						<qiun-data-charts type="line" :opts="opts" :chartData="twoChartData" />
					</view>
				</view>
			</swiper-item>

			<!-- 睡眠 -->
			<swiper-item style="height:700rpx;min-height:700rpx;">
				<view style="height: 100%;">
					<view class="bg-sty bg-img4" style="height: 60%;">
						<view class="gui-color-white">
							<text class="icons-m gui-icons">&#xe61e;</text>
							<view class="yuan">
								<view
									class="yuan-h gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.careReport.connection ? '已连接' : '未连接' }}</view>
									<view class="mt-30">
										{{
                      healthData.careReport.sleepTime && `${healthData.careReport.sleepTime / 60}h` || '-&#8194;-'
                    }}
									</view>
								</view>
							</view>
							<view class="gui-flex gui-rows gui-space-around mt-20">
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>
										{{ healthData.careReport.sleepDateTime && $common.parseTime(new Date(healthData.careReport.sleepDateTime.replace(/-/g, '/')), '{h}:{i}') || '00:00' }}
									</view>
									<view>入眠时间</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>
										{{ healthData.careReport.date && $common.parseTime(new Date(healthData.careReport.date.replace(/-/g, '/')), '{h}:{i}') || '00:00' }}
									</view>
									<view>起床时间</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>
										{{ healthData.careReport.deepTime && healthData.careReport.deepTime || '0.0' }}h
									</view>
									<view>深睡</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>
										{{ healthData.careReport.lightTime && healthData.careReport.lightTime || '0.0' }}h
									</view>
									<view>浅睡</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>
										{{ healthData.careReport.awakeTime && healthData.careReport.awakeTime || '0.0' }}h
									</view>
									<view>清醒</view>
								</view>
							</view>
						</view>
					</view>
					<view class="mt-10 pb-20" style="height: 40%; max-height: 40%;">
						<qiun-data-charts type="column" :opts="threeOpts" :chartData="threeChartData" />
					</view>
				</view>
			</swiper-item>

			<!-- 血压 -->
			<swiper-item style="height:700rpx;min-height:700rpx;">
				<view style="height: 100%;">
					<view class="bg-sty bg-img5" style="height: 60%;">
						<view class="gui-color-white">
							<text class="icons-m gui-icons">&#xe61e;</text>
							<view class="yuan">
								<view
									class="yuan-h gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.cholesterol.connection ? '已连接' : '未连接' }}
									</view>
									<view class="mt-30">
										{{
                      healthData.cholesterol.newSbpCholestero &&
                      healthData.cholesterol.newSbpCholestero &&
                      `${healthData.cholesterol.newSbpCholestero} / ${healthData.cholesterol.newDbpCholestero}mmHg` || '-&#8194;-'
                    }}
									</view>
								</view>
							</view>
							<view class="gui-flex gui-rows gui-space-around mt-20">
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>{{ healthData.cholesterol.averageCholestero || 0 }}
									</view>
									<view>平均值</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>
										{{
                      `${healthData.cholesterol.maxSbpCholestero || 0} -- ${healthData.cholesterol.minSbpCholestero || 0}`
                    }}
									</view>
									<view>收缩压(最高/最低)</view>
								</view>
								<view class="gui-flex gui-columns gui-align-items-center gui-justify-content-center">
									<view>
										{{
                      `${healthData.cholesterol.maxDbpCholestero || 0} -- ${healthData.cholesterol.minDbpCholestero || 0}`
                    }}
									</view>
									<view>舒张压(最高/最低)</view>
								</view>
							</view>
						</view>
					</view>
					<view class="mt-10 pb-20" style="height: 40%; max-height: 40%;">
						<qiun-data-charts type="mix" :opts="optsFour" :chartData="fourChartData" />
					</view>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	import guiSwitchNavigation3 from '@/components/gui-switch-navigation3.vue'

	export default {
		components: {
			guiSwitchNavigation3
		},
		data() {
			return {
				// option: {},
				zeroChartData: {
					"categories": [],
					"series": [{
						"name": "血糖",
						"data": []
					}]
				},
				chartData: {
					"categories": [],
					"series": [{
						"name": "计步",
						"data": []
					}]
				},
				twoChartData: {
					"categories": [],
					"series": [{
						"name": "心率",
						"data": []
					}]
				},
				threeChartData: {
					"categories": [],
					"series": [{
						"name": "深睡",
						"data": []
					}, {
						"name": "浅睡",
						"data": []
					}, {
						"name": "清醒",
						"data": []
					}]
				},
				fourChartData: {
					"categories": [],
					"series": [{
						"name": "舒张压",
						"data": []
					}, {
						"name": "收缩压",
						"data": []
					}]
				},
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 20, 10, 10],
					legend: {
						show: false //是否显示图例
					},
					xAxis: {
            disableGrid: true,
            rotateLabel: true,
            rotateAngle: 10
					},
					yAxis: {
            disabled: false,
            disableGrid: false,
            splitNumber: 5,
            gridType: "dash",
            dashLength: 4,
            padding: 10,
            showTitle: false
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						},

						length: {
							width: 15
						},

					}
				},
				optsFour: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 20, 5, 5],
					legend: {
						show: false //是否显示图例
					},
					xAxis: {
						disableGrid: true,
            rotateLabel: true,
            rotateAngle: 10
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						splitNumber: 5,
						gridType: "dash",
						dashLength: 4,
						padding: 10,
						showTitle: false
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						},
						mix: {
							column: {
								width: 20
							}
						},
						length: {
							width: 15
						}
					}
				},
				twoOpts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 20, 10, 10],
					legend: {
						show: false //是否显示图例
					},
					xAxis: {
						disableGrid: true
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						splitNumber: 5,
						gridType: "dash",
						dashLength: 4,
						padding: 10,
						showTitle: false
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						},
						mix: {
							column: {
								width: 20
							}
						},
						length: {
							width: 15
						}
					}
				},
				threeOpts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 15, 0, 5],
					legend: {},
					xAxis: {
						disableGrid: true
					},
					yAxis: {
						data: [{
							min: 0
						}]
					},
					extra: {
						column: {
							type: "group",
							width: 30,
							activeBgColor: "#000000",
							activeBgOpacity: 0.08
						}
					}
				},
				zeroOpts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 20, 10, 10],
					legend: {
						show: false //是否显示图例
					},
					xAxis: {
						disableGrid: true,
            rotateLabel: true,
            rotateAngle: 10
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						splitNumber: 5,
						gridType: "dash",
						dashLength: 4,
						padding: 10,
						showTitle: false
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						},
						mix: {
							column: {
								width: 20
							}
						},
						length: {
							width: 15
						}
					}
				},
				currentIndex: 0,
				tabs: [{
					id: 0,
					name: '血糖'
				}, {
					id: 1,
					name: '计步'
				}, {
					id: 2,
					name: '心率'
				}, {
					id: 3,
					name: '睡眠'
				}, {
					id: 4,
					name: '血压'
				}],
				/* 患者设备列表 */
				deviceList: [],
				/* 健康数据 */
				healthData: {
					/* 血糖 */
					sugar: {
						/* 最新血糖 */
						latestSugar: 0,
						/* 最新血糖 */
						latestTime: '00:00',
						/* 区间数据 */
						section: []
					},
					/* 计步 */
					threeStep: {
						/* 里程（公里） */
						mileage: 0,
						/* 目标步数 */
						targetSteps: 0,
						/* 平均步数 */
						averageSteps: 0,
						/* 消耗热量（大卡） */
						heatConsumption: 0,
						/* 当日总步数 */
						currentDayTotalSteps: 0,
						/* 步数区间值['00:00', '06:00', '12:00', '18:00', '24:00'] */
						section: [],
						/* 连接状态 */
						connection: false
					},
					/* 睡眠 */
					careReport: {
						/* 产生时间 */
						date: "",
						/* 入睡时间 */
						sleepDateTime: "",
						/* 入睡时长（分钟） */
						fallasleep: 0,
						/* 睡眠时长 （分钟） */
						sleepTime: 0,
						/* 休息时长 （分钟） */
						restTime: 0,
						/* 清醒时长 （分钟）*/
						awakeTime: 0,
						/* 浅睡时长 （分钟） */
						lightTime: 0,
						/* 浅睡时长 （分钟） */
						remTime: 0,
						/* 深睡时长 （分钟） */
						deepTime: 0,
						/* 睡眠时段 */
						bucket: "",
						/* 平均心率 */
						avghr: "",
						/* 平均呼吸率 */
						avgbr: "",
						/* 清醒百分比 */
						awakePer: "",
						/* rem百分比 */
						remPer: "",
						/* 浅睡百分比 */
						lightPer: "",
						/* 深睡百分比 */
						deepPer: "",
						/* 睡眠效率 */
						efficiency: "",
						/* 睡眠评分 */
						score: 0,
						/* 平均睡眠 */
						averageSleep: 0,
						/* 连接状态 */
						connection: false
					},
					/* 心率 */
					threeHeart: {
						/* 合计心率 */
						totalHeart: 0,
						/* 平均心率 */
						averageHeart: 0,
						/* 当前心率 */
						currentHeart: 0,
						/* 最高心率 */
						maxHeart: 0,
						/* 最低心率 */
						minHeart: 0,
						/* 心率区间 */
						section: [],
						/* 连接状态 */
						connection: false
					},
					/* 血压 */
					cholesterol: {
						/* 最新舒张压 */
						newDbpCholestero: 0,
						/* 最新收缩压 */
						newSbpCholestero: 0,
						/* 平均血压 */
						averageCholestero: 0,
						/* 最高舒张压 */
						maxDbpCholestero: 0,
						/* 最低舒张压 */
						minDbpCholestero: 0,
						/* 最高收缩压 */
						maxSbpCholestero: 0,
						/* 最低收缩压 */
						minSbpCholestero: 0,
						/* 血压区间 */
						section: []
					},
					/* 血氧 */
					oxygen: {}
				},
				patientInfo: '', //患者信息
				measureStatus: false,
				/* 健康数据预警 */
				indicatorInfo: [],
				ready: false
			}
		},
		props: {
			isRefresh: {
				type: Number,
				default: 0
			},
			info: {
				type: String,
				default: 0
			},
		},
		watch: {
			isRefresh: {
				deep: true,
				handler(newValue, oldValue) {
					if (newValue != oldValue) {
						//子组件中监控值变化做 相关业务功能
						this.loadHealthData() //更新数据
					}
				}
			},
			info: {
				deep: true,
				handler(newValue, oldValue) {
					this.patientInfo = newValue;
				}
			}
		},
		onShow() {
			this.ready && this.getDeviceDetailList()
		},
		created() {
			this.getDeviceDetailList()
		},
		methods: {
			navchange: function(index) {
				this.currentIndex = index;
			},
			swiperChange: function(e) {
				this.currentIndex = e.detail.current;
			},
			// 心率测量
			measure() {
				this.measureStatus = true
				setTimeout(() => this.getHealthData(1), 1500)
			},
			// 患者设备列表
			getDeviceDetailList() {
				if (this.info) {
					this.$common.RequestData({
						url: this.$common.getDeviceDetailList + this.info, // idCard 患者身份证参数（123为测试参数）
						method: 'GET'
					}, async res => {
						this.$set(this, 'deviceList', res?.data || [])
						await this.loadIndicatorList()
						this.loadHealthData()
						this.ready = true
					})
				}
			},
			loadIndicatorList() {
				this.$common.RequestData({
					url: this.$common.indicatorList,
					method: 'GET'
				}, res => this.indicatorInfo = res?.data || [])
			},
			loadHealthData() {
				if (this.info) {
					this.getHealthData()
					this.getHealthData(1)
					this.getHealthData(2)
					this.getHealthData(3)
				}
			},
			// 设备监控数据
			// iotType = 1 : 2.计步 4.心率 3.睡眠（暂未使用）
			// iotType = 2 : 3.睡眠（睡眠带）
			// iotType = 3 : 6.血压
			// iotType = null : 1.血糖（血糖为手动输入，没有设备类型）
			getHealthData: function(iotType) {
				if (!iotType) {
					this.$common.RequestData({
						url: this.$common.getHealthData,
						method: 'GET',
						data: {
							// 设备码 必填（血糖没有设备则填写患者id）
							iotSn: this.info,
							// 患者id 必填（123为测试参数）
							patientId: this.info,
							// 查询日期yyyy-MM-dd，枚举：2022-10-18（可空，空则查询今天的数据）
							date: '',
							// 健康数据类型 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] （可空，空则查询所有同时符合[iotSn、patientId、date]的健康数据）
							healthDataType: '1'
						}
					}, res => {
						const data = [],
							series = [],
							dataSpot = [],
							categories = [],
							resData = res?.data,
							/* 早餐前空腹 */
							indicatorOne = this.indicatorInfo.find(item => item.indexType === 1 && item
								.bloodSugarType === 1),
							/* 午/晚餐前后 */
							indicatorTow = this.indicatorInfo.find(item => item.indexType === 1 && item
								.bloodSugarType === 2)
						this.$set(this.healthData, 'sugar', resData?.sugar || {})
						// console.log("sugar", this.healthData.sugar)
						/* 处理展示数据 */
						resData.sugar?.section.forEach(item => {
							data.push(item.bloodSugar)
							if (item.recordType === 1) {
								if (item.bloodSugar > indicatorOne?.alertMax) {
									dataSpot.push(item.bloodSugar)
								} else {
									dataSpot.push(null)
								}
							} else {
								if (item.bloodSugar > indicatorTow?.alertMax) {
									dataSpot.push(item.bloodSugar)
								} else {
									dataSpot.push(null)
								}
							}
							categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(
									/-/g, '/')),
								'{m}/{d} {h}:{i}'))
						})
						/* 补充数据 */
						series.push({
							name: "血糖",
							type: "line",
							data: JSON.parse(JSON.stringify(data))
						})
						/* 处理预警 */
						data.forEach((item, index) => {
							if (index === 0) {
								if (item > indicatorOne?.alertMax) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}
							} else {
								if (item > indicatorTow?.alertMax) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}
							}
						})
						this.$set(this, 'zeroChartData', {
							series: series,
							categories: categories
						})
					})
				}
        else if (iotType === 1) {
					/* 手环信息 */
					const bracelet = this.deviceList.find(device => Object.is(device.iotType, 1));
					/* 具备手环信息再查询健康数据 */
					if (bracelet) {
						this.$common.RequestData({
							url: this.$common.getHealthData,
							method: 'GET',
							data: {
								// 设备码 必填（血糖没有设备则填写患者id）
								iotSn: bracelet.iotSn,
								// 患者id 必填（123为测试参数）
								patientId: this.info,
								// 查询日期yyyy-MM-dd，枚举：2022-10-18（可空，空则查询今天的数据）
								date: '',
								// 健康数据类型 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] （可空，空则查询所有同时符合[iotSn、patientId、date]的健康数据）
								healthDataType: '2/4'
							}
						}, res => {
							const resData = res?.data;
							this.$set(this.healthData, 'threeStep', resData?.threeStep || {})
							this.$set(this.healthData, 'threeHeart', resData?.threeHeart || {})
							this.$set(this.healthData.threeStep, 'connection', bracelet?.status && true ||
								false)
							this.$set(this.healthData.threeHeart, 'connection', bracelet?.status && true ||
								false)
							// console.log("threeStep", this.healthData.threeStep)
							// console.log("threeHeart", this.healthData.threeHeart)
							/* 计步 */
              const threeStep = this.healthData?.threeStep
							this.$set(this, 'chartData', {
                categories: [`${threeStep.date} 00:00`, `${threeStep.date} 06:00`, `${threeStep.date} 12:00`, `${threeStep.date} 18:00`, `${threeStep.date} 24:00`],
                series: [{
                  name: "计步",
                  data: threeStep?.section || [null, null,
                    null, null, null
                  ]
                }]
							})
							/* 心率 */
							const data = [],
								series = [],
								dataSpot = [],
								categories = [],
								indicator = this.indicatorInfo.find(item => item.indexType === 2)
							/* 处理展示数据 */
							this.healthData.threeHeart.section.forEach(item => {
								data.push(item.heartrate)
								if (item.heartrate > indicator?.alertMax) {
									dataSpot.push(item.heartrate)
								} else if (item.heartrate < indicator?.alertMin) {
									dataSpot.push(item.heartrate)
								} else {
									dataSpot.push(null)
								}
								categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(
										/-/g, '/')),
									'{m}/{d} {h}:{i}'))
							})
							/* 补充数据 */
							series.push({
								name: "心率",
								type: "line",
								data: JSON.parse(JSON.stringify(data))
							})
							/* 处理预警 */
							data.forEach((item, index) => {
								if (item > indicator?.alertMax) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								} else if (item < indicator?.alertMin) {
									data[index] = null
									series.push({
										point: true,
										type: "point",
										data: dataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}
							})
							this.$set(this, 'twoChartData', {
								series: series,
								categories: categories
							})
							this.measureStatus = false
						})
					}
				}
        else if (iotType === 2) {
					/* 睡眠带信息 */
					const sleepingBand = this.deviceList.find(device => Object.is(device.iotType, 2));
					if (sleepingBand) {
						this.$common.RequestData({
							url: this.$common.getHealthData,
							method: 'GET',
							data: {
								// 设备码 必填（血糖没有设备则填写患者id）
								iotSn: sleepingBand.iotSn,
								// 患者id 必填（123为测试参数）
								patientId: this.info,
								// 查询日期yyyy-MM-dd，枚举：2022-10-18（可空，空则查询今天的数据）
								date: '',
								// 健康数据类型 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] （可空，空则查询所有同时符合[iotSn、patientId、date]的健康数据）
								healthDataType: '3'
							}
						}, res => {
							const resData = res?.data;
							this.$set(this.healthData, 'careReport', resData?.careReport || {})
							this.$set(this.healthData.careReport, 'connection', sleepingBand?.status && true ||
								false)
							// console.log("careReport", this.healthData.careReport)
							/* 睡眠 */
							this.$set(this, 'threeChartData', {
								categories: [this.healthData?.careReport
									?.sleepDateTime && this.$common.parseTime(new Date(this
										.healthData.careReport
										.sleepDateTime.replace(/-/g, '/')), '{m}/{d} {h}:{i}') ||
									''
								],
								series: [{
										name: "深睡",
										data: [this.healthData?.careReport?.deepTime]
									},
									{
										name: "浅睡",
										data: [this.healthData?.careReport?.lightTime]
									},
									{
										name: "清醒",
										data: [this.healthData?.careReport?.awakeTime]
									}
								]
							})
						})
					}
				}
        else if (iotType === 3) {
					/* 血压计信息 */
					const sphygmomanometer = this.deviceList.find(device => Object.is(device.iotType, 3));
					if (sphygmomanometer) {
						this.$common.RequestData({
							url: this.$common.getHealthData,
							method: 'GET',
							data: {
								// 设备码 必填（血糖没有设备则填写患者id）
								iotSn: sphygmomanometer.iotSn,
								// 患者id 必填（123为测试参数）
								patientId: this.info,
								// 查询日期yyyy-MM-dd，枚举：2022-10-18（可空，空则查询今天的数据）
								date: '',
								// 健康数据类型 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] （可空，空则查询所有同时符合[iotSn、patientId、date]的健康数据）
								healthDataType: '6'
							}
						}, res => {
							const resData = res?.data
							this.$set(this.healthData, 'cholesterol', resData?.cholesterol || {})
							this.$set(this.healthData.cholesterol, 'connection', sphygmomanometer?.status &&
								true || false)
							// console.log("cholesterol", this.healthData.cholesterol)
							/* 血压 */
							const dbpData = [],
								sbpData = [],
								series = [],
								dbpDataSpot = [],
								sbpDataSpot = [],
								categories = [],
								/* 舒张压 */
								indicatorOne = this.indicatorInfo.find(item => item.indexType === 3 && item
									.bloodPressureType === 1),
								/* 收缩压 */
								indicatorTow = this.indicatorInfo.find(item => item.indexType === 3 && item
									.bloodPressureType === 2)
							/* 处理展示数据 */
							this.healthData.cholesterol.section.forEach(item => {
								dbpData.push(item.dbp)
								sbpData.push(item.sbp)
								if (item.dbp > indicatorOne?.alertMax) {
									dbpDataSpot.push(item.dbp)
								} else if (item.dbp < indicatorOne?.alertMin) {
									dbpDataSpot.push(item.dbp)
								} else {
									dbpDataSpot.push(null)
								}
								if (item.sbp > indicatorTow?.alertMax) {
									sbpDataSpot.push(item.sbp)
								} else if (item.sbp < indicatorTow?.alertMin) {
									sbpDataSpot.push(item.sbp)
								} else {
									sbpDataSpot.push(null)
								}
								categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(
										/-/g, '/')),
									'{m}/{d} {h}:{i}'))
							})
							/* 补充数据 */
							series.push({
								type: "line",
								name: "舒张压",
								data: JSON.parse(JSON.stringify(dbpData))
							}, {
								type: "line",
								name: "收缩压",
								data: JSON.parse(JSON.stringify(sbpData))
							})
							/* 处理预警 */
							dbpData.forEach((item, index) => {
								if (item > indicatorOne?.alertMax) {
									dbpData[index] = null
									series.push({
										point: true,
										type: "point",
										data: dbpDataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								} else if (item < indicatorOne?.alertMin) {
									dbpData[index] = null
									series.push({
										point: true,
										type: "point",
										data: dbpDataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}
							})
							sbpData.forEach((item, index) => {
								if (item > indicatorTow?.alertMax) {
									sbpData[index] = null
									series.push({
										point: true,
										type: "point",
										data: sbpDataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								} else if (item < indicatorTow?.alertMin) {
									sbpData[index] = null
									series.push({
										point: true,
										type: "point",
										data: sbpDataSpot,
										color: "#f04864",
										textColor: "#f04864",
										format: 'linSpotFormatter'
									})
								}
							})
							this.$set(this, 'fourChartData', {
								series: series,
								categories: categories,
							})
						})
					}
				}
			}
		}
	}
</script>
<style lang="less" scoped>
	//血糖加号
	.icons-jia {
		width: 150rpx;
		border-radius: 50rpx;
		height: 50rpx;
		line-height: 50rpx;
		background-color: #bfbfbf
	}

	//心率测量按钮
	.icons-bg {
		background-color: #ededed !important;
		color: #5a5656 !important;
	}

	//背景图片
	.bg-sty {
		background-attachment: fixed;
		background-size: 100% 100% !important;
		background-position: center center;
	}

	.bg-img1 {
		background: url("https://img.starup.net.cn/xmkj/zwb/img/8.png") no-repeat;
	}

	.bg-img2 {
		background: url("https://img.starup.net.cn/xmkj/zwb/img/5.png") no-repeat;
	}

	.bg-img3 {
		background: url("https://img.starup.net.cn/xmkj/zwb/img/1.png") no-repeat;
	}

	.bg-img4 {
		background: url("https://img.starup.net.cn/xmkj/zwb/img/7.png") no-repeat;
	}

	.bg-img5 {
		background: url("https://img.starup.net.cn/xmkj/zwb/img/6.png") no-repeat;
	}

	//圆圈
	.yuan {
		width: 240rpx;
		height: 240rpx;
		border: 2px solid white;
		border-radius: 240rpx;
		margin: auto;
	}

	.yuan-h {
		height: 240rpx;
	}

	//左上角小图标
	.icons-m {
		font-size: 44rpx;
		position: relative;
		top: 30rpx;
		left: 30rpx;
	}
</style>
