import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAOkVYHux/d/f0KcO6l9hjE2KEMmCsCKn\n' +
  'vVhdpxRUgtv3sLX9KlzSgwgydoLSCBTOrlFsMbKXif9QMvb8NgP/QYMCAwEAAQ=='

const privateKey = 'MIIBVgIBADANBgkqhkiG9w0BAQEFAASCAUAwggE8AgEAAkEA6RVge7H939/Qpw7q\n' +
  'X2GMTYoQyYKwIqe9WF2nFFSC2/ewtf0qXNKDCDJ2gtIIFM6uUWwxspeJ/1Ay9vw2\n' +
  'A/9BgwIDAQABAkEAiMMoR6waoJ+82CkiUDD2mBCo5PQv0UXHDfB1trIXzmSavExB\n' +
  'E+YUTznqrYEMhFz1wz2FYva6+M6Ug25U5ppC8QIhAPRfE1j/Xh4fjB8IQ0KFfTyM\n' +
  'cbj4zjUx4fRqr30qjSqtAiEA9CzK6zUrlqgjlF7MbnkPQbPOrPgSSE2ZnVJE3T7N\n' +
  'Uu8CIQDhkyH49UGb7OMbgjUUIsUIL1yKsqEm2XUKebCkibLlwQIhAJDPG8zgbKhd\n' +
  'sTQmAJGn5B9wFqtIN/d/ZhICH2si+rwrAiAECz7Rb8SnFpptHRBF6zwxtJ/Qd0rW\n' +
  'Ye1LRsNOvdgFcg=='

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}

