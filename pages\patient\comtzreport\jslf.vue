<template>
  <!-- 中医饮食指导（儿科一般指导） -->
  <view class="fs-32">
    <view class="my-20 fs-32">
      <view class="d-flex mb-20" style="justify-content: space-between;">
        <view>姓名：{{info.name || '-'}}</view>
        <view>性别：{{info.sex == 1 ? "男" : "女"}}</view>
        <view>年龄：{{info.age || '-'}}岁</view>
      </view>
      <view>时间：{{list.length ? $common.parseTime(list[0].createTime) : '-'}}</view>
    </view>
    <uni-table :loading="loading" border stripe emptyText="暂无更多数据">
      <!-- 表头行 -->
      <uni-tr>
        <uni-th align="center" width="80rpx">时间</uni-th>
        <uni-th align="center" width="150rpx">类型</uni-th>
        <uni-th align="center">执行方案</uni-th>
      </uni-tr>
      <!-- 表格数据行 -->
      <template v-for="(item, index) in list">
        <!-- 治疗行 -->
        <uni-tr :key="`${index}-therapy`">
          <uni-td align="center" :rowspan="4" class="time-cell">
            <view class="time-text">{{formatTimeText(item.arrange)}}</view>
          </uni-td>
          <uni-td align="center">治疗</uni-td>
          <uni-td align="left" class="content-cell">{{item.therapy}}</uni-td>
        </uni-tr>
        <!-- 运动行 -->
        <uni-tr :key="`${index}-exercise`">
          <uni-td align="center">运动</uni-td>
          <uni-td align="left" class="content-cell">{{item.exercise}}</uni-td>
        </uni-tr>
        <!-- 饮食行 -->
        <uni-tr :key="`${index}-diet`">
          <uni-td align="center">饮食</uni-td>
          <uni-td align="left" class="content-cell">{{item.diet}}</uni-td>
        </uni-tr>
        <!-- 文化活动行 -->
        <uni-tr :key="`${index}-culture`">
          <uni-td align="center">文化活动</uni-td>
          <uni-td align="left" class="content-cell">{{item.culture}}</uni-td>
        </uni-tr>
      </template>
    </uni-table>
  </view>
</template>

<script>
import {
  listFastingTherapy
} from '@/api/patient.js'
export default {
  name: 'jslf',
  props: ['templateId', 'visitRecordId', 'templateDictKey', 'info', 'patientId'],
  data() {
    return {
      loading: false,
      cardObj: uni.getStorageSync('mbcardObj'),
      list: []
    }
  },
  watch: {
    templateDictKey: {
      handler(val) {
      	if (val == 35) {
      		this.getDetail();
      	}
      },
      immediate: true
    }
  },
  methods: {
    // 获取建议详情
    getDetail() {
      this.loading = true
      listFastingTherapy({
        visitId: this.visitRecordId,
        patientId: this.patientId
      }).then(res => {
        this.list = res.rows
        this.loading = false
      })
    },
    // 格式化时间文本为垂直显示
    formatTimeText(text) {
      if (!text) return '';
      // 将文本按字符分割并用换行符连接
      return text.split('').join('\n');
    }
  },
}
</script>

<style>
.tle {
  width: 10%;
  border: 1px solid #f2f3f4;
  text-align: center;
  writing-mode: vertical-lr;
  /* 从左向右 从右向左是 writing-mode: vertical-rl;*/
  writing-mode: tb-lr;
  /*IE浏览器的从左向右 从右向左是 writing-mode: tb-rl；*/
  letter-spacing: 10rpx;
  line-height: 70rpx;
  /* font-size: 28rpx; */
}

.gui-td {
  width: 100rpx;
  flex: 1;
  overflow: hidden;
  padding: 20rpx 10rpx;
  display: flexbox;
}

.gui-td-text {
  line-height: 40rpx !important;
  /* font-size: 24rpx; */
}

#lineTd {
  padding: 0;
  background: #fff url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxsaW5lIHgxPSIwIiB5MT0iMCIgeDI9IjEwMCUiIHkyPSIxMDAlIiBzdHJva2U9IiNFQkVFRjUiIHN0cm9rZS13aWR0aD0iMSIvPjwvc3ZnPg==) no-repeat 100% center;
}

.time-cell {
  padding: 15rpx 3rpx !important;
  vertical-align: middle;
  font-weight: bold;
  background-color: #f8f9fa;
  width: 80rpx !important;
  max-width: 80rpx !important;
  min-width: 80rpx !important;
  text-align: center;
}

.time-text {
  white-space: pre-line;
  line-height: 1.6;
  font-size: 24rpx;
  letter-spacing: 2rpx;
}

.content-cell {
  padding: 12rpx 15rpx !important;
  line-height: 1.4;
  word-wrap: break-word;
  min-height: 60rpx;
}

/* 表格样式优化 */
.uni-table-td {
  padding: 12rpx 15rpx !important;
  font-size: 28rpx;
  line-height: 1.4;
  vertical-align: middle;
}

.uni-table-th {
  padding: 15rpx 10rpx !important;
  font-size: 30rpx;
  font-weight: bold;
  background-color: #e9ecef;
}
</style>
