<template>
	<view class="mx-40 my-20" style="height:  100vh;">
		<view class="gui-text-center gui-bold fs-30"> <text>舌面像记录</text></view>
			<view style="margin-top:15rpx" class="gui-flex gui-space-between">
					<view class="gui-text-small ">姓名：{{info.name}}</view>
					<view class="gui-text-small   ">性别：{{info.sex ==1?'男' : '女'}}</view>
					<view class="gui-text-small ">年龄：{{info.age || '-'}}</view>
					<view class="gui-text-small   ">医生：{{info.doctorName}}</view>
				</view>
				<view style="margin-top:15rpx" class="gui-flex gui-space-between">
						<view class="gui-text-small">就诊卡：{{info.visitCardNum || '-'}}</view>
						<view class="gui-text-small">手机号：{{info.tel || '-'}}</view>
						<view class="gui-text-small "></view>
					</view>
					<view class="gui-table  gui-border-t" style="margin-top:50rpx; ">
						<view class="gui-bold fs-30">舌 面：</view>
						<view class="gui-margin-top">
							<image v-for="(item,index) in list" :key="index" :src="item.img" class="logo" @click="clickImg(item)"></image>
							<!-- <gui-image src="/static/images/sheimge.png" :height="200" :width="300"></gui-image> -->
						</view>
						<view class="gui-margin-top gui-bold fs-30">描 述：</view>
						<view class="mt-10 fs-28 mb-30 content pb-20"> {{info.lingualRemark || '无'}}</view>
						<!-- <view class=" gui-flex gui-rows gui-nowrap  ">
							<text class="gui-td1 p-20 td gui-border-r gui-border-b gui-td-text fs-30 gui-border-l  gui-text-center">临床诊断</text>
							<text class="gui-td2 gui-border-r gui-border-b gui-td-text gui-bold gui-text-center px-20"> </text>
						</view>
						<view class=" gui-flex gui-rows gui-nowrap  ">
							<text class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex gui-justify-content-center  gui-text-center">检查所见</text>
							<text  class="shsj gui-td2 gui-border-r gui-border-b gui-td-text py-40  px-20 fs-28 ">  {{list.observation}}</text>
						</view> -->
						<!-- <view style="margin-top:15rpx" class="gui-flex gui-space-between">
								<view class="gui-text-small ">申请医生：韩景波</view>
								<view class="gui-text-small   "></view>
								<view class="gui-text-small ">检查日期：2022-02-04</view>
							</view>
							<view style="margin-top:15rpx" class="gui-flex gui-space-between">
									<view class="gui-text-small ">报告人：admin</view>
									<view class="gui-text-small   "></view>
									<view class="gui-text-small ">报告日期：2022-02-04</view>
								</view> -->

				
					</view>
					
	</view>
</template>

<script>
	export default {
		data() {
			return {
				content:'舌质颜色偏红，舌体胖大。舌苔黄腻，脾虚。痰湿化热，生活习惯不佳出现脾虚，水湿痰湿内停湿邪重滞，身体乏力，沉重疲劳，苔黄提示有热，口腔溃疡。',
				info:{},
				list:[],
			}
		},
		onLoad: function(option) {
		this.info = JSON.parse(option.info);
		let imgstr = this.info.lingualFiles
		if (imgstr != null) {
			this.list = imgstr.split(",").map(item =>{
				let obj = {}
				obj.img = item
				return obj 
			})
		}
		// this.report();
	
			},
			
		methods: {
			clickImg(item) {
				wx.previewImage({
					urls: [item.img], //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
					current: '', // 当前显示图片的http链接，默认是第一个
					success: function(res) {},
					fail: function(res) {},
					complete: function(res) {},
				})
			},
			report(){
				this.$common.RequestData({
					url: this.$common.getResultInfo+'/'+this.info.id,
					data: {},
					method: 'get',
				}, res => {
						// console.log(res)
							this.list=res.data;
				
				}, )
			}
		}
	}
</script>

<style scoped>
	.logo {
			height: 350rpx;
			width: 300rpx;
		}
	.content{
		line-height: 40rpx;
		text-indent: 60rpx;
		color:#6f6f6f;
	}
.demo{width:210rpx; height:88rpx; line-height:88rpx; text-align:center; margin:10rpx;}
.demo-auto-width{height:88rpx; line-height:88rpx; text-align:center; margin:10rpx;}
.gui-text-small{font-size:20rpx;}
.gui-td1{width:30%; display:flexbox;}
.gui-td2{width:70%; display:flexbox;}
.td{color:#7784eb;}
.shsj{text-indent:2em}
</style>
