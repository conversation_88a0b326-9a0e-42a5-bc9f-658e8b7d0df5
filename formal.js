 // 正式环境
export default function cofing(e){
	let domain
	let socketUrl
	let ddId
	let bingingWxUser
	let appid
	let agentid
	//广中医
	if(e == 1){
		domain = 'http://zwb1.gxzyy.com.cn/prod-api';//医院地址2024-6-19升级数据安全版本后修改为此域名
		socketUrl = 'ws://zwb1.gxzyy.com.cn/ws';     //医院环境socket2024-6-19升级数据安全版本后修改为此域名
		// domain = 'https://zwb.gzy.starup.net.cn/prod-api';//医院地址
		// socketUrl = 'wss://zwb.gzy.starup.net.cn/ws';     //医院环境socket
		bingingWxUser = true
		ddId = ''
		appid = ''
		agentid = ''
	}else if(e == 2){
		domain = 'https://jyyyzyjk.jyhosp.cn/web-api';//贵阳医院地址2024-06-4更换地址
		socketUrl = 'wss://jyyyzyjk.jyhosp.cn/web-ws'; //贵阳医院环境socket没有证书的时候只用ws就可以了
		ddId = 'ding096c8e73e2c8f83335c2f4657eb6378f'
		bingingWxUser = false
		appid = ''
		agentid = ''
	}else if(e == 3){//广中医慢病-路径/mbh5
		domain = 'http://zwb1.gxzyy.com.cn/mb-api';//广中医慢病医院地址2024-6-19升级数据安全版本后修改为此域名
		socketUrl = 'ws://zwb1.gxzyy.com.cn/mb-ws';     //中医慢病医院环境socket
		if( window.location.protocol === 'https:'){
			domain = 'https://zwb1.gxzyy.com.cn/mb-api';
			socketUrl = 'wss://zwb1.gxzyy.com.cn/mb-ws';
		}
		ddId = ''
		bingingWxUser = true
		appid = 'wx51f5aff0f538ed8b'
		agentid = '1000066'
	}else if(e == 4){//南宁中医院
 	domain = 'http://gyjk.frp.starup.net.cn/prod-api';//南宁中医院医院正式环境
 	socketUrl = 'ws://gyjk.frp.starup.net.cn/ws';     //南宁中医院socket-医院正式环境
 	ddId = ''
 	bingingWxUser = false
	appid = ''
	agentid = ''
	}else if(e == 5){//百色中医院
 	domain = 'https://bzyzwb.vip.cpolar.cn/prod-api';
 	socketUrl = 'ws://bzyzwb.vip.cpolar.cn/ws';
 	ddId = ''
 	bingingWxUser = false
	appid = ''
	agentid = ''
	}else if(e == 6){//兴安界首骨伤医院
 	domain = 'https://zwb.jsgsyy.com:8061/prod-api';
 	socketUrl = 'wss://zwb.jsgsyy.com:8061/ws';
 	ddId = ''
 	bingingWxUser = false
	appid = ''
	agentid = ''
	}else if(e == 8){//河南周口市中医院
		domain = 'https://lszwb.frp.starup.net.cn/zk-api';
		socketUrl = 'wss://lszwb.frp.starup.net.cn/zk-ws';
		ddId = ''
		bingingWxUser = false
		appid = ''
		agentid = ''
	}
	return {
		domain,
		socketUrl,
		ddId,
		bingingWxUser,
		appid,
		agentid
	}
}
