<template>
	<view class="mx-20" style="height:100%;">
		<view class="grace-box-banner gui-flex l-timeline-b ">
			<guiNavigation :items="listtab" @change="gethealthyList" padding="10rpx" :size="110"
				:currentIndex="healthykey"></guiNavigation>
		</view>
		<scroll-view :scroll-y="healthykey==0?true:false" class="mt-20" style="height:calc(100% - 32px)">
			<!-- 舌面 -->
			<view v-if="healthykey == 0" style="height: 100%;">
				<view class="l-timeline-l " v-for="(item,index) in LingualList">
					<view class="time-line">
						<view class="l-icon"></view>
						<view class="time-line-i"></view>
					</view>
					<view style="flex: 1;padding-left: 20rpx;">
						<view class="l-time fs-26 visitDate-row">
							<view>
								{{item.measureTime}}
								<span class=" ml-20 fs-26">舌面</span>
							</view>
							<view class="gui-accordion-title gui-flex gui-rows gui-nowrap gui-align-items-center"
								@tap="changeTo(index)">
								<text class=" gui-color-blue fs-30">详情</text>
								<text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
									v-if="currentIndex != index">&#xe603;</text>
								<text class="gui-accordion-icon gui-block-text gui-icons gui-color-gray gui-text-right"
									v-else>&#xe654;</text>
							</view>
							<!-- <text class=" gui-color-blue fs-26 sm_xq"
							@click="tongue(item)">详情&ensp;></text> -->
						</view>
						<!-- <view class="l-icon"></view> -->
						<view class="l-content laioxiaoFu">
							<view class="gui-accordion" v-if="currentIndex == index">
								<view class="fs-24">
									<view class="gui-flex" style="flex-direction: column;">
										<view class="gui-flex img-shemian">
											<image class="img" @click="previewImage(e,item.lingualFiles.split(','))"
												v-for="(i,e) in item.lingualFiles.split(',')" :src="i" />

											<!-- <button class="fs-32  bcolor gui-color-white" @click=" tongue(item)">
											查看记录
										</button> -->
										</view>
										<view class="fs-28" style="line-height: 40rpx;padding: 0 20rpx 20rpx 20rpx;">
											{{item.lingualRemark}}
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 血糖 -->
			<view class="mt-10 pb-20" v-if="healthykey==1&&zeroChartData.categories.length>0">
				<view class="charts-name">血糖（单位：mmol/L）</view>
				<qiun-data-charts type="mix" :opts="zeroOpts" :chartData="zeroChartData"  :canvas2d="true" :ontouch="true" canvasId="0"/>
			</view>
			<!-- 计步 -->
			<view class="mt-10 pb-20" v-if="healthykey==2&&chartData.categories.length>0">
				<view class="charts-name">计步（单位：步）最近一天</view>
				<qiun-data-charts type="line" :opts="opts" :chartData="chartData"  :canvas2d="true" :ontouch="true" canvasId="1"/>
			</view>
			<!-- 心率 -->
			<view class="mt-10 pb-20" v-if="healthykey==3&&twoChartData.categories.length>0">
				<view class="charts-name">心率（单位：次/分）最近10次</view>
				<qiun-data-charts type="line" :opts="opts" :chartData="twoChartData" :canvas2d="true" :ontouch="true" canvasId="2" />
			</view>
			<!-- 睡眠 -->
			<view class="mt-10 pb-20" v-if="healthykey==4&&threeChartData.categories.length>0">
				<view class="charts-name">睡眠（单位：小时）最近1天</view>
				<qiun-data-charts type="column" :opts="threeOpts" :chartData="threeChartData"  :canvas2d="true" :ontouch="true" canvasId="3"/>
			</view>
			<!-- 血压 -->
			<view class="mt-10 pb-20" v-if="healthykey==5&&fourChartData.categories.length>0">
				<view class="charts-name">血压（单位：mmHg）最近10次</view>
				<qiun-data-charts type="area" :opts="optsFour" :chartData="fourChartData"  :canvas2d="true" :ontouch="true" canvasId="4"/>
			</view>
			<!-- 脉搏 -->
			<view class="mt-10 pb-20" v-if="healthykey==6&&fiveChartData.categories.length>0">
				<view class="charts-name">脉搏（单位：次/分）</view>
				<qiun-data-charts type="mix" :opts="zeroOpts" :chartData="fiveChartData"  :canvas2d="true" :ontouch="true" canvasId="5"/>
			</view>
			<!-- 呼吸 -->
			<view class="mt-10 pb-20" v-if="healthykey==7&&sevenChartData.categories.length>0">
				<view class="charts-name">呼吸（单位：次/分）</view>
				<qiun-data-charts type="mix" :opts="zeroOpts" :chartData="sevenChartData" :canvas2d="true" :ontouch="true" canvasId="6" />
			</view>
			<!-- 体温 -->
			<!-- v-if="healthykey==7&&sixChartData.categories.length>0" -->
			<view class="mt-10 pb-20" v-if="healthykey==8&&sixChartData.categories.length>0">
				<view class="charts-name" v-if="selectlist.length>0">
					<view>体温（单位：°C）</view>
					<w-select
					    style="margin-left: 20rpx;"
					    v-model='chooseValue'
						:defaultValue="chooseValue"
					    :list='selectlist'
					    valueName='name'
					    keyName="name"
					    @change='changePeriod'
					>
					</w-select>
				</view>
				<qiun-data-charts type="mix" :opts="zeroOpts" :chartData="sixChartData"  :canvas2d="true" :ontouch="true" canvasId="7"/>
				<view class="Instructions">标识说明：<view class="identification"></view>为经期</view>
			</view>
			<gui-empty v-if="(LingualList.length == 0 && healthykey == 8)">
				<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
					<!-- 请根据您的项目要求制作并更换为空图片 -->
					<image class="gui-empty-img"
						src="https://images.weserv.nl/?url=https://upload-images.jianshu.io/upload_images/15372054-1f849183cebb80b1.png">
					</image>
				</view>
				<text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top"
					style="color:#9DABFF;">暂无数据 ......</text>
			</gui-empty>
		</scroll-view>
	</view>
</template>

<script>
	import wSelect from '@/components/w-select/w-select.vue'
	import guiNavigation from './gui-navigation.vue'
	export default {
		components: {
			guiNavigation,
			wSelect
		},
		props: {
			info: {
				type: Object,
				default: {}
			},
			monitorId: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				chooseValue: '',
				selectlist: [],
				/* 健康数据预警 */
				indicatorInfo: [],
				currentIndex: 0,
				healthykey: 0, //坐标
				listtab: [
					{
						id: 7,
						name: '舌面',
						type: '10'
					},{
						id: 3,
						name: '血糖',
						type: '1'
					},
					{
						id: 8,
						name: '计步',
						type: '2'
					}, //字典没有
					{
						id: 9,
						name: '心率',
						type: '4'
					}, //字典没有
					{
						id: 10,
						name: '睡眠',
						type: '3'
					}, //字典没有
					{
						id: 1,
						name: '血压',
						type: '6'
					},
					{
						id: 2,
						name: '脉搏',
						type: '7'
					},
					{
						id: 6,
						name: '呼吸',
						type: '8'
					},
					{
						id: 5,
						name: '体温',
						type: '9'
					}
				],
				LingualList: [],
				deviceList: [],
				optsFour: {
					enableScroll: true,
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 20, 10, 10],
					legend: {
						show: false //是否显示图例
					},
					xAxis: {
						disableGrid: true,
						rotateLabel: true,
						rotateAngle:20,
						itemCount: 5
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						splitNumber: 5,
						gridType: "dash",
						dashLength: 4,
						padding: 10,
						showTitle: false
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						},
						mix: {
							column: {
								width: 20
							}
						},
						length: {
							width: 15
						}
					}
				},
				fourChartData: {
					"categories": [],
					"series": [{
						"name": "舒张压",
						"data": []
					}, {
						"name": "收缩压",
						"data": []
					}]
				},
				threeChartData: {
					"categories": [],
					"series": [{
						"name": "深睡",
						"data": []
					}, {
						"name": "浅睡",
						"data": []
					}, {
						"name": "清醒",
						"data": []
					}]
				},
				threeOpts: {
					enableScroll: true,
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 15, 0, 5],
					legend: {},
					xAxis: {
						disableGrid: true,
						itemCount: 5
					},
					yAxis: {
						data: [{
							min: 0
						}]
					},
					extra: {
						column: {
							type: "group",
							width: 30,
							activeBgColor: "#000000",
							activeBgOpacity: 0.08
						}
					}
				},
				/* 健康数据 */
				healthData: {
					/* 血糖 */
					sugar: {
						/* 最新血糖 */
						latestSugar: 0,
						/* 最新血糖 */
						latestTime: '00:00',
						/* 区间数据 */
						section: []
					},
					/* 计步 */
					threeStep: {
						/* 里程（公里） */
						mileage: 0,
						/* 目标步数 */
						targetSteps: 0,
						/* 平均步数 */
						averageSteps: 0,
						/* 消耗热量（大卡） */
						heatConsumption: 0,
						/* 当日总步数 */
						currentDayTotalSteps: 0,
						/* 步数区间值['00:00', '06:00', '12:00', '18:00', '24:00'] */
						section: [],
						/* 连接状态 */
						connection: false
					},
					/* 睡眠 */
					careReport: {
						/* 产生时间 */
						date: "",
						/* 入睡时间 */
						sleepDateTime: "",
						/* 入睡时长（分钟） */
						fallasleep: 0,
						/* 睡眠时长 （分钟） */
						sleepTime: 0,
						/* 休息时长 （分钟） */
						restTime: 0,
						/* 清醒时长 （分钟）*/
						awakeTime: 0,
						/* 浅睡时长 （分钟） */
						lightTime: 0,
						/* 浅睡时长 （分钟） */
						remTime: 0,
						/* 深睡时长 （分钟） */
						deepTime: 0,
						/* 睡眠时段 */
						bucket: "",
						/* 平均心率 */
						avghr: "",
						/* 平均呼吸率 */
						avgbr: "",
						/* 清醒百分比 */
						awakePer: "",
						/* rem百分比 */
						remPer: "",
						/* 浅睡百分比 */
						lightPer: "",
						/* 深睡百分比 */
						deepPer: "",
						/* 睡眠效率 */
						efficiency: "",
						/* 睡眠评分 */
						score: 0,
						/* 平均睡眠 */
						averageSleep: 0,
						/* 连接状态 */
						connection: false
					},
					/* 心率 */
					threeHeart: {
						/* 合计心率 */
						totalHeart: 0,
						/* 平均心率 */
						averageHeart: 0,
						/* 当前心率 */
						currentHeart: 0,
						/* 最高心率 */
						maxHeart: 0,
						/* 最低心率 */
						minHeart: 0,
						/* 心率区间 */
						section: [],
						/* 连接状态 */
						connection: false
					},
					/* 血压 */
					cholesterol: {
						/* 最新舒张压 */
						newDbpCholestero: 0,
						/* 最新收缩压 */
						newSbpCholestero: 0,
						/* 平均血压 */
						averageCholestero: 0,
						/* 最高舒张压 */
						maxDbpCholestero: 0,
						/* 最低舒张压 */
						minDbpCholestero: 0,
						/* 最高收缩压 */
						maxSbpCholestero: 0,
						/* 最低收缩压 */
						minSbpCholestero: 0,
						/* 血压区间 */
						section: []
					},
					/* 血氧 */
					oxygen: {}
				},
				opts: {
					enableScroll: true,
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 20, 10, 10],
					legend: {
						show: false //是否显示图例
					},
					xAxis: {
						disableGrid: true,
						rotateLabel: true,
						rotateAngle: 20,
						itemCount: 5
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						splitNumber: 5,
						gridType: "dash",
						dashLength: 4,
						padding: 10,
						showTitle: false
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						},

						length: {
							width: 15
						},

					}
				},
				sevenChartData:{
					"categories": [],
					"series": [{
						"name": "呼吸",
						"data": []
					}]
				},
				sixChartData: {
					"categories": [],
					"series": [{
						"name": "体温",
						"data": []
					}]
				},
				fiveChartData: {
					"categories": [],
					"series": [{
						"name": "脉搏",
						"data": []
					}]
				},
				zeroChartData: {
					"categories": [],
					"series": [{
						"name": "血糖",
						"data": []
					}]
				},
				chartData: {
					"categories": [],
					"series": [{
						"name": "计步",
						"data": []
					}]
				},
				twoChartData: {
					"categories": [],
					"series": [{
						"name": "心率",
						"data": []
					}]
				},
				zeroOpts: {
					enableScroll: true,
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [15, 20, 10, 10],
					legend: {
						show: false //是否显示图例
					},
					xAxis: {
						disableGrid: true,
						rotateLabel: true,
						rotateAngle: 20,
						itemCount: 5
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						splitNumber: 5,
						gridType: "dash",
						dashLength: 4,
						padding: 10,
						showTitle: false
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						},
						mix: {
							column: {
								width: 20
							}
						},
						length: {
							width: 15
						}
					}
				},
				/* 患者设备列表 */
				deviceList: [],
			}
		},
		watch: {
			info: {
				handler(newLength, oldLength) {
					if (this.monitorId) {
						this.listtab.forEach((items, index) => {
							if (items.id == this.monitorId) {
								this.healthykey = index
								this.gethealthyList(index)
							}
						})
					} else {
						this.gethealthyList(0)
					}
				},
				immediate: true
			}
		},
		mounted() {

		},
		methods: {
			changePeriod(e){
				this.bodytemperature(e)
			},
			previewImage(e, list) {
				uni.previewImage({
					current: e, // 当前显示图片的索引值
					urls: list, // 需要预览的图片列表，photoList要求必须是数组
				})
			},
			changeTo(e) {
				this.currentIndex = e;
			},
			tongue(e) {
				this.$emit('tongue', e)
			},
			gethealthyList(e) {
				this.healthykey = e
				if (this.listtab[e].id == 7) {
					// 舌面像
					this.getgetLingualList()
				} else {
					// 其他类型
					this.getDeviceDetailList()
				}
			},
			getDeviceDetailList() {
				this.$common.RequestData({
					url: this.$common.getDeviceDetailList + this.info.patientId,
					method: 'get',
				}, res => {
					this.deviceList = res?.data || []
					this.loadIndicatorList()
				}, false)
			},
			loadIndicatorList() {
				this.$common.RequestData({
					url: this.$common.indicatorList,
					method: 'get',
				}, res => {
					this.indicatorInfo = res?.data || []
					console.log('请求的参数====',this.listtab[this.healthykey].id)
					if (this.listtab[this.healthykey].id == 3) {
						// 血糖
						this.getHealthData()
					} else if (this.listtab[this.healthykey].id == 8 || this.listtab[this.healthykey].id == 9) {
						// 计步心率
						this.braceletgetHealthData()
					} else if (this.listtab[this.healthykey].id == 10) {
						// 睡眠
						this.sleep()
					} else if (this.listtab[this.healthykey].id == 1) {
						// 血压
						this.pressure()
					} else if (this.listtab[this.healthykey].id == 2) {
						// 脉搏
						this.sphygmus()
					}else if(this.listtab[this.healthykey].id == 5){
						// 体温是否是经期
						this.$common.RequestData({
							url: this.$common.getMenstrualCycle,
							data: {
								patientId: this.info.patientId,
							},
							method: 'get',
						}, res => {
							console.log(res,'dddddd')
							// 第一次访问默认第一个选项
							this.selectlist = res.data ||[]
							this.chooseValue = res.data.length>0?res.data[0].name:''
							// 体温
							this.bodytemperature(this.selectlist[0])
						})

					}else if(this.listtab[this.healthykey].id == 6){
						this.breath()
					}

				}, false)
			},
			// 呼吸
			breath(){
				this.$common.RequestData({
					url: this.$common.getHealthData,
					method: 'GET',
					data: {
						// 设备码 必填（血糖没有设备则填写患者id）
						iotSn:  this.info.idCard,
						// 患者id 必填（123为测试参数）
						patientId: this.info.patientId,
						// 查询日期yyyy-MM-dd，枚举：2022-10-18（可空，空则查询今天的数据）
						date: '',
						range: '10',
						// 健康数据类型 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] （可空，空则查询所有同时符合[iotSn、patientId、date]的健康数据）
						healthDataType: '8'
					}
				}, res => {
					const data = [],
						series = [],
						dataSpot = [],
						categories = [],
						indicator = this.indicatorInfo.find(item => item.indexType === 5)
					/* 处理展示数据 */
					res.data.breathing.forEach(item => {
						data.push(item.breathing)
						if (item.breathing > indicator?.alertMax) {
							dataSpot.push(item.breathing)
						} else if (item.breathing < indicator?.alertMin) {
							dataSpot.push(item.breathing)
						} else {
							dataSpot.push(null)
						}
						categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(
								/-/g, '/')),
							'{m}/{d} {h}:{i}'))
					})
					/* 补充数据 */
					series.push({
						name: "心率",
						type: "line",
						data: JSON.parse(JSON.stringify(data))
					})
					/* 处理预警 */
					data.forEach((item, index) => {
						if (item > indicator?.alertMax) {
							data[index] = null
							series.push({
								point: true,
								type: "point",
								data: dataSpot,
								color: "#f04864",
								textColor: "#f04864",
								format: 'linSpotFormatter'
							})
						} else if (item < indicator?.alertMin) {
							data[index] = null
							series.push({
								point: true,
								type: "point",
								data: dataSpot,
								color: "#f04864",
								textColor: "#f04864",
								format: 'linSpotFormatter'
							})
						}
					})
					this.$set(this, 'sevenChartData', {
						series: series,
						categories: categories
					})
				})
			},
			// 体温是否是经期
			// 体温
			bodytemperature(item){
				var url = this.selectlist.length>0?this.$common.getByPatientIdAndIsMenstruation : this.$common.getHealthData
				var data = this.selectlist.length>0?{
					patientId:this.info.patientId,
					startDate:item.startDate,
					endDate:item.endDate
				}:{
					iotSn: this.info.idCard,
					patientId: this.info.patientId,
					date: '',
					range: '10',
					healthDataType: this.listtab[this.healthykey].type
				}
				this.$common.RequestData({
					url:url,
					data: data,
					method: 'get',
				}, res => {
					console.log(res,'体温')
					const data = [],
						series = [],
						dataSpot = [],
						dataSpotlist = [],
						categories = [],
						resData = res.data?res.data.temperature? res.data.temperature : res.data :[]
						resData.forEach((item,index) => {
							dataSpotlist.push(item)
							data.push(item.temperature)
							if(item.isMenstruation == 1){
								dataSpot.push(item.temperature)
							}
							categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(
									/-/g, '/')),
								'{m}/{d} {h}:{i}'))
						})


					/* 补充数据 */
					series.push({
						name: "体温",
						type: "line",
						data: JSON.parse(JSON.stringify(data))
					})
					console.log(dataSpotlist)
					/* 处理预警 */
					dataSpotlist.forEach((item, index) => {
						if(item.isMenstruation == 1){
							data[index] = null
							series.push({
								point: true,
								type: "point",
								data: dataSpot,
								color: "#f04864",
								textColor: "#f04864",
								format: 'linSpotFormatter'
							})
						}
					})
					// dataSpot.forEach((item, index) => {
					// 	// if(item.isMenstruation == 1){
					// 		dataSpot[index] = null

					// 	// }
					// })
					this.$set(this, 'sixChartData', {
						series: series,
						categories: categories
					})
				})

			},
			// 脉搏
			sphygmus() {
				this.$common.RequestData({
					url: this.$common.getHealthData,
					data: {
						iotSn: this.info.idCard,
						patientId: this.info.patientId,
						date: '',
						range: '10',
						healthDataType: this.listtab[this.healthykey].type
					},
					method: 'get',
				}, res => {
					const data = [],
						series = [],
						dataSpot = [],
						categories = [],
						resData = res?.data,
						indicatorOne = this.indicatorInfo.find(item => item.indexType === 4)
					resData?.pulse.forEach(item => {
						data.push(item.pulse)
							if (item.pulse > indicatorOne?.alertMax) {
								dataSpot.push(item.pulse)
							} else {
								dataSpot.push(null)
							}

						categories.push(this.$common.parseTime(item.timeBegin,'{m}/{d} {h}:{i}'))
					})
					/* 补充数据 */
					series.push({
						name: "脉搏",
						type: "line",
						data: JSON.parse(JSON.stringify(data))
					})
					/* 处理预警 */
					data.forEach((item, index) => {

							if (item > indicatorOne?.alertMax) {
								data[index] = null
								series.push({
									point: true,
									type: "point",
									data: dataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							}

					})
					this.$set(this, 'fiveChartData', {
						series: series,
						categories: categories
					})
				})
			},
			// 血压
			pressure() {
				/* 血压计信息 */
				const sphygmomanometer = this.deviceList.find(device => Object.is(device.iotType, 3));
				console.log('sphygmomanometer====',this.deviceList)
				// if (sphygmomanometer) {2024-2-18 检查后发现不能正常显示数据，和梁应昌一起检查后决定隐藏该判断。
					this.$common.RequestData({
						url: this.$common.getHealthData,
						method: 'GET',
						data: {
							// 设备码 必填（血糖没有设备则填写患者id）
							iotSn: sphygmomanometer?sphygmomanometer.iotSn:'',
							// 患者id 必填（123为测试参数）
							patientId: this.info.patientId,
							// 查询日期yyyy-MM-dd，枚举：2022-10-18（可空，空则查询今天的数据）
							date: '',
							range: '10',
							// 健康数据类型 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] （可空，空则查询所有同时符合[iotSn、patientId、date]的健康数据）
							healthDataType: '6'
						}
					}, res => {
						const resData = res?.data
						this.$set(this.healthData, 'cholesterol', resData?.cholesterol || {})
						this.$set(this.healthData.cholesterol, 'connection', sphygmomanometer?.status &&
							true || false)
						// console.log("cholesterol", this.healthData.cholesterol)
						/* 血压 */
						const dbpData = [],
							sbpData = [],
							series = [],
							dbpDataSpot = [],
							sbpDataSpot = [],
							categories = [],
							/* 舒张压 */
							indicatorOne = this.indicatorInfo.find(item => item.indexType === 3 && item
								.bloodPressureType === 1),
							/* 收缩压 */
							indicatorTow = this.indicatorInfo.find(item => item.indexType === 3 && item
								.bloodPressureType === 2)
						/* 处理展示数据 */
						this.healthData.cholesterol.section.forEach(item => {
							dbpData.push(item.dbp)
							sbpData.push(item.sbp)
							if (item.dbp > indicatorOne?.alertMax) {
								dbpDataSpot.push(item.dbp)
							} else if (item.dbp < indicatorOne?.alertMin) {
								dbpDataSpot.push(item.dbp)
							} else {
								dbpDataSpot.push(null)
							}
							if (item.sbp > indicatorTow?.alertMax) {
								sbpDataSpot.push(item.sbp)
							} else if (item.sbp < indicatorTow?.alertMin) {
								sbpDataSpot.push(item.sbp)
							} else {
								sbpDataSpot.push(null)
							}
							categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(
									/-/g, '/')),
								'{m}/{d} {h}:{i}'))
						})
						/* 补充数据 */
						series.push({
							type: "line",
							name: "舒张压",
							data: JSON.parse(JSON.stringify(dbpData))
						}, {
							type: "line",
							name: "收缩压",
							data: JSON.parse(JSON.stringify(sbpData))
						})
						/* 处理预警 */
						dbpData.forEach((item, index) => {
							if (item > indicatorOne?.alertMax) {
								dbpData[index] = null
								series.push({
									point: true,
									type: "point",
									data: dbpDataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							} else if (item < indicatorOne?.alertMin) {
								dbpData[index] = null
								series.push({
									point: true,
									type: "point",
									data: dbpDataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							}
						})
						sbpData.forEach((item, index) => {
							if (item > indicatorTow?.alertMax) {
								sbpData[index] = null
								series.push({
									point: true,
									type: "point",
									data: sbpDataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							} else if (item < indicatorTow?.alertMin) {
								sbpData[index] = null
								series.push({
									point: true,
									type: "point",
									data: sbpDataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							}
						})
						this.$set(this, 'fourChartData', {
							series: series,
							categories: categories,
						})
					})
				// }
			},
			// 睡眠
			sleep() {
				/* 睡眠带信息 */
				const sleepingBand = this.deviceList.find(device => Object.is(device.iotType, 2));
				if (sleepingBand) {
					this.$common.RequestData({
						url: this.$common.getHealthData,
						method: 'GET',
						data: {
							// 设备码 必填（血糖没有设备则填写患者id）
							iotSn: sleepingBand.iotSn,
							// 患者id 必填（123为测试参数）
							patientId: this.info.patientId,
							// 查询日期yyyy-MM-dd，枚举：2022-10-18（可空，空则查询今天的数据）
							date: '',
							// 健康数据类型 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] （可空，空则查询所有同时符合[iotSn、patientId、date]的健康数据）
							healthDataType: '3'
						}
					}, res => {
						const resData = res?.data;
						this.$set(this.healthData, 'careReport', resData?.careReport || {})
						this.$set(this.healthData.careReport, 'connection', sleepingBand?.status && true ||
							false)
						// console.log("careReport", this.healthData.careReport)
						/* 睡眠 */
						this.$set(this, 'threeChartData', {
							categories: [this.healthData?.careReport
								?.sleepDateTime && this.$common.parseTime(new Date(this
									.healthData.careReport
									.sleepDateTime.replace(/-/g, '/')), '{m}/{d} {h}:{i}') ||
								''
							],
							series: [{
									name: "深睡",
									data: [this.healthData?.careReport?.deepTime]
								},
								{
									name: "浅睡",
									data: [this.healthData?.careReport?.lightTime]
								},
								{
									name: "清醒",
									data: [this.healthData?.careReport?.awakeTime]
								}
							]
						})
					})
				}
			},
			// 计步
			braceletgetHealthData() {
				console.log('666')
				/* 手环信息 */
				const bracelet = this.deviceList.find(device => Object.is(device.iotType, 1));
				/* 具备手环信息再查询健康数据 */
				console.log('666', bracelet, this.deviceList)
				if (bracelet) {
					this.$common.RequestData({
						url: this.$common.getHealthData,
						method: 'GET',
						data: {
							// 设备码 必填（血糖没有设备则填写患者id）
							iotSn: bracelet.iotSn,
							// 患者id 必填（123为测试参数）
							patientId: this.info.patientId,
							// 查询日期yyyy-MM-dd，枚举：2022-10-18（可空，空则查询今天的数据）
							date: '',
							range: '10',
							// 健康数据类型 包括[1.血糖 2.计步 3.睡眠 4.心率 5.血氧 6.血压] （可空，空则查询所有同时符合[iotSn、patientId、date]的健康数据）
							healthDataType: '2/4'
						}
					}, res => {
						const resData = res?.data;
						this.$set(this.healthData, 'threeStep', resData?.threeStep || {})
						this.$set(this.healthData, 'threeHeart', resData?.threeHeart || {})
						this.$set(this.healthData.threeStep, 'connection', bracelet?.status && true ||
							false)
						this.$set(this.healthData.threeHeart, 'connection', bracelet?.status && true ||
							false)
						// console.log("threeStep", this.healthData.threeStep)
						// console.log("threeHeart", this.healthData.threeHeart)
						/* 计步 */
						if (this.listtab[this.healthykey].id == 8) {
							const threeStep = this.healthData?.threeStep
							this.$set(this, 'chartData', {
								categories: [`${threeStep.date} 00:00`, `${threeStep.date} 06:00`,
									`${threeStep.date} 12:00`, `${threeStep.date} 18:00`,
									`${threeStep.date} 24:00`
								],
								series: [{
									name: "计步",
									data: threeStep?.section || [null, null,
										null, null, null
									]
								}]
							})
							return
						}

						/* 心率 */
						const data = [],
							series = [],
							dataSpot = [],
							categories = [],
							indicator = this.indicatorInfo.find(item => item.indexType === 2)
						/* 处理展示数据 */
						this.healthData.threeHeart.section.forEach(item => {
							data.push(item.heartrate)
							if (item.heartrate > indicator?.alertMax) {
								dataSpot.push(item.heartrate)
							} else if (item.heartrate < indicator?.alertMin) {
								dataSpot.push(item.heartrate)
							} else {
								dataSpot.push(null)
							}
							categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(
									/-/g, '/')),
								'{m}/{d} {h}:{i}'))
						})
						/* 补充数据 */
						series.push({
							name: "心率",
							type: "line",
							data: JSON.parse(JSON.stringify(data))
						})
						/* 处理预警 */
						data.forEach((item, index) => {
							if (item > indicator?.alertMax) {
								data[index] = null
								series.push({
									point: true,
									type: "point",
									data: dataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							} else if (item < indicator?.alertMin) {
								data[index] = null
								series.push({
									point: true,
									type: "point",
									data: dataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							}
						})
						this.$set(this, 'twoChartData', {
							series: series,
							categories: categories
						})
						// this.measureStatus = false
					})
				}
			},
			// 血糖
			getHealthData() {
				this.$common.RequestData({
					url: this.$common.getHealthData,
					data: {
						iotSn: this.info.idCard,
						patientId: this.info.patientId,
						date: '',
						healthDataType: this.listtab[this.healthykey].type
					},
					method: 'get',
				}, res => {
					const data = [],
						series = [],
						dataSpot = [],
						categories = [],
						resData = res?.data,
						/* 早餐前空腹 */
						indicatorOne = this.indicatorInfo.find(item => item.indexType === 1 && item
							.bloodSugarType === 1),
						/* 午/晚餐前后 */
						indicatorTow = this.indicatorInfo.find(item => item.indexType === 1 && item
							.bloodSugarType === 2)
					resData.sugar?.section.forEach(item => {
						data.push(item.bloodSugar)
						if (item.recordType === 1) {
							if (item.bloodSugar > indicatorOne?.alertMax) {
								dataSpot.push(item.bloodSugar)
							} else {
								dataSpot.push(null)
							}
						} else {
							if (item.bloodSugar > indicatorTow?.alertMax) {
								dataSpot.push(item.bloodSugar)
							} else {
								dataSpot.push(null)
							}
						}
						categories.push(this.$common.parseTime(new Date(item.timeBegin.replace(
								/-/g, '/')),
							'{m}/{d} {h}:{i}'))
					})
					/* 补充数据 */
					series.push({
						name: "血糖",
						type: "line",
						data: JSON.parse(JSON.stringify(data))
					})
					/* 处理预警 */
					data.forEach((item, index) => {
						if (index === 0) {
							if (item > indicatorOne?.alertMax) {
								data[index] = null
								series.push({
									point: true,
									type: "point",
									data: dataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							}
						} else {
							if (item > indicatorTow?.alertMax) {
								data[index] = null
								series.push({
									point: true,
									type: "point",
									data: dataSpot,
									color: "#f04864",
									textColor: "#f04864",
									format: 'linSpotFormatter'
								})
							}
						}
					})
					this.$set(this, 'zeroChartData', {
						series: series,
						categories: categories
					})
					console.log(this.zeroChartData)
				})
			},
			// 舌面
			getgetLingualList(e) {
				let _this = this;
				// this.info.patientId
				this.$common.RequestData({
					url: this.$common.getLingualList,
					data: {
						patientId: this.info.patientId
					},
					method: 'get'
				}, res => {
					console.log('舌面的数据===',res.rows)
					this.LingualList = res.rows
				})
			},
		}
	}
</script>
<style lang="scss" scoped>
	.Instructions{
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: row;
		margin-top: 40rpx;
		.identification{
			width: 20rpx;
			height: 20rpx;
			border-radius: 100%;
			margin-right: 20rpx;
			background-color: #f04864;
		}
	}
	.charts-name{
		color: #008AFF;
		margin: 20rpx 0;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
	}
	.gui-accordion {
		background-color: #f5f6f8;
		// padding:20rpx;
		border-radius: 10rpx;
	}

	.img-shemian {
		flex-wrap: wrap;
		margin: 10rpx;
	}

	.img {
		width: 150rpx;
		border-radius: 10rpx;
		height: 150rpx;
		margin: 10rpx;
	}

	.gui-rows-title {
		flex: 1
	}

	.time-line {
		display: flex;
		flex-direction: column;
		width: 30rpx;
		position: relative;
		justify-content: center;
		align-items: center;

		.time-line-i {
			width: 2rpx;
			background-color: #aaa;
			height: 100%;
		}
	}

	.visitDate-row {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}

	.navscroll {
		white-space: nowrap;

		// height: 100rpx;
		::v-deep ::-webkit-scrollbar {
			// width: 4px !important;
			// height: 1px !important;
			// overflow: auto !important;
			// background: transparent !important;
			// -webkit-appearance: auto !important;
			// display: block;
			display: none;
			width: 0;
			height: 0;
		}

		.garce-items {
			display: inline-block; // 设置为行内块
		}
	}

	.prescription {
		display: flex;
		flex-direction: row;
		flex-flow: row wrap;
	}

	.prescription-item {
		width: 30%;
		line-height: 26px;
		text-align: center;
	}

	>>>.gui-scroll-x-items {
		align-items: center;
	}

	.demo-nav {
		padding: 15rpx 30rpx;
	}

	.demo-text {
		line-height: 200rpx;
		padding-bottom: 3000px;
	}

	.grace-box-banner .garce-items .line2 {
		font-size: 28rpx;
		color: #008AFF;
		display: inline-block; // 设置为行内块
		border: 1px solid #62dbff;
		padding: 0 15rpx;
		margin: 0 10rpx;
		border-radius: 20rpx;
	}

	.line2.active {
		color: #ffffff !important;
		/* font-weight:bold; */
		background-color: #008AFF;
	}

	.l-timeline-l {
		display: flex;
		flex-direction: row;
		// border-left: 1px solid #aaaaaa;
	}

	.l-timeline-b {
		/* border-bottom: 2px solid #008AFF; */
	}

	.l-time {
		// position: relative;
		// top: -15rpx;
	}

	.acolor {
		background-color: #7784eb;

	}

	.bcolor {
		background-color: #7784eb;
		height: 40rpx;
		line-height: 40rpx;
		width: 190rpx;
	}

	.tagicon {
		margin-right: 10rpx;
		height: 40rpx;
		width: 6rpx;
		border-radius: 5rpx;
		background: #008AFF;
		display: block;
	}

	.l-icon {
		background: #008AFF;
		width: 25rpx;
		height: 25rpx;
		border-radius: 25rpx;
		position: absolute;
		top: 0;
		// position: relative;
		// top: -50rpx;
		// left: -15rpx;
	}

	.l-content {
		padding-bottom: 30px;
		margin: 30rpx 0;
	}

	.gui-accordion-icon {
		width: 50rpx;
		// height: 80rpx;
		// line-height: 80rpx;
		font-size: 32rpx;
	}

	.gui-flex-direction-row {
		flex-direction: row-reverse;
	}

	.gui-accordion-title-text {
		// width: 200rpx;
		flex: 1;
	}

	>>>.gui-block-text {
		font-size: 30rpx !important;
	}

	.resuimg {
		width: 24px;
		height: 24px;
		font-size: 24px;
		line-height: 24px;
		vertical-align: middle;
		color: rgba(0, 186, 173, 1);
	}

	.resutext {
		width: 70px;
		height: 21px;
		font-size: 14px;
		text-align: left;
		font-weight: bold;
		line-height: 24px;
		padding-left: 8rpx;
		color: rgba(80, 80, 80, 1);
	}

	.propose_net {
		width: 100%;
		height: auto;
		margin-top: 5rpx;
		padding: 0 10rpx;
		text-indent: 2em;
		line-height: 52rpx;
		overflow-x: scroll;
	}

	.end_text {
		width: 325px;
		height: 42px;
		font-size: 14px;
		text-align: center;
		margin: 20% auto 0;
		color: rgba(212, 48, 48, 1);
	}

	.propose {
		width: 92%;

	}


	/* 干预 */
	.ganyuFu {
		background-color: #f5f6f8;
		line-height: 50rpx;
		padding-top: 10rpx;
		width: 85%;
		max-width: 85%;
		border-radius: 10rpx;
		padding: 5rpx 10rpx 5rpx 5rpx;
	}

	.ios {
		font-size: 35rpx;
		margin: 10rpx 10rpx;
		color: #1aca0d;
	}

	.laioxiaoZi {
		display: flex;
		align-items: center;
		line-height: 50rpx;
		padding: 10rpx 0rpx 10rpx 15rpx;
	}

	.Aios {
		font-size: 50rpx;
		margin: 10rpx 20rpx 10rpx 10rpx;
		color: #1aca0d;
		/* height: 100% !important; */
		display: flex;
		align-items: center;
		float: left;
	}

	.yuandian {
		float: left;
		display: block;
		line-height: 50rpx;
		top: 10px;
		width: 8px;
		height: 8px;
		border-radius: 20px;
		/* background: #cbd0db; */
		background: #6f6f6f;
		margin-top: 8px !important;
		margin: 8px;

	}

	/* 舌面详情按钮 */
	.sm_xq {
		float: right;
		margin-right: 30rpx;
	}

	/* 头像 */
	.head_img {
		border-radius: 10rpx;
		border: 1px solid #cdcdcd;
	}

	>>>.gui-list-title-text {
		font-size: 28rpx !important;
	}
</style>
