<template>
	<!-- 居民中医体质记录 -->
	<view class="p-20 fs-32">
		<view class="mt-20 mb-50">
			<view class="d-flex mb-20">
				<view>姓名：{{info.name}}</view>
				<view class="ml-120">性别：{{info.sex == 1 ? "男" : "女"}}</view>
			</view>
			<view>时间：{{obj.create_time ? $common.parseTime(obj.create_time) : '-'}}</view>
		</view>
		<view v-for="(item,index) in list" :key="index" class="mb-40">
			<view>{{item.timu}}</view>
			<view class="pt-30 text-grey-74">{{obj[item.key] || '-'}}</view>
		</view>
	</view>
</template>

<script>
	// import {getInfoMedical} from '@/api/home.js'
	export default {
	  name: 'jmzy',
		props:['templateId','templateDictKey','patientId','info','visitRecordId'],
		data(){
			return{
				cardObj:uni.getStorageSync('mbcardObj'),
				obj:{},
				list:[
					{key:"physical",timu:"A01. 请选择患者的中医普通体质",type:"checkbox",xx:'平和质,气虚质,阳虚质,阴虚质,湿热质,气郁质,痰湿质,血瘀质,特禀质,复合型体质',value:[]},
					{key:"transport",timu:"B11. 请选择患者的五行十态体质-中运",type:"radio",xx:'土运太过,土运不及,金运太过,金运不及,水运太过,水运不及,木运太过,木运不及,火运太过,火运不及',value:""},
					{key:"dayGas",timu:"B12. 请选择患者的五行十态体质-司天之气",type:"radio",xx:'厥阴风木,少阴君火,少阳相火,阳明燥金,太阳寒水,太阴湿土',value:""},
					{key:"springGas",timu:"B13. 请选择患者的五行十态体质-在泉之气",type:"radio",xx:'厥阴风木,少阴君火,少阳相火,阳明燥金,太阳寒水,太阴湿土',value:""},
					{key:"mainGas",timu:"B14. 请选择患者的五行十态体质-主气",type:"radio",xx:'厥阴风木,少阴君火,少阳相火,阳明燥金,太阳寒水,太阴湿土',value:""},
					{key:"politeGas",timu:"B15. 请选择患者的五行十态体质-客气",type:"radio",xx:'厥阴风木,少阴君火,少阳相火,阳明燥金,太阳寒水,太阴湿土',value:""},
					{key:"constitution",timu:"B16. 请选择患者的五行十态体质分类",type:"radio",xx:'土运太过,土运不及,金运太过,金运不及,水运太过,水运不及,木运太过,木运不及,火运太过,火运不及',value:""},
					{key:"comprehensive",timu:"B17. 综合结论",type:"textarea",xx:'',value:""},
				],
			}
		},
		watch:{
			templateDictKey:{
				handler (newLength, oldLength) {
					if( newLength == 2 ){
						this.getTzInfo();
					}
				},
				immediate: true
			}
			// templateDictKey(news,olds){
			// 	if( news == 2 ){
			// 		this.getTzInfo();
			// 	}
			// }
		},
		methods:{
			// 获取体质信息
			getTzInfo(){
				this.$common.RequestData({
					url: this.$common.dpMedical,
					data:{
						patienId:this.patientId,
						id:this.visitRecordId,
						dictValue:this.templateDictKey
					},
					method: "post"
				}, res => {
					for(var key in res.data){
						if(this.camelCase(key)){
							res.data[this.camelCase(key)] = res.data[key];
						}
					}
					this.obj = res.data;
					// this.getInit();
				},true,fail=>{
					
				})
				// getInfoMedical({
				// 	templateId:this.templateId,
				// 	templateKey: Number(this.templateDictKey)
				// }).then(res=>{
				// 	for(var key in res.data){
				// 		if(this.camelCase(key)){
				// 			res.data[this.camelCase(key)] = res.data[key];
				// 		}
				// 	}
				// 	this.obj = res.data;
				// })
			},
			// 下划线转驼峰
			camelCase(str) {
			   return str.replace(/_([a-z])/g, function(all, letter) {
			    return letter.toUpperCase();
			  })
			}
		}
	}
</script>

<style>
	.tle {
		width: 10%;
		border: 1px solid #f2f3f4;
		text-align: center;
		writing-mode: vertical-lr;/* 从左向右 从右向左是 writing-mode: vertical-rl;*/
		writing-mode: tb-lr;/*IE浏览器的从左向右 从右向左是 writing-mode: tb-rl；*/
		letter-spacing: 10rpx;
		line-height: 70rpx;
		/* font-size: 28rpx; */
	}

	.gui-td {
		width: 100rpx;
		flex: 1;
		overflow: hidden;
		padding: 20rpx 10rpx;
		display: flexbox;
	}

	.gui-td-text {
		line-height: 40rpx !important;
		/* font-size: 24rpx; */
	}
</style>
