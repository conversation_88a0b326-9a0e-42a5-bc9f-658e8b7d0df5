{"_from": "@babel/types@^7.27.3", "_id": "@babel/types@7.27.6", "_inBundle": false, "_integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "_location": "/@babel/types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/types@^7.27.3", "name": "@babel/types", "escapedName": "@babel%2ftypes", "scope": "@babel", "rawSpec": "^7.27.3", "saveSpec": null, "fetchSpec": "^7.27.3"}, "_requiredBy": ["/@vue/compiler-sfc/@babel/parser", "/@vue/server-renderer/@babel/parser", "/vue/@babel/parser"], "_resolved": "https://registry.npmmirror.com/@babel/types/-/types-7.27.6.tgz", "_shasum": "a434ca7add514d4e646c80f7375c0aa2befc5535", "_spec": "@babel/types@^7.27.3", "_where": "E:\\xmkj\\zwb\\合并版（广、灵、贵）\\zwb-doctor-uniapp\\node_modules\\@vue\\compiler-sfc\\node_modules\\@babel\\parser", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "deprecated": false, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "devDependencies": {"@babel/generator": "^7.27.5", "@babel/parser": "^7.27.5", "glob": "^7.2.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-types", "license": "MIT", "main": "./lib/index.js", "name": "@babel/types", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-types"}, "type": "commonjs", "types": "./lib/index-legacy.d.ts", "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "version": "7.27.6"}