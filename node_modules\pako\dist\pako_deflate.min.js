/*! pako 2.1.0 https://github.com/nodeca/pako @license (MIT AND Zlib) */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).pako={})}(this,(function(t){"use strict";function e(t){let e=t.length;for(;--e>=0;)t[e]=0}const a=256,s=286,n=30,r=15,i=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),_=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),l=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),h=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),o=new Array(576);e(o);const d=new Array(60);e(d);const u=new Array(512);e(u);const f=new Array(256);e(f);const c=new Array(29);e(c);const p=new Array(n);function g(t,e,a,s,n){this.static_tree=t,this.extra_bits=e,this.extra_base=a,this.elems=s,this.max_length=n,this.has_stree=t&&t.length}let w,m,b;function y(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}e(p);const v=t=>t<256?u[t]:u[256+(t>>>7)],z=(t,e)=>{t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255},k=(t,e,a)=>{t.bi_valid>16-a?(t.bi_buf|=e<<t.bi_valid&65535,z(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=a-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=a)},x=(t,e,a)=>{k(t,a[2*e],a[2*e+1])},A=(t,e)=>{let a=0;do{a|=1&t,t>>>=1,a<<=1}while(--e>0);return a>>>1},E=(t,e,a)=>{const s=new Array(16);let n,i,_=0;for(n=1;n<=r;n++)_=_+a[n-1]<<1,s[n]=_;for(i=0;i<=e;i++){let e=t[2*i+1];0!==e&&(t[2*i]=A(s[e]++,e))}},Z=t=>{let e;for(e=0;e<s;e++)t.dyn_ltree[2*e]=0;for(e=0;e<n;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.sym_next=t.matches=0},U=t=>{t.bi_valid>8?z(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0},R=(t,e,a,s)=>{const n=2*e,r=2*a;return t[n]<t[r]||t[n]===t[r]&&s[e]<=s[a]},S=(t,e,a)=>{const s=t.heap[a];let n=a<<1;for(;n<=t.heap_len&&(n<t.heap_len&&R(e,t.heap[n+1],t.heap[n],t.depth)&&n++,!R(e,s,t.heap[n],t.depth));)t.heap[a]=t.heap[n],a=n,n<<=1;t.heap[a]=s},T=(t,e,s)=>{let n,r,l,h,o=0;if(0!==t.sym_next)do{n=255&t.pending_buf[t.sym_buf+o++],n+=(255&t.pending_buf[t.sym_buf+o++])<<8,r=t.pending_buf[t.sym_buf+o++],0===n?x(t,r,e):(l=f[r],x(t,l+a+1,e),h=i[l],0!==h&&(r-=c[l],k(t,r,h)),n--,l=v(n),x(t,l,s),h=_[l],0!==h&&(n-=p[l],k(t,n,h)))}while(o<t.sym_next);x(t,256,e)},L=(t,e)=>{const a=e.dyn_tree,s=e.stat_desc.static_tree,n=e.stat_desc.has_stree,i=e.stat_desc.elems;let _,l,h,o=-1;for(t.heap_len=0,t.heap_max=573,_=0;_<i;_++)0!==a[2*_]?(t.heap[++t.heap_len]=o=_,t.depth[_]=0):a[2*_+1]=0;for(;t.heap_len<2;)h=t.heap[++t.heap_len]=o<2?++o:0,a[2*h]=1,t.depth[h]=0,t.opt_len--,n&&(t.static_len-=s[2*h+1]);for(e.max_code=o,_=t.heap_len>>1;_>=1;_--)S(t,a,_);h=i;do{_=t.heap[1],t.heap[1]=t.heap[t.heap_len--],S(t,a,1),l=t.heap[1],t.heap[--t.heap_max]=_,t.heap[--t.heap_max]=l,a[2*h]=a[2*_]+a[2*l],t.depth[h]=(t.depth[_]>=t.depth[l]?t.depth[_]:t.depth[l])+1,a[2*_+1]=a[2*l+1]=h,t.heap[1]=h++,S(t,a,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],((t,e)=>{const a=e.dyn_tree,s=e.max_code,n=e.stat_desc.static_tree,i=e.stat_desc.has_stree,_=e.stat_desc.extra_bits,l=e.stat_desc.extra_base,h=e.stat_desc.max_length;let o,d,u,f,c,p,g=0;for(f=0;f<=r;f++)t.bl_count[f]=0;for(a[2*t.heap[t.heap_max]+1]=0,o=t.heap_max+1;o<573;o++)d=t.heap[o],f=a[2*a[2*d+1]+1]+1,f>h&&(f=h,g++),a[2*d+1]=f,d>s||(t.bl_count[f]++,c=0,d>=l&&(c=_[d-l]),p=a[2*d],t.opt_len+=p*(f+c),i&&(t.static_len+=p*(n[2*d+1]+c)));if(0!==g){do{for(f=h-1;0===t.bl_count[f];)f--;t.bl_count[f]--,t.bl_count[f+1]+=2,t.bl_count[h]--,g-=2}while(g>0);for(f=h;0!==f;f--)for(d=t.bl_count[f];0!==d;)u=t.heap[--o],u>s||(a[2*u+1]!==f&&(t.opt_len+=(f-a[2*u+1])*a[2*u],a[2*u+1]=f),d--)}})(t,e),E(a,o,t.bl_count)},F=(t,e,a)=>{let s,n,r=-1,i=e[1],_=0,l=7,h=4;for(0===i&&(l=138,h=3),e[2*(a+1)+1]=65535,s=0;s<=a;s++)n=i,i=e[2*(s+1)+1],++_<l&&n===i||(_<h?t.bl_tree[2*n]+=_:0!==n?(n!==r&&t.bl_tree[2*n]++,t.bl_tree[32]++):_<=10?t.bl_tree[34]++:t.bl_tree[36]++,_=0,r=n,0===i?(l=138,h=3):n===i?(l=6,h=3):(l=7,h=4))},O=(t,e,a)=>{let s,n,r=-1,i=e[1],_=0,l=7,h=4;for(0===i&&(l=138,h=3),s=0;s<=a;s++)if(n=i,i=e[2*(s+1)+1],!(++_<l&&n===i)){if(_<h)do{x(t,n,t.bl_tree)}while(0!=--_);else 0!==n?(n!==r&&(x(t,n,t.bl_tree),_--),x(t,16,t.bl_tree),k(t,_-3,2)):_<=10?(x(t,17,t.bl_tree),k(t,_-3,3)):(x(t,18,t.bl_tree),k(t,_-11,7));_=0,r=n,0===i?(l=138,h=3):n===i?(l=6,h=3):(l=7,h=4)}};let D=!1;const N=(t,e,a,s)=>{k(t,0+(s?1:0),3),U(t),z(t,a),z(t,~a),a&&t.pending_buf.set(t.window.subarray(e,e+a),t.pending),t.pending+=a};var I=(t,e,s,n)=>{let r,i,_=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=(t=>{let e,s=4093624447;for(e=0;e<=31;e++,s>>>=1)if(1&s&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<a;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0})(t)),L(t,t.l_desc),L(t,t.d_desc),_=(t=>{let e;for(F(t,t.dyn_ltree,t.l_desc.max_code),F(t,t.dyn_dtree,t.d_desc.max_code),L(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*h[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e})(t),r=t.opt_len+3+7>>>3,i=t.static_len+3+7>>>3,i<=r&&(r=i)):r=i=s+5,s+4<=r&&-1!==e?N(t,e,s,n):4===t.strategy||i===r?(k(t,2+(n?1:0),3),T(t,o,d)):(k(t,4+(n?1:0),3),((t,e,a,s)=>{let n;for(k(t,e-257,5),k(t,a-1,5),k(t,s-4,4),n=0;n<s;n++)k(t,t.bl_tree[2*h[n]+1],3);O(t,t.dyn_ltree,e-1),O(t,t.dyn_dtree,a-1)})(t,t.l_desc.max_code+1,t.d_desc.max_code+1,_+1),T(t,t.dyn_ltree,t.dyn_dtree)),Z(t),n&&U(t)},C={_tr_init:t=>{D||((()=>{let t,e,a,h,y;const v=new Array(16);for(a=0,h=0;h<28;h++)for(c[h]=a,t=0;t<1<<i[h];t++)f[a++]=h;for(f[a-1]=h,y=0,h=0;h<16;h++)for(p[h]=y,t=0;t<1<<_[h];t++)u[y++]=h;for(y>>=7;h<n;h++)for(p[h]=y<<7,t=0;t<1<<_[h]-7;t++)u[256+y++]=h;for(e=0;e<=r;e++)v[e]=0;for(t=0;t<=143;)o[2*t+1]=8,t++,v[8]++;for(;t<=255;)o[2*t+1]=9,t++,v[9]++;for(;t<=279;)o[2*t+1]=7,t++,v[7]++;for(;t<=287;)o[2*t+1]=8,t++,v[8]++;for(E(o,287,v),t=0;t<n;t++)d[2*t+1]=5,d[2*t]=A(t,5);w=new g(o,i,257,s,r),m=new g(d,_,0,n,r),b=new g(new Array(0),l,0,19,7)})(),D=!0),t.l_desc=new y(t.dyn_ltree,w),t.d_desc=new y(t.dyn_dtree,m),t.bl_desc=new y(t.bl_tree,b),t.bi_buf=0,t.bi_valid=0,Z(t)},_tr_stored_block:N,_tr_flush_block:I,_tr_tally:(t,e,s)=>(t.pending_buf[t.sym_buf+t.sym_next++]=e,t.pending_buf[t.sym_buf+t.sym_next++]=e>>8,t.pending_buf[t.sym_buf+t.sym_next++]=s,0===e?t.dyn_ltree[2*s]++:(t.matches++,e--,t.dyn_ltree[2*(f[s]+a+1)]++,t.dyn_dtree[2*v(e)]++),t.sym_next===t.sym_end),_tr_align:t=>{k(t,2,3),x(t,256,o),(t=>{16===t.bi_valid?(z(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)})(t)}};var B=(t,e,a,s)=>{let n=65535&t|0,r=t>>>16&65535|0,i=0;for(;0!==a;){i=a>2e3?2e3:a,a-=i;do{n=n+e[s++]|0,r=r+n|0}while(--i);n%=65521,r%=65521}return n|r<<16|0};const H=new Uint32Array((()=>{let t,e=[];for(var a=0;a<256;a++){t=a;for(var s=0;s<8;s++)t=1&t?3988292384^t>>>1:t>>>1;e[a]=t}return e})());var M=(t,e,a,s)=>{const n=H,r=s+a;t^=-1;for(let a=s;a<r;a++)t=t>>>8^n[255&(t^e[a])];return-1^t},P={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},j={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};const{_tr_init:K,_tr_stored_block:Y,_tr_flush_block:G,_tr_tally:X,_tr_align:W}=C,{Z_NO_FLUSH:q,Z_PARTIAL_FLUSH:J,Z_FULL_FLUSH:Q,Z_FINISH:V,Z_BLOCK:$,Z_OK:tt,Z_STREAM_END:et,Z_STREAM_ERROR:at,Z_DATA_ERROR:st,Z_BUF_ERROR:nt,Z_DEFAULT_COMPRESSION:rt,Z_FILTERED:it,Z_HUFFMAN_ONLY:_t,Z_RLE:lt,Z_FIXED:ht,Z_DEFAULT_STRATEGY:ot,Z_UNKNOWN:dt,Z_DEFLATED:ut}=j,ft=258,ct=262,pt=42,gt=113,wt=666,mt=(t,e)=>(t.msg=P[e],e),bt=t=>2*t-(t>4?9:0),yt=t=>{let e=t.length;for(;--e>=0;)t[e]=0},vt=t=>{let e,a,s,n=t.w_size;e=t.hash_size,s=e;do{a=t.head[--s],t.head[s]=a>=n?a-n:0}while(--e);e=n,s=e;do{a=t.prev[--s],t.prev[s]=a>=n?a-n:0}while(--e)};let zt=(t,e,a)=>(e<<t.hash_shift^a)&t.hash_mask;const kt=t=>{const e=t.state;let a=e.pending;a>t.avail_out&&(a=t.avail_out),0!==a&&(t.output.set(e.pending_buf.subarray(e.pending_out,e.pending_out+a),t.next_out),t.next_out+=a,e.pending_out+=a,t.total_out+=a,t.avail_out-=a,e.pending-=a,0===e.pending&&(e.pending_out=0))},xt=(t,e)=>{G(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,kt(t.strm)},At=(t,e)=>{t.pending_buf[t.pending++]=e},Et=(t,e)=>{t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e},Zt=(t,e,a,s)=>{let n=t.avail_in;return n>s&&(n=s),0===n?0:(t.avail_in-=n,e.set(t.input.subarray(t.next_in,t.next_in+n),a),1===t.state.wrap?t.adler=B(t.adler,e,n,a):2===t.state.wrap&&(t.adler=M(t.adler,e,n,a)),t.next_in+=n,t.total_in+=n,n)},Ut=(t,e)=>{let a,s,n=t.max_chain_length,r=t.strstart,i=t.prev_length,_=t.nice_match;const l=t.strstart>t.w_size-ct?t.strstart-(t.w_size-ct):0,h=t.window,o=t.w_mask,d=t.prev,u=t.strstart+ft;let f=h[r+i-1],c=h[r+i];t.prev_length>=t.good_match&&(n>>=2),_>t.lookahead&&(_=t.lookahead);do{if(a=e,h[a+i]===c&&h[a+i-1]===f&&h[a]===h[r]&&h[++a]===h[r+1]){r+=2,a++;do{}while(h[++r]===h[++a]&&h[++r]===h[++a]&&h[++r]===h[++a]&&h[++r]===h[++a]&&h[++r]===h[++a]&&h[++r]===h[++a]&&h[++r]===h[++a]&&h[++r]===h[++a]&&r<u);if(s=ft-(u-r),r=u-ft,s>i){if(t.match_start=e,i=s,s>=_)break;f=h[r+i-1],c=h[r+i]}}}while((e=d[e&o])>l&&0!=--n);return i<=t.lookahead?i:t.lookahead},Rt=t=>{const e=t.w_size;let a,s,n;do{if(s=t.window_size-t.lookahead-t.strstart,t.strstart>=e+(e-ct)&&(t.window.set(t.window.subarray(e,e+e-s),0),t.match_start-=e,t.strstart-=e,t.block_start-=e,t.insert>t.strstart&&(t.insert=t.strstart),vt(t),s+=e),0===t.strm.avail_in)break;if(a=Zt(t.strm,t.window,t.strstart+t.lookahead,s),t.lookahead+=a,t.lookahead+t.insert>=3)for(n=t.strstart-t.insert,t.ins_h=t.window[n],t.ins_h=zt(t,t.ins_h,t.window[n+1]);t.insert&&(t.ins_h=zt(t,t.ins_h,t.window[n+3-1]),t.prev[n&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=n,n++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<ct&&0!==t.strm.avail_in)},St=(t,e)=>{let a,s,n,r=t.pending_buf_size-5>t.w_size?t.w_size:t.pending_buf_size-5,i=0,_=t.strm.avail_in;do{if(a=65535,n=t.bi_valid+42>>3,t.strm.avail_out<n)break;if(n=t.strm.avail_out-n,s=t.strstart-t.block_start,a>s+t.strm.avail_in&&(a=s+t.strm.avail_in),a>n&&(a=n),a<r&&(0===a&&e!==V||e===q||a!==s+t.strm.avail_in))break;i=e===V&&a===s+t.strm.avail_in?1:0,Y(t,0,0,i),t.pending_buf[t.pending-4]=a,t.pending_buf[t.pending-3]=a>>8,t.pending_buf[t.pending-2]=~a,t.pending_buf[t.pending-1]=~a>>8,kt(t.strm),s&&(s>a&&(s=a),t.strm.output.set(t.window.subarray(t.block_start,t.block_start+s),t.strm.next_out),t.strm.next_out+=s,t.strm.avail_out-=s,t.strm.total_out+=s,t.block_start+=s,a-=s),a&&(Zt(t.strm,t.strm.output,t.strm.next_out,a),t.strm.next_out+=a,t.strm.avail_out-=a,t.strm.total_out+=a)}while(0===i);return _-=t.strm.avail_in,_&&(_>=t.w_size?(t.matches=2,t.window.set(t.strm.input.subarray(t.strm.next_in-t.w_size,t.strm.next_in),0),t.strstart=t.w_size,t.insert=t.strstart):(t.window_size-t.strstart<=_&&(t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,t.insert>t.strstart&&(t.insert=t.strstart)),t.window.set(t.strm.input.subarray(t.strm.next_in-_,t.strm.next_in),t.strstart),t.strstart+=_,t.insert+=_>t.w_size-t.insert?t.w_size-t.insert:_),t.block_start=t.strstart),t.high_water<t.strstart&&(t.high_water=t.strstart),i?4:e!==q&&e!==V&&0===t.strm.avail_in&&t.strstart===t.block_start?2:(n=t.window_size-t.strstart,t.strm.avail_in>n&&t.block_start>=t.w_size&&(t.block_start-=t.w_size,t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,n+=t.w_size,t.insert>t.strstart&&(t.insert=t.strstart)),n>t.strm.avail_in&&(n=t.strm.avail_in),n&&(Zt(t.strm,t.window,t.strstart,n),t.strstart+=n,t.insert+=n>t.w_size-t.insert?t.w_size-t.insert:n),t.high_water<t.strstart&&(t.high_water=t.strstart),n=t.bi_valid+42>>3,n=t.pending_buf_size-n>65535?65535:t.pending_buf_size-n,r=n>t.w_size?t.w_size:n,s=t.strstart-t.block_start,(s>=r||(s||e===V)&&e!==q&&0===t.strm.avail_in&&s<=n)&&(a=s>n?n:s,i=e===V&&0===t.strm.avail_in&&a===s?1:0,Y(t,t.block_start,a,i),t.block_start+=a,kt(t.strm)),i?3:1)},Tt=(t,e)=>{let a,s;for(;;){if(t.lookahead<ct){if(Rt(t),t.lookahead<ct&&e===q)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=zt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==a&&t.strstart-a<=t.w_size-ct&&(t.match_length=Ut(t,a)),t.match_length>=3)if(s=X(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do{t.strstart++,t.ins_h=zt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!=--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=zt(t,t.ins_h,t.window[t.strstart+1]);else s=X(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(s&&(xt(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,e===V?(xt(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(xt(t,!1),0===t.strm.avail_out)?1:2},Lt=(t,e)=>{let a,s,n;for(;;){if(t.lookahead<ct){if(Rt(t),t.lookahead<ct&&e===q)return 1;if(0===t.lookahead)break}if(a=0,t.lookahead>=3&&(t.ins_h=zt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==a&&t.prev_length<t.max_lazy_match&&t.strstart-a<=t.w_size-ct&&(t.match_length=Ut(t,a),t.match_length<=5&&(t.strategy===it||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){n=t.strstart+t.lookahead-3,s=X(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=n&&(t.ins_h=zt(t,t.ins_h,t.window[t.strstart+3-1]),a=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!=--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,s&&(xt(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if(s=X(t,0,t.window[t.strstart-1]),s&&xt(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(s=X(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,e===V?(xt(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(xt(t,!1),0===t.strm.avail_out)?1:2};function Ft(t,e,a,s,n){this.good_length=t,this.max_lazy=e,this.nice_length=a,this.max_chain=s,this.func=n}const Ot=[new Ft(0,0,0,0,St),new Ft(4,4,8,4,Tt),new Ft(4,5,16,8,Tt),new Ft(4,6,32,32,Tt),new Ft(4,4,16,16,Lt),new Ft(8,16,32,32,Lt),new Ft(8,16,128,128,Lt),new Ft(8,32,128,256,Lt),new Ft(32,128,258,1024,Lt),new Ft(32,258,258,4096,Lt)];function Dt(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=ut,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),yt(this.dyn_ltree),yt(this.dyn_dtree),yt(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),yt(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),yt(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}const Nt=t=>{if(!t)return 1;const e=t.state;return!e||e.strm!==t||e.status!==pt&&57!==e.status&&69!==e.status&&73!==e.status&&91!==e.status&&103!==e.status&&e.status!==gt&&e.status!==wt?1:0},It=t=>{if(Nt(t))return mt(t,at);t.total_in=t.total_out=0,t.data_type=dt;const e=t.state;return e.pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=2===e.wrap?57:e.wrap?pt:gt,t.adler=2===e.wrap?0:1,e.last_flush=-2,K(e),tt},Ct=t=>{const e=It(t);var a;return e===tt&&((a=t.state).window_size=2*a.w_size,yt(a.head),a.max_lazy_match=Ot[a.level].max_lazy,a.good_match=Ot[a.level].good_length,a.nice_match=Ot[a.level].nice_length,a.max_chain_length=Ot[a.level].max_chain,a.strstart=0,a.block_start=0,a.lookahead=0,a.insert=0,a.match_length=a.prev_length=2,a.match_available=0,a.ins_h=0),e},Bt=(t,e,a,s,n,r)=>{if(!t)return at;let i=1;if(e===rt&&(e=6),s<0?(i=0,s=-s):s>15&&(i=2,s-=16),n<1||n>9||a!==ut||s<8||s>15||e<0||e>9||r<0||r>ht||8===s&&1!==i)return mt(t,at);8===s&&(s=9);const _=new Dt;return t.state=_,_.strm=t,_.status=pt,_.wrap=i,_.gzhead=null,_.w_bits=s,_.w_size=1<<_.w_bits,_.w_mask=_.w_size-1,_.hash_bits=n+7,_.hash_size=1<<_.hash_bits,_.hash_mask=_.hash_size-1,_.hash_shift=~~((_.hash_bits+3-1)/3),_.window=new Uint8Array(2*_.w_size),_.head=new Uint16Array(_.hash_size),_.prev=new Uint16Array(_.w_size),_.lit_bufsize=1<<n+6,_.pending_buf_size=4*_.lit_bufsize,_.pending_buf=new Uint8Array(_.pending_buf_size),_.sym_buf=_.lit_bufsize,_.sym_end=3*(_.lit_bufsize-1),_.level=e,_.strategy=r,_.method=a,Ct(t)};var Ht={deflateInit:(t,e)=>Bt(t,e,ut,15,8,ot),deflateInit2:Bt,deflateReset:Ct,deflateResetKeep:It,deflateSetHeader:(t,e)=>Nt(t)||2!==t.state.wrap?at:(t.state.gzhead=e,tt),deflate:(t,e)=>{if(Nt(t)||e>$||e<0)return t?mt(t,at):at;const a=t.state;if(!t.output||0!==t.avail_in&&!t.input||a.status===wt&&e!==V)return mt(t,0===t.avail_out?nt:at);const s=a.last_flush;if(a.last_flush=e,0!==a.pending){if(kt(t),0===t.avail_out)return a.last_flush=-1,tt}else if(0===t.avail_in&&bt(e)<=bt(s)&&e!==V)return mt(t,nt);if(a.status===wt&&0!==t.avail_in)return mt(t,nt);if(a.status===pt&&0===a.wrap&&(a.status=gt),a.status===pt){let e=ut+(a.w_bits-8<<4)<<8,s=-1;if(s=a.strategy>=_t||a.level<2?0:a.level<6?1:6===a.level?2:3,e|=s<<6,0!==a.strstart&&(e|=32),e+=31-e%31,Et(a,e),0!==a.strstart&&(Et(a,t.adler>>>16),Et(a,65535&t.adler)),t.adler=1,a.status=gt,kt(t),0!==a.pending)return a.last_flush=-1,tt}if(57===a.status)if(t.adler=0,At(a,31),At(a,139),At(a,8),a.gzhead)At(a,(a.gzhead.text?1:0)+(a.gzhead.hcrc?2:0)+(a.gzhead.extra?4:0)+(a.gzhead.name?8:0)+(a.gzhead.comment?16:0)),At(a,255&a.gzhead.time),At(a,a.gzhead.time>>8&255),At(a,a.gzhead.time>>16&255),At(a,a.gzhead.time>>24&255),At(a,9===a.level?2:a.strategy>=_t||a.level<2?4:0),At(a,255&a.gzhead.os),a.gzhead.extra&&a.gzhead.extra.length&&(At(a,255&a.gzhead.extra.length),At(a,a.gzhead.extra.length>>8&255)),a.gzhead.hcrc&&(t.adler=M(t.adler,a.pending_buf,a.pending,0)),a.gzindex=0,a.status=69;else if(At(a,0),At(a,0),At(a,0),At(a,0),At(a,0),At(a,9===a.level?2:a.strategy>=_t||a.level<2?4:0),At(a,3),a.status=gt,kt(t),0!==a.pending)return a.last_flush=-1,tt;if(69===a.status){if(a.gzhead.extra){let e=a.pending,s=(65535&a.gzhead.extra.length)-a.gzindex;for(;a.pending+s>a.pending_buf_size;){let n=a.pending_buf_size-a.pending;if(a.pending_buf.set(a.gzhead.extra.subarray(a.gzindex,a.gzindex+n),a.pending),a.pending=a.pending_buf_size,a.gzhead.hcrc&&a.pending>e&&(t.adler=M(t.adler,a.pending_buf,a.pending-e,e)),a.gzindex+=n,kt(t),0!==a.pending)return a.last_flush=-1,tt;e=0,s-=n}let n=new Uint8Array(a.gzhead.extra);a.pending_buf.set(n.subarray(a.gzindex,a.gzindex+s),a.pending),a.pending+=s,a.gzhead.hcrc&&a.pending>e&&(t.adler=M(t.adler,a.pending_buf,a.pending-e,e)),a.gzindex=0}a.status=73}if(73===a.status){if(a.gzhead.name){let e,s=a.pending;do{if(a.pending===a.pending_buf_size){if(a.gzhead.hcrc&&a.pending>s&&(t.adler=M(t.adler,a.pending_buf,a.pending-s,s)),kt(t),0!==a.pending)return a.last_flush=-1,tt;s=0}e=a.gzindex<a.gzhead.name.length?255&a.gzhead.name.charCodeAt(a.gzindex++):0,At(a,e)}while(0!==e);a.gzhead.hcrc&&a.pending>s&&(t.adler=M(t.adler,a.pending_buf,a.pending-s,s)),a.gzindex=0}a.status=91}if(91===a.status){if(a.gzhead.comment){let e,s=a.pending;do{if(a.pending===a.pending_buf_size){if(a.gzhead.hcrc&&a.pending>s&&(t.adler=M(t.adler,a.pending_buf,a.pending-s,s)),kt(t),0!==a.pending)return a.last_flush=-1,tt;s=0}e=a.gzindex<a.gzhead.comment.length?255&a.gzhead.comment.charCodeAt(a.gzindex++):0,At(a,e)}while(0!==e);a.gzhead.hcrc&&a.pending>s&&(t.adler=M(t.adler,a.pending_buf,a.pending-s,s))}a.status=103}if(103===a.status){if(a.gzhead.hcrc){if(a.pending+2>a.pending_buf_size&&(kt(t),0!==a.pending))return a.last_flush=-1,tt;At(a,255&t.adler),At(a,t.adler>>8&255),t.adler=0}if(a.status=gt,kt(t),0!==a.pending)return a.last_flush=-1,tt}if(0!==t.avail_in||0!==a.lookahead||e!==q&&a.status!==wt){let s=0===a.level?St(a,e):a.strategy===_t?((t,e)=>{let a;for(;;){if(0===t.lookahead&&(Rt(t),0===t.lookahead)){if(e===q)return 1;break}if(t.match_length=0,a=X(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,a&&(xt(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===V?(xt(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(xt(t,!1),0===t.strm.avail_out)?1:2})(a,e):a.strategy===lt?((t,e)=>{let a,s,n,r;const i=t.window;for(;;){if(t.lookahead<=ft){if(Rt(t),t.lookahead<=ft&&e===q)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(n=t.strstart-1,s=i[n],s===i[++n]&&s===i[++n]&&s===i[++n])){r=t.strstart+ft;do{}while(s===i[++n]&&s===i[++n]&&s===i[++n]&&s===i[++n]&&s===i[++n]&&s===i[++n]&&s===i[++n]&&s===i[++n]&&n<r);t.match_length=ft-(r-n),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(a=X(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(a=X(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),a&&(xt(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===V?(xt(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(xt(t,!1),0===t.strm.avail_out)?1:2})(a,e):Ot[a.level].func(a,e);if(3!==s&&4!==s||(a.status=wt),1===s||3===s)return 0===t.avail_out&&(a.last_flush=-1),tt;if(2===s&&(e===J?W(a):e!==$&&(Y(a,0,0,!1),e===Q&&(yt(a.head),0===a.lookahead&&(a.strstart=0,a.block_start=0,a.insert=0))),kt(t),0===t.avail_out))return a.last_flush=-1,tt}return e!==V?tt:a.wrap<=0?et:(2===a.wrap?(At(a,255&t.adler),At(a,t.adler>>8&255),At(a,t.adler>>16&255),At(a,t.adler>>24&255),At(a,255&t.total_in),At(a,t.total_in>>8&255),At(a,t.total_in>>16&255),At(a,t.total_in>>24&255)):(Et(a,t.adler>>>16),Et(a,65535&t.adler)),kt(t),a.wrap>0&&(a.wrap=-a.wrap),0!==a.pending?tt:et)},deflateEnd:t=>{if(Nt(t))return at;const e=t.state.status;return t.state=null,e===gt?mt(t,st):tt},deflateSetDictionary:(t,e)=>{let a=e.length;if(Nt(t))return at;const s=t.state,n=s.wrap;if(2===n||1===n&&s.status!==pt||s.lookahead)return at;if(1===n&&(t.adler=B(t.adler,e,a,0)),s.wrap=0,a>=s.w_size){0===n&&(yt(s.head),s.strstart=0,s.block_start=0,s.insert=0);let t=new Uint8Array(s.w_size);t.set(e.subarray(a-s.w_size,a),0),e=t,a=s.w_size}const r=t.avail_in,i=t.next_in,_=t.input;for(t.avail_in=a,t.next_in=0,t.input=e,Rt(s);s.lookahead>=3;){let t=s.strstart,e=s.lookahead-2;do{s.ins_h=zt(s,s.ins_h,s.window[t+3-1]),s.prev[t&s.w_mask]=s.head[s.ins_h],s.head[s.ins_h]=t,t++}while(--e);s.strstart=t,s.lookahead=2,Rt(s)}return s.strstart+=s.lookahead,s.block_start=s.strstart,s.insert=s.lookahead,s.lookahead=0,s.match_length=s.prev_length=2,s.match_available=0,t.next_in=i,t.input=_,t.avail_in=r,s.wrap=n,tt},deflateInfo:"pako deflate (from Nodeca project)"};const Mt=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var Pt=function(t){const e=Array.prototype.slice.call(arguments,1);for(;e.length;){const a=e.shift();if(a){if("object"!=typeof a)throw new TypeError(a+"must be non-object");for(const e in a)Mt(a,e)&&(t[e]=a[e])}}return t},jt=t=>{let e=0;for(let a=0,s=t.length;a<s;a++)e+=t[a].length;const a=new Uint8Array(e);for(let e=0,s=0,n=t.length;e<n;e++){let n=t[e];a.set(n,s),s+=n.length}return a};let Kt=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){Kt=!1}const Yt=new Uint8Array(256);for(let t=0;t<256;t++)Yt[t]=t>=252?6:t>=248?5:t>=240?4:t>=224?3:t>=192?2:1;Yt[254]=Yt[254]=1;var Gt=t=>{if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(t);let e,a,s,n,r,i=t.length,_=0;for(n=0;n<i;n++)a=t.charCodeAt(n),55296==(64512&a)&&n+1<i&&(s=t.charCodeAt(n+1),56320==(64512&s)&&(a=65536+(a-55296<<10)+(s-56320),n++)),_+=a<128?1:a<2048?2:a<65536?3:4;for(e=new Uint8Array(_),r=0,n=0;r<_;n++)a=t.charCodeAt(n),55296==(64512&a)&&n+1<i&&(s=t.charCodeAt(n+1),56320==(64512&s)&&(a=65536+(a-55296<<10)+(s-56320),n++)),a<128?e[r++]=a:a<2048?(e[r++]=192|a>>>6,e[r++]=128|63&a):a<65536?(e[r++]=224|a>>>12,e[r++]=128|a>>>6&63,e[r++]=128|63&a):(e[r++]=240|a>>>18,e[r++]=128|a>>>12&63,e[r++]=128|a>>>6&63,e[r++]=128|63&a);return e};var Xt=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0};const Wt=Object.prototype.toString,{Z_NO_FLUSH:qt,Z_SYNC_FLUSH:Jt,Z_FULL_FLUSH:Qt,Z_FINISH:Vt,Z_OK:$t,Z_STREAM_END:te,Z_DEFAULT_COMPRESSION:ee,Z_DEFAULT_STRATEGY:ae,Z_DEFLATED:se}=j;function ne(t){this.options=Pt({level:ee,method:se,chunkSize:16384,windowBits:15,memLevel:8,strategy:ae},t||{});let e=this.options;e.raw&&e.windowBits>0?e.windowBits=-e.windowBits:e.gzip&&e.windowBits>0&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Xt,this.strm.avail_out=0;let a=Ht.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(a!==$t)throw new Error(P[a]);if(e.header&&Ht.deflateSetHeader(this.strm,e.header),e.dictionary){let t;if(t="string"==typeof e.dictionary?Gt(e.dictionary):"[object ArrayBuffer]"===Wt.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,a=Ht.deflateSetDictionary(this.strm,t),a!==$t)throw new Error(P[a]);this._dict_set=!0}}function re(t,e){const a=new ne(e);if(a.push(t,!0),a.err)throw a.msg||P[a.err];return a.result}ne.prototype.push=function(t,e){const a=this.strm,s=this.options.chunkSize;let n,r;if(this.ended)return!1;for(r=e===~~e?e:!0===e?Vt:qt,"string"==typeof t?a.input=Gt(t):"[object ArrayBuffer]"===Wt.call(t)?a.input=new Uint8Array(t):a.input=t,a.next_in=0,a.avail_in=a.input.length;;)if(0===a.avail_out&&(a.output=new Uint8Array(s),a.next_out=0,a.avail_out=s),(r===Jt||r===Qt)&&a.avail_out<=6)this.onData(a.output.subarray(0,a.next_out)),a.avail_out=0;else{if(n=Ht.deflate(a,r),n===te)return a.next_out>0&&this.onData(a.output.subarray(0,a.next_out)),n=Ht.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===$t;if(0!==a.avail_out){if(r>0&&a.next_out>0)this.onData(a.output.subarray(0,a.next_out)),a.avail_out=0;else if(0===a.avail_in)break}else this.onData(a.output)}return!0},ne.prototype.onData=function(t){this.chunks.push(t)},ne.prototype.onEnd=function(t){t===$t&&(this.result=jt(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg};var ie=ne,_e=re,le=function(t,e){return(e=e||{}).raw=!0,re(t,e)},he=function(t,e){return(e=e||{}).gzip=!0,re(t,e)},oe=j,de={Deflate:ie,deflate:_e,deflateRaw:le,gzip:he,constants:oe};t.Deflate=ie,t.constants=oe,t.default=de,t.deflate=_e,t.deflateRaw=le,t.gzip=he,Object.defineProperty(t,"__esModule",{value:!0})}));
