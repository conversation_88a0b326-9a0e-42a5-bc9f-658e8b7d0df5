# 营养干预方案接口对接文档

## 1. 接口概述

营养干预方案管理系统提供完整的营养干预方案查询、保存等功能，支持减重方案、饮食方案、运动方案的统一管理。

## 2. 查询营养干预方案列表接口

### 接口信息
- **接口名称**: 查询营养干预方案列表（支持关联查询）
- **接口地址**: `GET /zwb/nutritionIntervention/list`
- **接口描述**: 查询营养干预方案列表，支持分页查询和按患者ID+就诊ID的关联查询
- **权限要求**: 需要zwb:nutritionIntervention:list权限

### 请求参数

#### 分页查询参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |
| orderByColumn | String | 否 | 排序字段 |
| isAsc | String | 否 | 排序方向(asc/desc) |

#### 查询条件参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| patientId | String | 否 | 患者ID |
| visitId | String | 否 | 就诊ID |

**特殊说明**: 当同时提供`patientId`和`visitId`参数时，接口会自动使用关联查询逻辑，返回包含饮食详情和运动详情的完整数据。

### 请求示例

#### 按患者和就诊查询（关联查询）
```http
GET /zwb/nutritionIntervention/list?patientId=123456&visitId=789012
```


### 响应数据结构

#### 响应格式
```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 100,
  "rows": [
    {
      // 营养干预方案数据
    }
  ]
}
```

#### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 状态码，200表示成功 |
| msg | String | 响应消息 |
| total | Long | 总记录数 |
| rows | Array | 数据列表 |

#### 营养干预方案数据结构（rows中的单个对象）

##### 基础信息字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 序列号 |
| patientId | String | 患者ID |
| visitId | String | 就诊ID |
| checkDate | Date | 首诊时间(yyyy-MM-dd格式) |

##### 身体指标字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| height | BigDecimal | 身高(cm) |
| weight | BigDecimal | 体重(kg) |
| bmi | BigDecimal | BMI值(kg/m²) |
| bodyFatRate | BigDecimal | 体脂率(%) |
| leanWeight | BigDecimal | 瘦体重(kg) |
| boneMass | BigDecimal | 骨骼肌(kg) |
| waistline | BigDecimal | 腰围(cm) |
| waistHipRatio | BigDecimal | 腰臀比 |
| weightLevel | String | 体重等级(正常/超重/肥胖I级/肥胖II级/肥胖III级) |
| bodyFatLevel | String | 体脂率等级(正常/高) |
| basalMetabolism | Integer | 基础代谢(kcal) |

##### 疾病信息字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| hasHypertension | Integer | 是否有高血压(0否1是) |
| hasDiabetes | Integer | 是否有糖尿病(0否1是) |
| hasHeartDisease | Integer | 是否有冠心病(0否1是) |
| hasGout | Integer | 是否有痛风(0否1是) |
| hasChronicKidney | Integer | 是否有慢性肾病(0否1是) |
| hasLiverDysfunction | Integer | 是否有肝功能异常(0否1是) |
| hasThyroidDysfunction | Integer | 是否有甲状腺功能减退(0否1是) |
| otherDiseases | String | 其他疾病 |

##### 目标设定字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| targetWeightMonthly | BigDecimal | 月末目标体重(kg) |
| targetWaistlineMonthly | BigDecimal | 月末目标腰围(cm) |
| targetWeightLongterm | BigDecimal | 长期目标体重(kg) |
| targetWaistlineLongterm | BigDecimal | 长期目标腰围(cm) |

##### 营养配比字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| proteinRatio | String | 蛋白质比例(%) |
| fatRatio | String | 脂肪比例(%) |
| carbohydrateRatio | String | 碳水化合物比例(%) |
| calorie | String | 热量(kcal) |
| proteinDay | String | 蛋白质(g/日) |
| recommendedCalories | String | 推荐热量摄入(kcal/日) |
| recommendedProtein | String | 推荐蛋白摄入量(g/日) |

##### 运动指导字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| heartRateControl | String | 有氧训练心率控制(如110-125次/分钟) |
| exercisePrecautions | String | 运动注意事项 |
| suggestedAerobicExercises | String | 建议有氧运动项目 |

##### 方案内容字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| weightLossPlanName | String | 减重方案名称 |
| dietGuidanceContent | String | 饮食指导说明 |
| exerciseGuidanceContent | String | 运动指导说明 |

##### 关联详情字段（仅在关联查询时返回）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| dietDetailList | Array | 饮食方案详情列表 |
| exerciseDetailList | Array | 运动方案详情列表 |

#### 饮食方案详情数据结构（dietDetailList中的单个对象）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 序列号 |
| nutritionId | String | 营养干预方案主表ID |
| mealType | String | 餐次类型(早餐/加餐/午餐/晚餐/建议饮水) |
| mealTime | String | 用餐时间(如7:30,10:00,12:00,18:30) |
| foodCategory | String | 食物类别(肉蛋鱼/乳类/水果类/谷薯类/油脂类/蔬菜类/豆制品/水) |
| foodContent | String | 食物内容(如1个鸡蛋,250ml脱脂奶,200g,35g主食) |
| extraMeal | Integer | 是否加餐(0否1是) |
| sortOrder | Long | 排序 |

#### 运动方案详情数据结构（exerciseDetailList中的单个对象）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 序列号 |
| nutritionId | String | 营养干预方案主表ID |
| weekNumber | String | 第n周 |
| exerciseDuration | String | 运动时长 |
| exerciseType | String | 运动类型(有氧运动/抗阻训练) |
| exerciseContent | String | 运动项目 |
| highIntensity | String | 周运动量 |
| sortOrder | Long | 排序 |

### 响应示例

#### 关联查询响应（包含详情数据）
```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 1,
  "rows": [
    {
      "id": "1234567890123456789",
      "patientId": "P001",
      "visitId": "V001",
      // ... 其他基础字段
      "dietDetailList": [
        {
          "id": "D001",
          "nutritionId": "1234567890123456789",
          "mealType": "早餐",
          "mealTime": "7:30",
          "foodCategory": "肉蛋鱼",
          "foodContent": "1个鸡蛋",
          "extraMeal": 0,
          "sortOrder": 1
        },
        {
          "id": "D002",
          "nutritionId": "1234567890123456789",
          "mealType": "早餐",
          "mealTime": "7:30",
          "foodCategory": "乳类",
          "foodContent": "250ml脱脂奶",
          "extraMeal": 0,
          "sortOrder": 2
        }
      ],
      "exerciseDetailList": [
        {
          "id": "E001",
          "nutritionId": "1234567890123456789",
          "weekNumber": "第一周",
          "exerciseDuration": "40分钟",
          "exerciseType": "有氧运动",
          "exerciseContent": "快走或单车、游泳、椭圆仪",
          "highIntensity": "运动5天·每天40分钟以上",
          "sortOrder": 1
        },
        {
          "id": "E002",
          "nutritionId": "1234567890123456789",
          "weekNumber": "第二周",
          "exerciseDuration": "50分钟",
          "exerciseType": "有氧运动",
          "exerciseContent": "快走或单车、游泳、椭圆仪",
          "highIntensity": "运动5天·每天50分钟以上",
          "sortOrder": 2
        }
      ]
    }
  ]
}
```

### 错误响应

#### 权限不足
```json
{
  "code": 403,
  "msg": "权限不足"
}
```

#### 参数错误
```json
{
  "code": 400,
  "msg": "参数错误：日期格式不正确"
}
```

#### 系统错误
```json
{
  "code": 500,
  "msg": "系统内部错误"
}
```

## 3. 接口调用说明

### 调用流程
1. 确保具有`zwb:nutritionIntervention:list`权限
2. 根据需要构造查询参数
3. 发送GET请求到接口地址
4. 解析返回的JSON数据

### 注意事项
1. **关联查询**：当同时提供`patientId`和`visitId`时，会返回包含`dietDetailList`和`exerciseDetailList`的完整数据
2. **分页查询**：普通查询支持分页，关联查询通常返回单条记录
3. **日期格式**：所有日期字段使用`yyyy-MM-dd`格式
4. **数值精度**：BigDecimal类型字段保持原有精度
5. **布尔值表示**：疾病相关字段使用0/1表示否/是

### 性能建议
1. 使用关联查询时建议明确指定`patientId`和`visitId`
2. 大数据量查询时合理设置`pageSize`
3. 利用索引字段进行条件查询以提高性能

## 4. 前端集成示例

### JavaScript调用示例
```javascript
// 普通分页查询
const queryList = async (params) => {
  const response = await fetch('/zwb/nutritionIntervention/list?' + new URLSearchParams(params));
  return await response.json();
};

// 关联查询
const queryByPatientAndVisit = async (patientId, visitId) => {
  const response = await fetch(`/zwb/nutritionIntervention/list?patientId=${patientId}&visitId=${visitId}`);
  return await response.json();
};
```

### Vue.js组件示例
```vue
<template>
  <div>
    <el-table :data="tableData" v-loading="loading">
      <el-table-column prop="patientId" label="患者ID"></el-table-column>
      <el-table-column prop="checkDate" label="首诊时间"></el-table-column>
      <el-table-column prop="weightLevel" label="体重等级"></el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total">
    </el-pagination>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0
    };
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };
        const result = await this.queryList(params);
        this.tableData = result.rows;
        this.total = result.total;
      } finally {
        this.loading = false;
      }
    },
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchData();
    }
  },
  mounted() {
    this.fetchData();
  }
};
</script>
```

---

**文档版本**: v1.0  
**更新时间**: 2025-07-15  
**维护人员**: 系统开发团队
