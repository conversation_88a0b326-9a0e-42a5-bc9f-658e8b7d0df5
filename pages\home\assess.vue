<template>
	<view>
		<view class="gui-padding">
				<view class="gui-table  gui-border-t" style="margin-top:50rpx; ">
					<view class="gui-theader gui-flex gui-rows gui-nowrap theader ">
						<text class="gui-td1 gui-border-r gui-border-b gui-td-text gui-bold gui-border-l  gui-text-center">序号</text>
						<text class="gui-td2 gui-border-r gui-border-b gui-td-text gui-bold gui-text-center">患者信息</text>
						<text class="gui-td3 gui-border-r gui-border-b gui-td-text gui-bold gui-text-center">异常内容</text>
						<text class="gui-td4 gui-border-r gui-border-b gui-td-text gui-bold gui-text-center">提交时间</text>
						<text class="gui-td5 gui-border-r gui-border-b gui-td-text gui-bold gui-text-center">状态</text>
					
					</view>
					<view class="gui-tbody gui-flex gui-rows gui-nowrap gui-color-gray" 
					v-for="(item, index) in questionwarning" :key="'a'+index" >
						<text  class="gui-td1 gui-td-text gui-text-center gui-border-r gui-border-l gui-border-b">{{index+1}}</text>
						<text class="gui-td2 gui-td-text gui-text-center gui-border-r gui-border-b">{{item.name}} {{item.sex== 1 ?'男':'女'}} {{item.age}}岁</text>
						<text class="gui-td3 gui-td-text gui-text-center gui-border-r gui-border-b gui-color-red">【 {{item.warning}} 】</text>
						<text class="gui-td4 gui-td-text gui-text-center gui-border-r gui-border-b">{{item.warningTime}}</text>
						<text class="gui-td5 gui-td-text gui-text-center gui-border-r gui-border-b">{{item.status== 1 ?'已处理':'未处理'}}</text>
					</view>
				</view>
			</view>
	</view>
	
	
</template>

<script>
	export default {
		data() {
			return {
				questionwarning:[],
			}
		},
		onShow() {
			this.QuestionWarning();	
		},
		methods: {
		
			//监测预警
			QuestionWarning(){
				this.$common.RequestData({
					url: this.$common.QuestionWarning,
					data: {
						seachType:1
					},
					method: 'get',
				},res => {
					this.questionwarning = res.rows;
				
				},)
			}
		}
	}
</script>

<style scoped>

	.gui-td1{width:10%; display:flexbox;}
	.gui-td2{width:24%; display:flexbox;}
	.gui-td3{width:40%;  display:flexbox;}
	.gui-td4{width:22%;  display:flexbox;}
	.gui-td5{width:13%; display:flexbox;}
	.theader{color:#7784eb;}
	.gui-td-text{line-height:60rpx !important; font-size:24rpx;}
	.bgao{
	color: rgb(0, 138, 255)
	}
</style>