<template name="gui-im-input">
	<view>
		<view :style="{paddingBottom:paddingB + 'px'}" class="gui-im-footer gui-bg-gray gui-dark-bg-level-2">
			<view class="gui-flex gui-row gui-nowrap gui-space-between gui-align-items-end">
				<!-- <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" v-if="voiceBtnShow" @tap="showRec">&#xe617;</view> -->
				<!-- <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" @tap="chooseImg">&#xe63d;</view> -->
				<!-- <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" @tap="chooseTask">&#xe62f;</view> -->
				<view style="margin-left: 12rpx;margin-bottom: 12rpx;">
					<view class="btn-icons" @click.stop="switchMode">
						<text v-show="inputType===1" class="gui-icons" style="font-size: 40rpx;">&#xe800;</text>
						<text v-show="inputType===2" class="gui-icons" style="font-size: 40rpx;">&#xe627;</text>
					</view>
				</view>
				<view v-show="inputType===1" class="gui-im-input gui-bg-white gui-dark-bg-level-3">
					<textarea type="text" v-model="inputMsg" :fixed="true" :maxlength="-1" rows="7" @confirm="sendTextMsg"
						:cursor-spacing="35" :adjust-position="adjust" :show-confirm-bar="false" :auto-height="true"
						@focus="inputFocus" @blur="inputBlur"></textarea>
				</view>
				<view v-show="inputType===2" class="gui-im-input" style="padding: 0;">
					<!-- <view style="font-size: 32rpx; line-height: 40rpx; text-align: center;">按住 说话</view> -->
					<gui-im-speech ref="speech" @permRecord="switchMode" @endRecord="sendSoundMsg"></gui-im-speech>
				</view>
				<view style="flex-shrink: 0; margin-bottom: 8rpx; margin-right: 10rpx;">
					<view class="gui-items gui-color-white gui-bg-blue b-radius-10" style="padding:0 20rpx; margin: 10rpx;"
						hover-class="gui-tap" @click.stop="sendTextMsg">发送
					</view>
				</view>

			</view>
			<view class="gui-flex gui-row gui-nowrap gui-align-items-center" style="margin-left: 12rpx;">
				<!-- <view class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap" v-if="voiceBtnShow"
					@tap="showRec">&#xe617;
				</view> -->
				<view style="font-size: 50rpx;" class="gui-im-menus gui-icons gui-secondary-text" hover-class="gui-tap"
					@tap="chooseImg">&#xe63d;
				</view>
				<view class="gui-flex gui-align-items-center" style="margin-left: 20rpx;" @tap="chooseTask(3)">
					<view style="font-size: 50rpx;" class=" gui-icons gui-secondary-text" hover-class="gui-tap">&#xe62f;
					</view>
					<view style="margin-right: 20rpx;"> | 宣教指导</view>
				</view>
				<view class="gui-flex gui-align-items-center" @tap="chooseTask(1)">
					<view style="font-size: 45rpx;" class=" gui-icons gui-secondary-text" hover-class="gui-tap">&#xe624;
					</view>
					<view> | 问卷填写</view>
				</view>
			</view>
			<view>
				<gui-iphone-bottom></gui-iphone-bottom>
			</view>
		</view>
		<!-- 语音输入 -->
		<view class="gui-im-record" v-if="recShow">
			<view class="gui-im-record-txt">
				{{ recTxt }}
				<text v-if="recing">已录音 : {{ recLength }} s</text>
			</view>
			<view class="gui-im-record-btn" @tap="rec" :class="[recing ? 'gui-im-recording' : '']"></view>
			<view class="gui-im-send-voice" v-if="tmpVoice != ''">
				<text @tap="sendVoiceMsg">发送语音</text>
			</view>
			<view class="gui-im-record-close graceIMFonts icon-close" @tap="closeRec" v-if="!recing"></view>
		</view>
	</view>
</template>
<script>
	// 挂载 vuex
	import {
		mapState,
		mapMutations
	} from 'vuex';
	import utils from '@/tools/utils/utils.js'
	import url from "@/common.js"
	var bgAudioMannager = uni.getBackgroundAudioManager();
	import guiImSpeech from './gui-im-speech.vue';
	export default {
		name: "gui-im-input",
		comments: {
			guiImSpeech
		},
		props: {
			inputContent: {
				type: String,
				default: ""
			},
			receiverUid: {
				type: String,
				default: ""
			},
			orderId: {
				type: String,
				default: ""
			},
			token: {
				type: String,
				default: ""
			},
			group: {
				type: String,
				default: ""
			},
			adjustPosition: {
				type: Boolean,
				default: true
			},
		},
		data() {
			return {
				paddingB: '12',
				uploading: false,
				recShow: false,
				recTxt: "请点击绿色按钮开始录音",
				inputMsg: "",
				recorderManager: null,
				recing: false,
				recLength: 1,
				recTimer: null,
				tmpVoice: '',
				voiceLen: 0,
				voiceBtnShow: false,
				// 播放相关
				player: null,
				playTxt: "试听语音",
				adjust: true,
				// 1文本 2语音
				inputType: 1
			}
		},
		computed: {
			...mapState(['graceIMUID'])
		},
		watch: {
			adjustPosition: {
				deep: true,
				handler(newValue, oldValue) {
					//子组件中监控值变化做 相关业务功能
					this.adjust = newValue
					console.log('watch-adjustPosition:' + newValue)
				}
			},
			inputContent: {
				deep: true,
				handler(newValue, oldValue) {
					console.log('撤回成功的消息内容==', newValue)
					this.inputMsg = newValue
				}
			}
		},
		created: function() {
			// #ifndef H5
			this.voiceBtnShow = true;
			this.recorderManager = uni.getRecorderManager();
			this.recorderManager.onStop((res) => {
				this.tmpVoice = res.tempFilePath;
				this.recing = false;
				this.recTxt = "... 已录音 " + this.recLength +
					"s, 点击绿色按钮重新录音 ...";
				clearInterval(this.recTimer);
			});
			this.recorderManager.onError(() => {
				uni.showToast({
					title: '录音失败',
					icon: 'none'
				});
				this.recing = false;
				this.recTxt = "请点击绿色按钮开始录音",
					clearInterval(this.recTimer);
			});
			// #endif
			// #ifdef MP
			try {
				var res = uni.getSystemInfoSync();
				res.model = res.model.replace(' ', '');
				res.model = res.model.toLowerCase();
				var res1 = res.model.indexOf('iphonex');
				if (res1 > 5) {
					res1 = -1;
				}
				var res2 = res.model.indexOf('iphone1');
				if (res2 > 5) {
					res2 = -1;
				}
				if (res1 != -1 || res2 != -1) {
					this.paddingB = uni.upx2px(50) + 'px';
				}
			} catch (e) {
				return null;
			}
			// #endif
		},
		methods: {
			inputFocus(e) {
				if (!this.adjust) {
					if (e.detail.height) {
						this.paddingB = String(e.detail.height); //输入框抬高到软键盘上面
					}
				} else {
					this.paddingB = 0;
				}

			},
			inputBlur() {
				this.paddingB = '12'
			},
			// 切换模式
			switchMode() {
				if (this.$refs["speech"].permStatus) {
					this.inputType = this.inputType === 1 ? 2 : 1
				} else {
					if (this.$refs["speech"].permDepict) {
						uni.showToast({
							title: this.$refs["speech"].permDepict,
							icon: "none"
						});
					} else {
						this.$refs["speech"].reqRecord()
					}
				}
			},
			// 录音
			rec: function() {
				if (this.recing) {
					this.recorderManager.stop();
					this.recing = false;
					this.recTxt = "... 已录音 " + this.recLength +
						"s, 点击绿色按钮重新录音 ...";
					clearInterval(this.recTimer);
				} else {
					this.recorderManager.start({
						duration: 60000,
						format: 'mp3'
					});
					this.recing = true;
					this.recTxt = "... 正在录音 ...";
					this.recLength = 1;
					this.recTimer = setInterval(() => {
						this.recLength++;
					}, 1000);
				}
			},
			// 发送录音
			sendVoiceMsg: function() {
				if (this.tmpVoice == '') {
					uni.showToast({
						title: "请先录制一段语音",
						icon: "none"
					});
					return;
				}
				// 关闭界面
				this.recShow = false;
				this.$emit('sendVoice', this.tmpVoice, this.recLength);
				this.tmpVoice = '';
				this.recLength = 0;
				this.recTxt = "请点击绿色按钮开始录音";
			},
			// 展示录音界面
			showRec: function() {
				this.recShow = true;
			},
			// 关闭录音界面
			closeRec: function() {
				this.recShow = false;
			},

			// 发送文本消息
			sendTextMsg: function() {
				if (this.inputMsg < 1) {
					return false;
				}
				this.$emit('toSendSocket');
				// let inputMsg = this.replaceEmoji(this.inputMsg);
				var msg = {
					type: 'msg',
					contentType: 'txt',
					receiverType: '2', //接收方类型 1医生，2患者 -用于离线消息推送判断
					receiverUid: this.receiverUid, //接收者用户uid
					senderUid: this.graceIMUID, //发送方用户uid
					groupIndex: this.group, //好友组
					content: this.inputMsg, //发送的信息
					orderId: this.orderId, // 咨询订单id
					patientId: uni.getStorageSync('mbcardObj').patientId, // 患者id
					token: this.token //后台校验token，后面要封装在请求头
				}
				//改用http接口发送消息
				url.RequestDataNo({
					url: url.sendMsg,
					data: msg
				}, res => {
					this.$emit('sendText', this.inputMsg);
					this.inputMsg = '';
				})
			},
			sendSoundMsg(data) {
				//alert(JSON.stringify(res.blob.size))
				this.$common.H5uploadFile(data.blob, res => {
					//alert(JSON.stringify(res))
					let content = JSON.stringify({
						url: res.data.url,
						size: data.blob.size,
						second: data.duration / 1000
					})
					let msg = {
						type: 'msg',
						contentType: 'voice',
						receiverUid: this.receiverUid, //接收者用户uid
						senderUid: this.graceIMUID, //发送方用户uid
						groupIndex: this.group, //好友组
						content: content, //发送的信息
						orderId: this.orderId, // 咨询订单id
						token: this.token //后台校验token，后面要封装在请求头
					}
					url.RequestData({
						url: url.sendMsg,
						data: msg
					}, res => {
						this.$emit('sendVoice', content);
					})
				})
			},
			chooseTask(e) {
				this.$common.navTo('/pages/chat/task?groupIndex=' + this.group); //好友组
				uni.setStorageSync("mbtaskStatus", e)
				// if (uni.getStorageSync('mbhuanzInfo').patientId) {
				// 	this.$common.navTo('/pages/chat/task?groupIndex=' + this.group);//好友组
				// 	uni.setStorageSync("mbtaskStatus",e)
				// } else{
				// 	this.$common.msg('该用户没有绑定就诊卡')
				// }
			},
			// 选择图片
			chooseImg: function() {
				console.log(utils.translate)
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePaths = res.tempFilePaths;
						utils.translateImg(tempFilePaths[0], 0.1, ' ', imgUrl => {
							console.log(imgUrl, '解压图片')
							//打印压缩后返回的图片url
							this.sendImg(imgUrl)
						})
					}
				});
			},
			getImageInfo(src) {
				let _this = this
				uni.getImageInfo({
					src,
					success(res) {
						console.log('压缩前', res)
						let canvasWidth = res.width //图片原始长宽
						let canvasHeight = res.height
						let img = new Image()
						img.src = res.path
						let canvas = document.createElement('canvas');
						let ctx = canvas.getContext('2d')
						canvas.width = canvasWidth / 2
						canvas.height = canvasHeight / 2
						ctx.drawImage(img, 0, 0, canvasWidth / 2, canvasHeight / 2)
						canvas.toBlob(function(fileSrc) {
							let imgSrc = window.URL.createObjectURL(fileSrc)
							console.log('压缩后', imgSrc)
							_this.sendImg(imgSrc)
						})
					}
				})
			},
			// 发送图片信息
			sendImg(img) {
				this.$common.uploadFile(img, resx => {
					let msg = {
						type: 'msg',
						contentType: 'img',
						receiverUid: this.receiverUid, //接收者用户uid
						senderUid: this.graceIMUID, //发送方用户uid
						groupIndex: this.group, //好友组
						content: resx.data.url, //发送的信息
						orderId: this.orderId, // 咨询订单id
						token: this.token //后台校验token，后面要封装在请求头
					}
					url.RequestData({
						url: url.sendMsg,
						data: msg
					}, res => {
						this.$emit('chooseImage', img);
					})
				})
			},
		}
	}
</script>
<!--<style scoped>-->
<!--.gui-im-footer{background:#FFFFFF; width:100%; position:fixed; left:0; bottom:0; height:130rpx; display:flex; flex-wrap:nowrap; overflow:hidden; box-shadow:1px 1px 6px #999999; align-items:center;}-->
<!--.gui-im-footer .gui-items{width:auto; line-height:88rpx; flex-shrink:0; font-size:36rpx; color:#2B2E3D;}-->
<!--.gui-im-menus{width:80rpx; height:80rpx; flex-shrink:0; line-height:80rpx; text-align:center;}-->
<!--.gui-im-input{width:600rpx; margin:10rpx; background:#F4F5F6; border-radius:6rpx; height:70rpx;}-->
<!--.gui-im-input input{width:100%; background:#F4F5F6; color:#2B2E3D; height:50rpx; line-height:40rpx; font-size:32rpx; margin-top:12rpx;}-->
<!--.gui-im-input textarea{width:100%; background:#F4F5F6; color:#2B2E3D; height:80rpx; line-height:40rpx; font-size:32rpx; margin-top:12rpx;}-->
<!--.gui-im-record{width:100%; position:fixed; left:0; bottom:0; background:#FFFFFF; padding:30px 0; padding-bottom:100rpx; z-index:11; box-shadow:1px 1px 6px #999999;}-->
<!--.gui-im-record-close{width:100rpx; height:100rpx; position:absolute; top:0px; right:0px; z-index:100; text-align:center; line-height:100rpx; color:#888888; font-size:38rpx !important;}-->
<!--.gui-im-record-txt{text-align:center; font-size:26rpx; line-height:30px; padding-bottom:10px; color:#CCCCCC;}-->
<!--.gui-im-record-btn{width:60px; height:60px; margin:0 auto; border:5px solid #F1F2F3; border-radius:100%; background:#00B26A;}-->
<!--.gui-im-recording{background:#FF0000; animation:fade linear 2s infinite;}-->
<!--@keyframes fade{from{opacity:0.1;} 50%{opacity:1;} to{opacity:0.1;}}-->
<!--.gui-im-record-txt text{color:#00B26A; padding:0 12px;}-->
<!--.gui-im-send-voice{margin-top:12px; font-size:28rpx; color:#00BA62; text-align:center;}-->
<!--.gui-im-send-voice text{margin:0 15px; color:#00BA62;}-->

<!--@font-face{font-family:"graceIMFonts"; src:url('data:font/truetype;charset=utf-8;base64,d09GMgABAAAAAASoAAsAAAAACpAAAARbAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDMgqILIZ4ATYCJAMUCwwABCAFhG0HSxsHCcgOJQUABgUAAMBk8EB6fW8yk/3Z7Cd4BLPXQW6vA9B99wc4f67lvzL5jC8R2YVkAS+XAsN4QPB1pPqF+wfHUj8K/8Hyvu1BXjGyLDq25lGBDagb1ySRuNkEfcvRGr5iXEVcDQGYRBOJS2OXykbwRoFqQYpmTZc48HZYUBEkgaF3BBypENvQMbT12hNgq/b24g35wgANXYLX6jelSITc52EvKqRlDzJKWljFuYDbcSCBTAAFYkGgZxZuhipT05hBv+dOLYCJgYb2POx50vPyFxW2DZaiQzqT/8MDDYlCgHCAGk+KrY0reB4m8aDB8yQeJDwv50HBiwrZtGh0ur0JhAGiEhCdYIisoERHonui2lcmOM2zJHp7m6a+suXpoNZng9tfDG17vkyohugi+3dbns4wdwWyFuTaGyL2DH1o+v/RweMTg2Y6LqngIEk2qXNSr6zPX9naOrilZVBb25D29qFN+/Nh99riT7Z3vdsad6xlyKkr1tWNZ09eGz6kkbRWS7iliav7hBVp1hm64Q4O3rMhcFCzzBa4ywoa3MgWtHtjyNAmVUhYR3kdd6O7DasHDfpeN2jwb7vbQFjoV6Fa7o6VK9v0qChdVrZdRURhRbwW3rqlUHWLS5mMreMeM3nRSDO2TbYUy16uGosg002pTpZwphre5b7iZKa3S1VPqnNboCS+PE4Hmd9c9SJ2Lk+1zG3JjnAsPmH0XBpvDWJl+WX186e6zIWKSG6z0np0qthYRZSdTEQULL34fb9uJZ66RKiru0eP4K4lSXcWecR/xPf20/WbFi0Bz/8qJrcUz0VLHq9jd7SqyEjVyoWKSFZYZV4TL99QQihCuiGEghgP3sLyqYFyCAlv/7hDnEsCyWXFCOnjZkAVINnlhrLtTEJFJEusvRjRsGVZpxpGRH+psDBFlC1MRCRsj0k9bjZvPtcjYrOpiXamCVUROssu7hLWO2VNhdl5+yxFRU/Z8/pcBwNGkpu2yNAKAPaY/CNXAvak/CmXAfaM/CYXH8o/w6M7d+aODar8ZrgUAB2fJ5zRauxBhbTgeNSSWHrgShOOqa8crbp6d0qSt09n10Gh0T6LTOBwDfjzrxh7EEUmBAZBFmh4EAUSg0SkwmaCjotCcGBQAyYZlBzvIgCKG0I5gXTGAAS+nIKGN9dB4ssnUmH/QCeY/+DAV0gwGSICzugiWUw3GxZgjmER8V1IESyTCDvz1YvrsdSmc9Qu18jNmDqVIIrLznUnS7CJaRH7ONuleMYIItQyUDFcD+u6hWxqqVhg0TJjdnpMzpSgJ4oWLAN8aYCC4WBgRBA8XSAUBCxMiLIww9fz+vVgJNrQ4aARXT/8zWAoTpTVI+KIJjYBskRjJuralEon7UjEw2iEgCC3QgsDRDF1YXQes0DYwZupYAQY0cgZIjbpxNDJSFJNdPU6Yx5LT5ueeAZdaEIKJXThwGPGdM60RcRz0WJbUY1TF8sb8IrH8sVdigkAAA==') format('truetype');}-->
<!--.graceIMFonts{font-family:"graceIMFonts" !important; font-style:normal; color:#2B2E3D; font-size:56rpx;}-->
<!--.icon-voice:before{content:"\e63a";}-->
<!--.icon-photograph:before{content:"\e619";}-->
<!--.icon-close:before{content:"\e625";}-->
<!--.icon-kbd{content:"\e73b";}-->
<!--</style>-->

<style scoped>
	.gui-im-footer {
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 0;
		border: 1px solid #F1F2F3;
	}

	.gui-im-footer .gui-items {
		width: auto;
		line-height: 56rpx;
		flex-shrink: 0;
		font-size: 28rpx;
	}

	.gui-im-menus {
		width: 80rpx;
		height: 80rpx;
		flex-shrink: 0;
		line-height: 80rpx;
		text-align: center;
	}

	.gui-im-input {
		width: calc(750rpx - 230rpx);
		margin: 10rpx;
		padding: 16rpx;
		border-radius: 6rpx;
	}

	.gui-im-input textarea {
		width: 100%;
		height: 40rpx;
		max-height: 400rpx;
		line-height: 40rpx;
		font-size: 34rpx;
		/* margin-top: 10rpx; */
	}

	.gui-im-record {
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 0;
		padding: 30px 0;
		padding-bottom: 100rpx;
		z-index: 11;
	}

	.gui-im-record-close {
		width: 100rpx;
		height: 100rpx;
		position: absolute;
		top: 0px;
		right: 0px;
		z-index: 100;
		text-align: center;
		line-height: 100rpx;
		font-size: 38rpx !important;
	}

	.gui-im-record-txt {
		text-align: center;
		font-size: 26rpx;
		line-height: 30px;
		padding-bottom: 10px;
	}

	.gui-im-record-btn {
		width: 60px;
		height: 60px;
		margin: 0 auto;
		border: 5px solid #F1F2F3;
		border-radius: 100%;
		background: #00B26A;
	}

	.gui-im-recording {
		background: #FF0000;
		animation: fade linear 2s infinite;
	}

	@keyframes fade {
		from {
			opacity: 0.1;
		}

		50% {
			opacity: 1;
		}

		to {
			opacity: 0.1;
		}
	}

	.gui-im-record-txt text {
		color: #00B26A;
		padding: 0 12px;
	}

	.gui-im-send-voice {
		margin-top: 12px;
		font-size: 28rpx;
		color: #00BA62;
		text-align: center;
	}

	.gui-im-send-voice text {
		margin: 0 15px;
		color: #00BA62;
	}

	.gui-icons {
		/* font-size: 50rpx; */
	}

	.btn-icons {
		width: 48rpx;
		line-height: 40rpx;
		text-align: center;
		margin: 10rpx;
		padding-top: 4rpx;
		border-radius: 50rpx;
		box-shadow: 0 0 0 1.5px;
	}
</style>