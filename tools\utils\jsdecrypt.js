import CryptoJS from "crypto-js"

export default {
  decryptData(encryptedData, publicKey) {
    const secretKey = CryptoJS.enc.Utf8.parse(publicKey)
    const iv = CryptoJS.enc.Utf8.parse('1234567890123456')
    const decryptedData = CryptoJS.AES.decrypt({
        ciphertext: CryptoJS.enc.Base64.parse(encryptedData)
      },
      secretKey,
      {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })
    try {
      const json = JSON.parse(decryptedData.toString(CryptoJS.enc.Utf8))
      return json
    } catch (e) {
      return decryptedData.toString(CryptoJS.enc.Utf8);
    }
  }
}
