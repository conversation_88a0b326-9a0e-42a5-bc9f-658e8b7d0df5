# Contribution

# Git Flow 

The crypto-js project uses [git flow](https://github.com/nvie/gitflow) to manage branches. 
Do your changes on the `develop` or even better on a `feature/*` branch. Don't do any changes on the `master` branch.

# Pull request

Target your pull request on `develop` branch. Other pull request won't be accepted.

# How to build

1. Clone

2. Run

    ```sh
    npm install
    ```

3. Run

    ```sh
    npm run build
    ```
    
4. Check `build` folder