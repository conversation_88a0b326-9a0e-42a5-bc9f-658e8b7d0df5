<template>
	<view>
		<!-- 加载历史消息 -->
		<!-- <text v-if="online" class="online">已下线</text>/ -->
		<!-- 视频功能按钮 -->
		<view style="text-align:center;">
			<text class="grace-im-system-msg" @tap="getHistoryMsg">{{historyLodginText}}</text>
		</view>
		<!-- IM 消息展示区 -->
		<gui-im-message ref="imRef" :msgs="graceIMMsgs" :userid="friendUid" :group="group" @revokeText="revokeText"></gui-im-message>
		<!-- 介绍会话 -->
		<view v-if="types == 2" class="end">会话已结束</view>
		<view class="file" v-if="patientId" @click="fileClick">档案</view>
		<view style="height: 176rpx;"></view>
		<!-- 底部提交区 -->
		<gui-im-input v-if="types != 2"
			@chooseImage="chooseImage"
			@sendText="sendText"
			@sendVoice="sendVoice"
			@toSendSocket="toSendSocket"
			:receiver-uid="friendUid"
			:token="graceIMToken"
			:group="group"
			:orderId="orderId"
			:inputContent="inputContent"
      :adjustPosition="adjustPosition">
			</gui-im-input>
	</view>
</template>
<script>
	// 挂载 vuex
	import {
		mapState,
		mapMutations
	} from 'vuex';
	import url from "@/common.js"
	// import * as dd from 'dingtalk-jsapi';
	import {patientGetInfo} from "@/api/im.js"

	export default {
		data() {
			return {
				adjustPosition: true,//默认软键盘当上块内容区
				online: false, //是否在线
				msgs: [],
				userInfo: uni.getStorageSync('mbuserInfo'),
				graceIMScTops: 9999,
				imUser: {
					userId: uni.getStorageSync('mbuserInfo').userId, // 系统用户id 空
					userUid: '', //链接im返回的uuid
					userType: '1', // 用户类型  1：医生  2：患者
					uname: uni.getStorageSync('mbuserInfo').userName, // 用户名
					nickName: uni.getStorageSync('mbuserInfo').nickName, // 昵称
					uface: uni.getStorageSync('mbuserInfo').avatar // 头像
				},
				friendUid: 0,
				group: '',
				page: 1,
				size: 10,
				historyLodginText: '点击加载历史消息',
				title: '',
				orderId: "",
				types: null,
				friendName:null, //名称
				avatar:null,  //头像
				// 标题头
				nametitle:'',
				patientId:'',//患者id
				inputContent:'',//撤回的消息内容
			}
		},
		onLoad: function(options) {
			let imObj = uni.getStorageSync('mbimObj');
			console.log(imObj,'数据imObj')
			uni.setStorageSync('mbhuanzInfo',imObj)
			uni.removeStorageSync('mbimObj')
			// console.log("聊天界面中上一集传递过来的", imObj)
			this.group = imObj.groupIndex;
			this.orderId = imObj.orderId;
			this.types = imObj.types;
			this.friendUid = imObj.friendUid;
			this.imUser.userUid = imObj.userUid;
			this.friendName =imObj.uname; // 患者姓名
			this.avatar = imObj.uface; //患者头像
			this.patientId = imObj.patientId
			// this.nametitle = ''
			let gender  = imObj.gender == 1 ? "男" : imObj.gender == 2 ?"女":'未知'
			let age
			if(imObj.age != null){
				 age = Math.floor(Number(imObj.age))
			}else {
				 age = "- - "
			}
			let name = imObj.name||imObj.friendName
			this.nametitle = name + `(`+ gender +',' +  age + `岁` + `)`
			console.log(this.nametitle,'数据this.nametitle')
		  uni.setNavigationBarTitle({
			title: this.nametitle
		  })
			this.getMsgReaded(); // 标记已读
			// this.isdd()

		},
		onShow() {
			this.toSendSocket()
			this.getHistoryMsg(); //加载历史信息
			setTimeout(() => {
				this.pageScroll();
			}, 500)
		},
		onUnload() {
			this.$refs['imRef'].playStop()
		},
		computed: {
			...mapState([
				'graceIMMsgsCH',
				'graceIMStatus',
				'graceIMMsgs',
				'graceIMScTop',
				'graceIMHeight',
				'graceIMUIndex',
				'graceIMUID',
				'graceIMToken',
				'graceIMConfig',
				'graceIMPage'
			])
		},
		watch: {
			graceIMScTop(n, o) {
				this.pageScroll();
			},
			graceIMMsgs(){
				this.checkImMsgLength();
			},
			graceIMMsgsCH(n,o){
				// 有消息撤回
				if(n.isRevoke == 1){
					console.log('有消息撤回msgId',n)
					console.log(this.graceIMMsgs,'xxxxxxxxx')
					var arrindex=this.graceIMMsgs.findIndex((item)=>item.msgId==n.msgId);
					this.graceIMMsgs.splice(arrindex,1);
				}
			}
		},
		methods: {
			toSendSocket(){
				let that = this
				uni.sendSocketMessage({
					data: JSON.stringify({type:'keepConnect'}),
					fail: function(e) {
						console.log('心跳发送失败，重新发起ws连接');
						that.$store.dispatch('graceIMConnect', that.imUser);
					}
				});
			},

			fileClick(){
				patientGetInfo(this.patientId).then(res => {
					// console.log('在线咨询的患者档案==',res)
					if (res.code == 200 && res.data) {
						// this.$common.navTo('/pages/patient/details?openType=3&patientInfo=' + encodeURIComponent(JSON.stringify(res.data)))
						this.$common.navTo('/pages/patient/details?openType=3')
						uni.setStorageSync('mbdetailsPatientId',res.data.patientId)
					} else{
						this.$common.msg('未查到用户信息，无法查看档案')
					}
				})
				// this.$common.navTo('/pages/patient/details?openType=3&patientId=' + this.patientId)
			},
			...mapMutations(['graceIMConnect,clearGraceIMMsgs,closeSocket']),
      checkImMsgLength(){
        //聊天内容少于5条时 输入框输入是软键盘不顶掉上面内容模块保证最上面可见到。
        // 多于5条时顶上去看下放最新的
        if(this.graceIMMsgs.length < 5){
          this.adjustPosition = false
        }else {
          this.adjustPosition = true
        }
		if (this.graceIMMsgs.length>0) {
			// this.graceIMMsgs.filter(e=>{
			// 	if (e.isRevoke == 1) {//撤回消息
			// 		uni.showToast({
			// 			title: '正在撤回',
			// 			icon: 'loading'
			// 		});
			// 		this.historyLodginText = '点击加载历史消息';
			// 		this.getHistoryMsg(3)
			// 	}
			// })
		}
      },
	  revokeText(e){
	  	// console.log('撤回消息===',e)
		// console.log('撤回消息字段内容===',this.chooseMsgType(e.content))
	  	this.$set(this,'inputContent',this.chooseMsgType(e.content))
	  },
	  // 判断信息类型
	  chooseMsgType(str) {
	  	let imgZZ = new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/);
	  	try {
	  	  let jsonData = JSON.parse(str);
	  	  if (typeof jsonData === 'number') {// 数字类型
	  	  	return String(jsonData);
	  	  }
		  return ' ';
	  	  // return jsonData.questionnaire_name;
	  	} catch (error) {
	  		if (imgZZ.test(str)) {// 图片类型
	  			return ' ';
	  		} else {
	  			return str;
	  		}
	  	}
	  },
		// 判断是否是钉钉
		isdd(){
			let self = this
			if (dd.env.platform != 'notInDingTalk') {
				 // uni.showModal({
				 //  	title:'是钉钉'
				 //  })
				  // 简化百度方法
				     //      window.$dd.ready(function() {
				     //          window.$dd.biz.navigation.setTitle({
									// title: this.nametitle,
				     //              onSuccess : function(result) {
				     //              },
				     //              onFail : function(err) {}
				     //          });
				     //      })
				  // 百度找的方法
				//  const ChangePageTitle = (title) => {
				//     let userAgentObj = JSON.parse(localStorage.getItem('userAgentObj'))||null
				//     if(userAgentObj&&userAgentObj.isDingTalk){//钉钉内
				//         window.$dd.ready(function() {
				//             window.$dd.biz.navigation.setTitle({
				// 				title: this.nametitle,
				//                 onSuccess : function(result) {
				//                 },
				//                 onFail : function(err) {}
				//             });
				//         });
				//     }else{//微信或浏览器内
				//         var $body = $('body');
				//         document.title = title;//普通浏览器用这一句就可以修改了
				//         var $iframe = $('<iframe style="display: none"></iframe>');
				//         $iframe.on('load', function() {
				//           setTimeout(function() {
				//             $iframe.off('load').remove();
				//           }, 0);
				//         }).appendTo($body);
				//     }
				// }


				  //         window.$dd.ready(function() {
				  //             window.$dd.biz.navigation.setTitle({
				  //                 title : "1",//控制标题文本，空字符串表示显示默认文本
				  //                 onSuccess : function(result) {
				  //                 },
				  //                 onFail : function(err) {}
				  //             });
				  //         });
				  // 官方api
				// dd.setNavigationBar({
				//   title: '123123123',
				//   backgroundColor: '#108ee9',
				//   success() {
				//     dd.alert({
				//       content: '设置成功',
				//     });
				//   },
				//   fail() {
				//     dd.alert({
				//       content: '设置失败',
				//     });
				//   },

				// });


			}else{
					// 修改标题头
				uni.setNavigationBarTitle({
					// title: imObj.friendName,
					title: this.nametitle
				})
			}
		},
			// 标记已读
			getMsgReaded() {
				let that = this;
				if (uni.getStorageSync("mbtoken")) {
					url.RequestDataNo({
						url: url.msgReaded,
						data: {
							senderUid: this.friendUid, //空
							receiverUid: uni.getStorageSync("mbuserUid")
						}
					}, res => {
						this.getMsgCount();
						// 清空聊天记录，重新加载
						that.page = 1;
						that.msgs = [];
						that.getHistoryMsg();
					})
				}
			},
			// 获取消息总数
			getMsgCount() {
				if (uni.getStorageSync("mbtoken")) {
					url.RequestDataNo({
						url: url.msgCount,
						data: {
							userUId: uni.getStorageSync("mbuserUid")
						}
					}, res => {
						uni.setStorageSync("mbmsgCount", res.data)
					})
				}
			},
			// 加载历史消息
			getHistoryMsg: function() {
				let that = this;
				if (this.historyLodginText != '点击加载历史消息') {
					return;
				}
				if (!this.graceIMPage[this.group]) {
					this.graceIMPage[this.group] = 1;
				}
				this.historyLodginText = '加载中 ...';
				url.RequestDataNo({
					url: url.sessionRecord,
					data: {
						groupIndex: this.group,
						orderId: this.orderId,
						"pageSize": this.size,
						"pageNum": this.page
					}
				}, res => {
					var msg = res.rows;
					if (that.page == 1) {
						that.$store.commit('clearGraceIMMsgs');
					}
					for (var i = 0; i < msg.length; i++) {
						msg[i].date = msg[i].date ? url.parseTime(new Date(msg[i].date.substr(0, 19).replace(
							/T/g, ' ').replace(/-/g, '/')), '{m}-{d} {h}:{i}') : "";
						this.graceIMMsgs.unshift(msg[i]);
					}
					if (this.graceIMMsgs.length >= res.total) {
						this.historyLodginText = '已经加载全部';
						return;
					}
					this.page++;
					this.historyLodginText = '点击加载历史消息';
				})
			},

			back_up: function() {
				uni.navigateTo({
					url: "../../my/personal/comment-add"
				})
			},
			// 01 发送文本消息
			sendText: function(msg) {
				this.toSendSocket();
				this.pageScroll();
			},
			// 02 选择图片
			chooseImage: function(img) {
				this.toSendSocket();
				this.pageScroll();
			},
			// 03 发送语音消息
			sendVoice: function(voice) {
				this.toSendSocket();
				this.pageScroll();
			},
			// 滚动条滚动 [ 有新消息可以自动滚动到底部 ]
			pageScroll: function() {
				setTimeout(() => {
					uni.pageScrollTo({
						scrollTop: 999999 + Math.random(),
						duration: 100
					})
				}, 200);
			}
		},
	}
</script>
<style scoped>
	.file{
		width: 100rpx;
		height: 100rpx;
		background:linear-gradient(to bottom,#008AFF,#3a6eff);
		/* background-color: #3688FF; */
		color: #fff;
		border-radius: 100%;
		display: flex;
		justify-content: center;
		position: fixed;
		bottom: 25%;
		right: 30rpx;
		align-items: center;
	}
	.pendant {
		z-index: 9999;
		background-color: #F1F1F1;
		border-radius: 50rpx;
		width: 120rpx;
		height: 120rpx;
		border-radius: 100rpx;
		text-align: center;
		line-height: 120rpx;
		font-size: 22rpx;
		position: fixed;
		right: 20rpx;
		bottom: 150rpx;
	}

	page {
		background-color: #F7FBFE;
	}

	.end {
		text-align: center;
		margin-top: 120px;
		font-size: 12px;
		color: red;
		text-decoration: underline;
	}

	.online {
		position: fixed;
		width: 100%;
		background-color: red;
		text-align: center;
		margin-top: 0rpx;
	}

	.grace-im-system-msg {
		background: #C1C1C1;
		color: #FFF;
		font-size: 12px;
		line-height: 1.5em;
		padding: 5px 10px;
		margin-top: 30rpx;
		display: inline-block;
		border-radius: 3px;
	}
</style>
