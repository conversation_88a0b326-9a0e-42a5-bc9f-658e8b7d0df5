import Config from "../common.js"
import tools from '@/tools/utils/jsdecrypt.js'

const Http = function(url, method, data = {}, header = {}, config = {}) {
	return new Promise((resolve, reject) => {
		const baseUrl = Config.domain;
		if (url.startsWith('/')) {
			url = `${baseUrl}${url}`
		} else {
			url = url
		}
		try {
			var shopToken = uni.getStorageSync('mbtoken') ? uni.getStorageSync('mbtoken') : "";
		} catch (e) {

		}
		var urldata={
			url: url,
			data: data,
			method: method
		};
		uni.request({
			url,
			method,
			data,
			header: {
				"Content-Type": "application/json; charset=UTF-8",
				"Authorization": shopToken ? 'Bearer ' + shopToken : ""
				// "Content-Type": "application/json; charset=UTF-8",
				// "Authorization": wxToken ? 'wx ' + wxToken : ""
			},
			...config,
			success(res) {
				if (res.header['otherstr']) {
					res.data = tools.decryptData(res.data, res.header.otherstr)
				}
				console.log('http URL', url)
				console.log('http data', res)
				uni.hideLoading();
				if (res.data.code == 0 || res.data.code == 200) {
					if(res != null){resolve(res.data);}
				} else if (res.data.code == 401) {
					console.log('我进入401了')
					Config.getTokenInfo(urldata,resolve);
				} else {
					console.log('进来了500')
					setTimeout(() => {
						Config.msg(res.data.msg ? res.data.msg : res.data.msg,"none",3000);
					}, 500);
					reject(res)
				}
			},
			fail(err) {
				reject(err)
			}
		})
	})
}
Http.get = (url, data = {}, header = {}, config = {}) => Http(url, 'get', data, header, config);
Http.put = (url, data = {}, header = {}, config = {}) => Http(url, 'put', data, header, config);
Http.post = (url, data = {}, header = {}, config = {}) => Http(url, 'post', data, header, config);
Http.head = (url, data = {}, header = {}, config = {}) => Http(url, 'head', data, header, config);
Http.patch = (url, data = {}, header = {}, config = {}) => Http(url, 'patch', data, header, config);
Http.delete = (url, data = {}, header = {}, config = {}) => Http(url, 'delete', data, header, config);
Http.options = (url, data = {}, header = {}, config = {}) => Http(url, 'options', data, header, config);

export default Http;
