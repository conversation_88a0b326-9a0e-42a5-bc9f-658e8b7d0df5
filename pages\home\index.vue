<template>
	<view class="maincolor">
		<view class="gui-bg-white p-40">
			<view class="gui-flex gui-rows pr-20 gui-space-between">
				<view class="gui-text-small demo ">
					<view style="letter-spacing: 5rpx;" class="fs-40 gui-bold mb-20">
						<text>你好,{{userInfo.nickName}}医生</text>
					</view>
					<view><text class=" fs-30" style="color:#B2BAC6 ;">{{month}}，星期{{week}}。</text></view>
				</view>

				<view class="yisheng">
					<img style="width: 45%;height: 45%;" src="../../static/images/pone.png" />
				</view>
			</view>
			<view class="" v-if="roles.indexOf('管理员') != -1">
				<!-- 管理员医生接诊数据 -->
				<view class="py-40 databg">
					<view class="gui-flex gui-align-items-center   gui-wrap gui-justify-content-start gui-text-center">
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{healthData || 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">健康管理总人数</text>
						</view>
						<!-- 分割线 -->
						<view class="fgline"></view>
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{todayData|| 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">今日人流量</text>
						</view>
						<view class="fgline"></view>
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{totalToday || 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">总人流量数</text>
						</view>
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{adminInfo.sfzs|| 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">随访问卷总数</text>
						</view>
						<view class="fgline"></view>
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{adminInfo.xjzs|| 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">宣教总数</text>
						</view>
						<view class="fgline"></view>
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{adminInfo.jczs|| 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">监测总数</text>
						</view>
					</view>
				</view>
				<!-- 管理员折线图				 -->
				<view v-if="lineData.categories.length>0" class="mt-30">
					<view class="gui-flex gui-space-between mt-20 ai-center ">
						<view class=" gui-color-blue  ">
							就诊人流量趋势图
						</view>
						<view class=" gui-flex fs-20">
							<view @click="getList(index)" class="times-ion" :class="dateIndex == index ? 'times-ions':''"
								v-for="(item,index) in dateList">{{item.name}}</view>
						</view>
					</view>
					<view>
						<view class="line" v-if="lineData.categories.length>0">
							<qiun-data-charts type="mix" :opts="opts" :chartData="lineData"  :canvas2d="true" :ontouch="true" canvasId="0"/>
						</view>
					</view>
				</view>
				<!-- 管理员医生接诊榜 -->
				<view v-if="doctorRanking.length >0" class="content">
					<view class="fs-20 gui-color-blue ai-center">医生管理人数排行</view>
					<view style="margin-top:15rpx;font-size: 28rpx;" class="gui-flex gui-rows gui-nowrap"
						v-for="(item,index) in doctorRanking" :key="item.index">
						<view class=" demo2 gui-color-red">{{index+1}}</view>
						<view class=" demo3  gui-color-gray">{{item.nickName}}</view>
						<view class=" demo3 gui-color-gray">{{item.docTitle||"无"}}</view>
						<view class=" demo3 gui-color-gray">{{item.size||"-"}}</view>
					</view>
				</view>
			</view>
			<view class="" v-else>
				<!-- 普通医生接诊数据  -->
				<view class="py-40 databg">
					<view class="gui-flex gui-align-items-center   gui-wrap gui-justify-content-start gui-text-center">
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{doclist.jzrs||0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">今日接诊</text>
						</view>
						<!-- 分割线 -->
						<view class="fgline"></view>
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{doclist.dclsl|| 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">累计咨询数</text>
						</view>
						<view class="fgline"></view>
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{doclist.wdhz|| 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">我的患者数</text>
						</view>
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{doclist.xfgy|| 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">问卷干预数</text>
						</view>
						<view class="fgline"></view>
				
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{listInfo.jcsl|| 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">监测总数</text>
						</view>
						<view class="fgline"></view>
				
						<view class="gui-text-small mt-20 mb-20 u-text demo gui-bold fs-40 gui-color-white">
							<text class="dsplay-ib">{{listInfo.sfsl|| 0}}</text>
							<text class=" dsplay-ib gui-bold gui-text gui-color-white ">检测干预数</text>
						</view>
					</view>
				</view>
				<!-- 监测指标 -->
				<view class="">
					<span class="bqhead"></span>
					<view class="mt-20 mb-20 gui-flex gui-rows gui-space-between align-center">
						<view class="fs-32 gui-color-blue gui-bold">健康数据提示</view>
						<view v-if="monitor.length != 0" class="fs-28 demo  gui-color-blue gui-icons "
							@tap=" abnormal()">
							查看更多<img class="ml-10 pt-10" style="width: 13%;  " src="../../static/images/more.png" />
						</view>
					</view>
				</view>
				<view class="mt-15  px-30 py-20 b-radius-20 border-b mb-40">
					<view class="fs-24 gui-flex gui-justify-content-center gui-color-gray" v-if="monitor.length == 0">
						暂无数据 ~
					</view>
					<view class="gui-flex gui-space-between mb-20" v-for="(item,index) in monitor" :key="item.index">
						<view class="gui-flex ai-center">
							<view class="yuan"></view>
							<view class="fs-28 ml-10">{{$common.parseTime(item.create_time,'{m}-{d} {h}:{i}')}}</view>
							<view class="fs-28 ml-10">{{item.name}}</view>
							<view class="fs-28 gui-flex" v-if="item.sex == 1">
								<view
									class="gui-color-white ml-10 nan px-10 gui-border-radius gui-flex gui-align-items-center">
									{{item.age}}岁</view>
								<view class="ml-10" style="display: inline-block; ">
									<img style="width: 70%; " src="../../static/images/man1.png" />
								</view>
							</view>
							<view class="fs-28 gui-flex" v-else-if="item.sex == 2">
								<view
									class="gui-color-white ml-10 nv px-10 gui-border-radius gui-flex gui-align-items-center">
									{{item.age}}岁</view>
								<view class="ml-10" style="display: inline-block; "> <img style="width: 70%; "
										src="../../static/images/nv.png" /></view>
							</view>
							<view class="fs-28 gui-flex" v-else>
								<view
									class="gui-color-white ml-10 nan px-10 gui-border-radius gui-flex gui-align-items-center">
									{{item.age}}岁</view>
								<view class="ml-10" style="display: inline-block; ">
									<img style="width: 70%; " src="../../static/images/avatar-who.png" />
								</view>
							</view>
						</view>
						<view style="line-height: 15rpx;" @tap="abnormal(item)"
							class="gui-flex gui-text-small gui-flex fs-26 demo gui-bold  gui-align-items-center gui-justify-content-center">
							<block v-if="item.measure_result && item.measure_result.includes('异常')">
								<text class="c">
									<img style="width: 46%;height:25%;" src="../../static/images/icon.png" />
								</text>
								<text class="gui-color-red">{{item.measure_result}}</text>
							</block>
							<block v-else>
								<text>{{item.measure_result}}</text>
							</block>
							<img class="ml-20 hsjiantou" src="../../static/images/more-p.png" />
						</view>
					</view>
				</view>
			</view>
		</view>

	</view>

</template>

<script>
	import {getUserPwdFlag} from '@/api/login.js'
	import {
			mapState,
			mapMutations
		} from 'vuex';
	import url from "../../common.js";
	import LineChart from '@/components/stan-ucharts/LineChart.vue';
	// import * as dd from 'dingtalk-jsapi';

	export default {
		components: {
			LineChart,
		},
		data() {
			return {
				imUser: {
					userId: uni.getStorageSync('mbuserInfo').userId, // 系统用户id 空
					userUid: '', //链接im返回的uuid
					userType: '1', // 用户类型  1：医生  2：患者
					uname: uni.getStorageSync('mbuserInfo').userName, // 用户名
					nickName: uni.getStorageSync('mbuserInfo').nickName, // 昵称
					uface: uni.getStorageSync('mbuserInfo').avatar // 头像
				},
				opts: {
					enableScroll: true,
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4",
						"#ea7ccc"
					],
					padding: [40, 0, 40, 0],
					legend: {
						show: false //是否显示图例
					},
					xAxis: {
						disableGrid: true,
						rotateLabel: true,
						rotateAngle: 20,
						itemCount: 6,
						inverse:true
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						splitNumber: 5,
						gridType: "dash",
						dashLength: 4,
						padding: 10,
						showTitle: false
					},
					extra: {
						line: {
							type: "curve",
							width: 2
						},

						length: {
							width: 15
						},

					}
				},
				dateList: [{
					id: 6,
					name: '近半年'
				}, {
					id: 12,
					name: '近一年'
				}],
				// //性别
				// sexlits:[],
				month: {},
				week: {},
				userInfo: {}, //医生信息
				roles: [], //角色
				isRoles: 1,
				// 医生端统计数
				doclist: {},
				//普通医生数据
				listInfo: {},
				//管理员医生数据
				adminInfo: {},
				//监测指标
				monitor: {},
				//评估指标
				evaluation: {},
				//舌面相数据
				tongue: {},
				//排行榜数据
				doctorRanking: {},
				healthData: {},
				todayData: {},
				totalToday: {},
				taskInfo: {},
				//人群折线图
				//门诊情况折线图数据
				dateIndex: 0,
				lineData: {
					"categories": [],
					"type": "line",
					"series": [{
						"name": "",
						"data": []
					}]
				},
				code: '',
				ddCorpId: this.$common.ddId,
				bingingWxUser: this.$common.bingingWxUser //使用企业微信的环境用true，目前仅广中医true，其他环境是false
			}
		},
		onShow() {
			// console.log('**********',this.$common.bingingWxUser)
			this.checkLogin(); //检查登登录状态
		},
		onLoad(option) {
			// #ifdef H5
			//获取user-agaent标识头
			let ua = window.navigator.userAgent.toLowerCase();
			//判断 微信浏览器 或者 企业微信浏览器
			if (this.isWechat() || this.isWorkWechat()) {
				// TODO 医生端授权开关
				// alert('医生端授权开关'+this.bingingWxUser)
				if (this.bingingWxUser == true) {
					let params = this.getRequestParams();
					let code = params['code'];
					this.wxcode = code
					this.toWxLogin()
				}
			}
			// #endif
		},
		computed: {
			...mapState(['getMsgReadedNum','graceIMStatus','graceIMMsgs'])
		},
		methods: {
			// checkDing() {
			// 	let self = this;
			// 	// 先判断是否是在钉钉中运行此应用
			// 	if (dd.env.platform != 'notInDingTalk') {
			// 		// self.$common.msg('在钉钉环境中。。')
			// 		//钉钉环境下未绑定钉钉用户的触发 关联绑定
			// 		if (!uni.getStorageSync('mbddUserId')) {
			// 			dd.ready(() => {
			// 				// self.$common.msg('钉钉sdk 准备就绪！')
			// 				dd.runtime.permission.requestAuthCode({
			// 					corpId: self.ddCorpId
			// 				}).then((result) => {
			// 					self.code = result.code;
			// 					// self.$common.msg('钉钉code获取：'  + self.code)
			// 					self.$common.RequestData({
			// 						url: self.$common.dingBindUser,
			// 						data: {
			// 							password: self.code,
			// 							userId: self.userInfo.userId
			// 						}
			// 					}, res => {
			// 						// self.$common.msg('钉钉请求接口返回：'  + JSON.stringify(res))
			// 						if (res.code === 200) {
			// 							uni.setStorageSync("mbddUserId", res.data.ddUserId)
			// 						}
			// 					});
			// 				}).catch(err => {
			// 					self.$common.msg('获取钉钉用户异常：' + JSON.stringify(err))
			// 				});
			// 			});

			// 		}

			// 	} else {
			// 		console.warn('请在钉钉中访问本应用!');
			// 	}
			// },
			checkLogin() {
				let self = this;
				this.$common.checkLogin(res => {
					if (res === 0) {
						//跳转登录页面
						self.$common.navLaunch('/pages/login/index')
					} else if (res > 0) {
						this.getPageData();//初始化

					}
				});
			},
			getPageData() {
				this.getInfo();//获取角色信息
				this.getToday();//获取当前日期信息
				this.getpwdStatus();//检查密码过期判断修改提醒
			},
			getpwdStatus(){//检查密码过期判断修改提醒
				let that = this;
				getUserPwdFlag().then(res =>{
					// console.log('检查密码',res)
					if (res.data.pwdStatus) {
						uni.showModal({
							title: '提示',
							content: '密码过期，请重新设置！',
							showCancel:false,
							success: function (res) {
								if (res.confirm) {
									that.$common.navLaunch('/pages/my/revise')
								} else if (res.cancel) {
									that.getCode();
								}
							}
						});
					}
				})
				return
			},
			//用户信息
			getInfo() {
				this.$common.RequestData({
					url: this.$common.getInfo,
					data: {},
					method: 'get',
				}, res => {
					this.userInfo = res.data.user;
					uni.setStorageSync("mbuserInfo", res.data.user)
					this.roles = res.data.posts;
					if (res.data.posts.indexOf("管理员") != -1 ) {
						console.log('当前角色是管理员')
						this.getRanking();//管理员医生排行榜
						this.getList();//管理员折线图数据
						this.admin();//管理员数据统计
					} else {
						console.log('当前角色是普通医生')
						this.selectDoctorStatistical()// 医生端统计
						this.getData();//统计数据
						this.getMonitorAndEva();//普通医生监测评估
						// 升级版本
						this.gettongue();
					}
					if (uni.getStorageSync("mbuserInfo") && this.graceIMStatus !=='success') {
						//发起IM登录
					  this.$store.dispatch('graceIMConnect', this.imUser).then(res=>{
					  });
					}
					// this.checkDing();
				}, )
			},

			//统计数据
			getData() {
				//普通医生
				this.$common.RequestData({
					url: this.$common.QuantityData,
					data: {},
					method: 'get',
				}, res => {
					this.listInfo = res.data;
				}, )

			},
			// 医生端统计
			selectDoctorStatistical() {
				//普通医生
				this.$common.RequestData({
					url: this.$common.selectDoctorStatistical,
					data: {},
					method: 'get',
				}, res => {
					console.log("医生统计数据doclist", this.doclist)
					this.doclist = res.data;
				}, )

			},
			//普通医生监测评估
			getMonitorAndEva() {
				this.$common.RequestData({
					url: this.$common.selectPerformsList,
					data: {},
					method: 'get',
				}, res => {
					this.monitor = res.data
				})
			},
			gettongue() {
				this.$common.RequestData({
					url: this.$common.getLingualWarningList,
					data: {
						isRead: 0
					},
					method: 'get',
				}, res => {
					this.tongue = res.rows;
				}, )
			},

			//管理员医生排行榜
			getRanking() {
				this.$common.RequestData({
					url: this.$common.doctorRanking,
					data: {},
					method: 'get',
				}, res => {
					res.data = res.data.sort(function(a, b) {
						return b.size - a.size;
					})
					this.doctorRanking = res.data;
				}, )
			},
			//今日时间
			getToday() {
				new Date().getTime();
				let month = this.$common.parseTime(new Date(), '{m}月{d}日');

				new Date().getTime();
				let week = this.$common.parseTime(new Date(), '{a}');
				this.month = month;
				this.week = week;
			},
			//管理员折线图数据
			getList(type) {
				this.dateIndex = type !== undefined? type : this.dateIndex;
				this.initData();
				this.$common.RequestData({
					url: this.$common.Consultation,
					data: {
						dateIndex:this.dateList[this.dateIndex].id
					},
					method: 'get'
				}, res => {
					let list = res.data.xData.reverse()
					let arr = res.data.expectedData.reverse()
					// console.log(list,arr,'管理员折线图数据')
					this.lineData = {
						categories: list,
						series: [{
							name: '',
							type: "line",
							data: JSON.parse(JSON.stringify(arr)),
						}]
					};
					this.getServerData();
				}, )

			},
			//初始化图表数据
			initData() {
				//门诊情况折线图数据
				this.lineData = {
					categories: [],
					series: [{
						name: '接诊人数',
						data: []
					}]
				};
			},
			getServerData() {
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
					this.$refs['lineData'].showCharts();
				}, 500);
			},

			admin() {
				new Date().getTime();
				let todaydata = this.$common.parseTime(new Date(), '{y}-{m}-{d}');
				//统计总数
				this.$common.RequestData({
					url: this.$common.statistics,
					data: {},
					method: 'get',
				}, res => {
					this.adminInfo = res.data;
				}, )
				//健康管理总数
				this.$common.RequestData({
					url: this.$common.healthManage,
					data: {},
					method: 'get',
				}, res => {
					this.healthData = res.data;
				}, )
				//今日人流量
				this.$common.RequestData({
					url: this.$common.Appointment,
					data: {
						startDate: todaydata,
						endDate: todaydata,
					},
					method: 'get',
				}, res => {
					this.todayData = res.data.AppointmentCount || '';
				}, )
				//今日总流量
				this.$common.RequestData({
					url: this.$common.getCaseCount,
					data: {
						startDate: "",
						endDate: "",
					},
					method: 'get',
				}, res => {
					this.totalToday = res.data.CaseCount;
				}, )
				//康养总数
				this.$common.RequestData({
					url: this.$common.getTaskInfo,
					data: {},
					method: 'get',
				}, res => {
					this.taskInfo = res.data;
				}, )

			},

			abnormal: function(item) {
				if (item) {
					this.gotongue(item)
					return
				}
				this.$common.navTo('/pages/home/<USER>')
			},
			gotongue(item) {
				this.$common.RequestData({
					url: this.$common.readLingual + item.id,
					data: {},
					method: 'get',
				}, res => {
					if (res.code == 200) {
						// this.$common.navTo('/pages/patient/details?patientId=' + item.patientId + '&monitorId=' +
						// 	item.monitorId)
						this.$common.navTo('/pages/patient/details')
						uni.setStorageSync('mbdetailsPatientId',item.patientId)
						uni.setStorageSync('mbmonitorId',item.monitorId)
					} else {
						this.$common.msg("数据异常，请稍后刷新重试~")
					}
				}, )

			},
			assess: function() {
				// 评估
				this.$common.navTo('/pages/home/<USER>')
			},
			toWxLogin(type) {
				//type=login的时候，说明是历史账户安全登录，不需要微信登录逻辑
				
				if (uni.getStorageSync("logintype")) {return}
				// 微信登录逻辑
				if (!this.wxcode) {
					// alert('微信登录逻辑')
					let redirect_uri = window.location.href
					console.log('redirect_uri', redirect_uri)
					// 1000066--慢病正式环境
					// 1000018--公司测试环境
					// let uri =
					// 	'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx51f5aff0f538ed8b&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE&agentid=1000066#wechat_redirect'
					let uri =
						'https://open.weixin.qq.com/connect/oauth2/authorize?appid='+this.$common.appid+'&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE&agentid='+this.$common.agentid+'#wechat_redirect'
					window.location.href = uri.replace("REDIRECT_URI", redirect_uri)
				} else {
					this.toBinding()
				}
			},
			getRequestParams() {
				let url = location.href;
				let requestParams = {};
				if (url.indexOf('?') !== -1) {
					let str = url.substr(url.indexOf('?') + 1); //截取?后面的内容作为字符串
					console.log(str, '?后面的内容');
					let strs = str.split('&'); //将字符串内容以&分隔为一个数组
					console.log(strs, '以&切割的数组');
					for (let i = 0; i < strs.length; i++) {
						requestParams[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1]);
						// 将数组元素中'='左边的内容作为对象的属性名，'='右边的内容作为对象对应属性的属性值
					}
				}
				return requestParams;
			},
			toBinding() {
				// 向后台发起请求 携带code
				let queryParams = {
					code: this.wxcode
				}
				this.$common.RequestData({
					url: this.$common.toBinding,
					data: queryParams,
					method: "get"
				}, res => {
					uni.setStorageSync('mbwxUserId',rs.data.wxUser)
					this.wxcode = ''
				}, null, err => {
					console.log(err);
				})
			},
			isWechat() {
				//获取user-agaent标识头
				var ua = window.navigator.userAgent.toLowerCase();
				//判断ua和微信浏览器的标识头是否匹配
				if (ua.match(/micromessenger/i) == 'micromessenger') {
					return true;
				} else {
					return false;
				}
			},
			isWorkWechat() {
				//获取user-agaent标识头
				var ua = window.navigator.userAgent.toLowerCase();
				//判断ua和微信浏览器的标识头是否匹配
				if ((ua.match(/micromessenger/i) == 'micromessenger') && (ua.match(/wxwork/i) == 'wxwork')) {
					return true;
				} else {
					return false;
				}
			},
		},
	}
</script>

<style scoped>
	.ai-center{
		font-size: 32rpx;
	}
	page {
		background-color: white;
	}

	.demo3 {
		width: 210rpx;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		margin: 10rpx;
	}

	.demo2 {
		line-height: 88rpx;
		text-align: center;
		margin: 10rpx;
	}

	.maincolor {
		background-color: white;
		height: 10vh;
	}

	.u-text {
		width: 32%;
	}

	.yisheng {
		height: 40%;
		display: flex;
		flex-direction: row-reverse;
	}

	.databg {
		background-image: url(../../static/images/databg-q.png);
		background-repeat: no-repeat;
		background-size: cover;
		border-radius: 30rpx;
	}

	.demo1 {
		display: flex;
		justify-content: space-evenly;
	}

	.border-a {
		border: 1px solid;
		border-radius: 10px;
	}

	.times-ion {
		padding: 5rpx 20rpx;
		border: 1px solid #5e5e5e;
		margin-right: 10rpx;
		border-radius: 10rpx;
	}

	.times-ions {
		border: 2px solid #7784eb;
		color: #7784eb;
		font-weight: bold;
	}

	.border-b {
		/* box-shadow: 5px 10px 10px 5px #ebe8eb; */
		border: unset;
	}

	.line-height-30 {
		line-height: 30rpx;
	}

	.u-width {
		width: 90rpx;
	}

	.dsplay-ib {
		display: block;
	}

	.fgline {
		height: 60rpx;
		width: 4rpx;
		border-right: 4rpx solid #899df2;
		display: inline-block;
	}

	.wmtb {
		line-height: 26rpx;
		border-radius: 5rpx;
		width: 70rpx;
		height: 24rpx;
		text-align: center;
		font-size: 20rpx;
		display: inline-block;
		background-repeat: no-repeat;
	}

	.nv {
		background-image: url(../../static/images/womanbg.png);
	}

	.nan {
		background-image: url(../../static/images/manbg.png);
	}

	.dashed {
		height: 24rpx;
		width: 20%;
		border-bottom: 4rpx dashed #edeaed;
	}

	.hsjiantou {
		width: 16rpx;
		height: 50%;
	}

	.content {
		padding-bottom: var(--window-bottom);
	}

	/* 圆点 */
	.yuan {
		width: 8px;
		height: 8px;
		border-radius: 50%;
		line-height: 8px;
		background-color: #919191;
		display: flex;
	}

	/* 标签头 */
	.bqhead {
		background: #008AFF;
		width: 8rpx;
		height: 30rpx;
		position: absolute;
		left: 25rpx;
		margin-top: 3px;
		z-index: 99;
	}

	.px-30 {
		padding-left: 0rpx !important;
	}
</style>
