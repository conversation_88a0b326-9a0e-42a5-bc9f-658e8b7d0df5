1、用以下命令安装钉钉插件：
npm install dingtalk-jsapi

2、在相应vue页面引入插件：
	import * as dd from 'dingtalk-jsapi';
	
3、添加js方法获取用户code
		  checkDing() {
		  	// 先判断是否是在钉钉中运行此应用
		    if (dd.env.platform != 'notInDingTalk') {
		      dd.ready(()=>{
		        dd.runtime.permission.requestAuthCode({corpId: 'ding288e6c6c4c5a33ee35c2f4657eb6378f'}).then((result) => {
		          this.code = result.code;
		        }).catch(err => {
		          console.log(err);
		        });
		      });
		    } else {
		      console.warn('请在钉钉中访问本应用!');
		    }
		  },	
		  
注意：原先贵阳打包运行路径/h5/  广中医没有，最后已经统一需要设置为/H5/,广中医叶不列外。