<template>
	<view style="height:  100vh;">
		<view class="mx-40 my-20" v-if="!pdfUrl">
			<view class="gui-text-center gui-bold fs-30"> <text>{{jylist.docTitle}}</text></view>
			<view style="margin-top:15rpx" class="gui-flex gui-space-between">
				<view class="gui-text-small ">姓名：{{jylist.patientName}}</view>
				<view class="gui-text-small   ">性别：{{jylist.gender== 1 ?'男':'女'}}</view>
				<view class="gui-text-small ">年龄：{{jylist.age}}</view>
			</view>
			<view style="margin-top:15rpx" class="gui-flex gui-space-between">
				<view class="gui-text-small ">科室：{{jylist.applyDept}}</view>
				<view class="gui-text-small   ">医生：{{jylist.applyDoctor}}</view>
				<view class="gui-text-small "></view>
			</view>
			<view class="gui-table  gui-border-t" style="margin-top:50rpx; ">
				<table-cp :arr="jylist.executItems"></table-cp>
				<!-- <view class=" gui-flex flex-coloum ">

					<view
						class="gui-td1 fs-30  p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex  gui-bold   ">
						项目</view>
					<view class="shsj gui-td2 gui-border-r gui-border-b gui-td-text   px-20 fs-28"><text
							class="item-text" v-for="item in jylist.executItems">{{item.itemName}}</text></view>
				</view>
				<view class=" gui-flex flex-coloum">
					<view
						class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex  gui-bold  ">
						检查结果</view>
					<view class="shsj gui-td2 gui-border-r gui-border-b gui-td-text  px-20 fs-28 text-num"> <text
							class="item-text" v-for="item in jylist.executItems">{{item.itemResult}}</text> </view>
				</view>
				<view class=" gui-flex  flex-coloum">
					<view
						class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex gui-bold  ">
						参考值</view>
					<view class="shsj gui-td2 gui-border-r gui-border-b gui-td-text px-20 fs-28 text-num"> <text
							class="item-text" v-for="item in jylist.executItems">{{item.itemValue|| '-'}}</text> </view>
				</view>
				<view class=" gui-flex flex-coloum ">
					<view
						class="gui-td1 fs-30   p-20  td gui-border-r gui-border-b gui-td-text gui-align-items-center gui-border-l gui-flex  gui-bold ">
						单位</view>
					<view class="shsj gui-td2 gui-border-r gui-border-b gui-td-text px-20 fs-28 text-num"> <text
							class="item-text" v-for="item in jylist.executItems">{{item.itemUnit|| '-'}}</text> </view>
				</view> -->
				<view style="margin-top:15rpx" class="gui-flex gui-space-between">
					<view class="gui-text-small ">检查员：{{jylist.gatherer|| '-'}}</view>
					<view class="gui-text-small   "></view>
					<view class="gui-text-small ">检验日期：{{jylist.executeTime|| '-'}}</view>
				</view>
				<view style="margin-top:15rpx" class="gui-flex gui-space-between">
					<view class="gui-text-small ">报告人：{{jylist.reportor|| '-'}}</view>
					<view class="gui-text-small   "></view>
					<view class="gui-text-small ">报告日期：{{jylist.reportTime|| '-'}}</view>
				</view>
			</view>
		</view>
		<view v-if="pdfUrl" style="height: 100%;">
			<iframe :src="pdfUrl" width="100%" height="100%"/>
		</view>
	</view>
</template>

<script>
	import tableCp from './component/table.vue'
	export default {
		components:{tableCp},
		data() {
			return {
				info: '',
				jylist: [],
				jyapplyItem: [],
				pdfUrl:''
			}
		},
		onLoad: function(option) {
			this.info = JSON.parse(option.info);
			this.report();
		},
		methods: {
			report() {
				this.$common.RequestData({
					url: this.$common.getExamination + '/' + this.info.id,
					data: {},
					method: 'get',
				}, res => {
					this.jylist = res.data;
					// this.jyapplyItem = res.data.executItems;
					if (res.data?.executItems[0]?.imagUrls?.includes('.pdf')) {
						this.pdfUrl = res.data.executItems[0].imagUrls
					}
				}, )
			},
		}
	}
</script>

<style scoped>
	.text-num {
		word-break: break-all;
	}

	.item-text {}

	.demo {
		width: 210rpx;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		margin: 10rpx;
	}

	.demo-auto-width {
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		margin: 10rpx;
	}

	.gui-text-small {
		font-size: 20rpx;
	}

	.gui-td1 {
		display: flexbox;
	}

	.gui-td2 {
		display: flexbox;
	}

	.td {
		color: #7784eb;
	}

	.shsj {
		text-indent: 2em
	}
</style>
