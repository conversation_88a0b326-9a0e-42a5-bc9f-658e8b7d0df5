<template>
  <view class="pdf-content">
    <!-- 加载状态遮罩 -->
    <view v-if="isLoading" class="loading-overlay">
      <view class="loading-container">
        <view class="loading-spinner"></view>
        <view class="loading-text">正在加载PDF...</view>
        <view v-if="loadingProgress > 0" class="loading-progress">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: loadingProgress + '%' }"></view>
          </view>
          <text class="progress-text">{{ loadingProgress }}%</text>
        </view>
      </view>
    </view>

    <!-- PDF 组件 -->
    <vue-pdf-embed
        v-if="path"
        annotation-layer
        text-layer
        :source="{
		        url: path,
		        cMapUrl: fontLink,
		        cMapPacked: true,
		      }"
        :height="'95vh'"
        :width="375"
        @progress="handleProgress"
        @loaded="handlePdfMsg"
        @loading-failed="handleLoadingFailed"
        @rendered="handleRendered"
    />

    <!-- 加载失败提示 -->
    <view v-if="loadingError" class="error-container">
      <view class="error-icon">⚠️</view>
      <view class="error-text">PDF加载失败</view>
      <view class="error-message">{{ errorMessage }}</view>
      <button class="retry-button" @click="retryLoading">重新加载</button>
    </view>

    <!-- <view style="position: absolute;bottom: 20rpx;">
      <button @click="lastPages()">上一页</button>
      <button @click="nextPages()">下一页</button>
    </view> -->
  </view>
</template>

<script>
import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'
import { previewNutritionInterventionPDF } from '@/api/patient'
export default {
  components: {
    VuePdfEmbed,
  },
  data() {
    return {
      // pdf地址
      path: "",
      // 文字链接
      fontLink: "https://unpkg.com/pdfjs-dist@2.12.313/cmaps/",
      // 分页
      pages: 1,
      sumPages: 0,
      // 患者ID和就诊ID
      patientId: "",
      visitId: "",
      // 加载状态
      isLoading: false,
      loadingProgress: 0,
      loadingError: false,
      errorMessage: ""
    }
  },
  onLoad(option) {
    console.log('PDF预览页面接收到的参数:', option)

    // 如果直接传入了PDF路径，使用传入的路径
    if (option.path) {
      this.startLoading()
      this.path = option.path
    } else if (option.patientId && option.visitId) {
      // 如果传入的是患者ID和就诊ID，构建PDF URL
      this.patientId = option.patientId
      this.visitId = option.visitId
      this.getPdfUrl()
    }

    uni.setNavigationBarTitle({
      title:'营养干预方案报告'
    })
  },
  methods: {
    // 获取PDF预览地址
    getPdfUrl() {
      if (!this.patientId || !this.visitId) {
        uni.showToast({
          title: '缺少必要参数',
          icon: 'none'
        })
        return
      }

      this.startLoading()

      previewNutritionInterventionPDF({
        patientId: this.patientId,
        visitId: this.visitId
      }).then(res => {
        // 将返回的URL赋值给path，拼接前缀
        this.path = this.$common.domain + res.data.url
      }).catch(error => {
        console.error('获取PDF地址失败:', error)
        this.handleLoadingFailed(error)
      })
    },

    // 开始加载
    startLoading() {
      this.isLoading = true
      this.loadingProgress = 0
      this.loadingError = false
      this.errorMessage = ""
    },

    // 处理加载进度
    handleProgress(progressParams) {
      if (progressParams && progressParams.loaded && progressParams.total) {
        this.loadingProgress = Math.round((progressParams.loaded / progressParams.total) * 100)
      }
    },

    // 处理加载失败
    handleLoadingFailed(error) {
      console.error('PDF加载失败:', error)
      this.isLoading = false
      this.loadingError = true
      this.errorMessage = error.message || '网络连接异常，请检查网络后重试'

      uni.showToast({
        title: 'PDF加载失败',
        icon: 'none',
        duration: 2000
      })
    },

    // 处理渲染完成
    handleRendered() {
      this.isLoading = false
      console.log('PDF渲染完成')
    },

    // 重新加载
    retryLoading() {
      this.loadingError = false
      if (this.patientId && this.visitId) {
        this.getPdfUrl()
      } else if (this.path) {
        this.startLoading()
        // 触发重新渲染
        const currentPath = this.path
        this.path = ""
        this.$nextTick(() => {
          this.path = currentPath
        })
      }
    },
    lastPages() {
      if(this.pages > 1) {
        this.pages -= 1
      }
    },
    nextPages() {
      if(this.pages < this.sumPages) {
        this.pages += 1
      } else {
        this.pages = 1
      }
    },
    // 获取pdf信息 numPages 总页数
    handlePdfMsg(val) {
      this.sumPages = val._pdfInfo.numPages
      this.isLoading = false
      console.log('PDF加载完成，总页数:', this.sumPages)
    }
  },

}
</script>

<style scoped>
.pdf-content {
  position: relative;
  width: 100%;
  height: 100vh;
}

/* 加载遮罩层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
}

/* 加载动画 */
.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 30rpx;
}

/* 进度条 */
.loading-progress {
  width: 400rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background-color: #007aff;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误提示 */
.error-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  z-index: 9999;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 60rpx;
}

.retry-button {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 32rpx;
  cursor: pointer;
}

.retry-button:active {
  background-color: #0056cc;
}
</style>
