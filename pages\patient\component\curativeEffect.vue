<template>
	<scroll-view :scroll-y="true" style="height:100%;padding: 20rpx;">
		<view class="">
			<view class="">
				<!-- <view class=""> -->
					<view class="l-timeline-l" v-for="(item, key, index) in getEvaluationData">
						<view class="time-line">
							<view class="l-icon"></view>
							<view class="time-line-i"></view>
						</view>
						<view style="flex: 1;padding-left: 20rpx;">
						<view class="l-time fs-26" style="color: rgb(144, 147, 153);">
							{{item.createTime}}
							<block v-if="item.evaluationType == 1">
							<span class=" ml-20 fs-26" style="color: #1d1d1d;"
								v-if="item.detailedEvaluationResults == 1">疗效评估:&ensp;好转</span>
							<span class=" ml-20 fs-26" style="color: #1d1d1d;"
								v-if="item.detailedEvaluationResults == 2">疗效评估:&ensp;一般</span>
							<span class=" ml-20 fs-26" style="color: #1d1d1d;"
								v-if="item.detailedEvaluationResults == 3">疗效评估:&ensp;无进展</span>
							<span class=" ml-20 fs-26" style="color: #1d1d1d;"
								v-if="item.detailedEvaluationResults == 4">疗效评估:&ensp;显著</span>
							<span class=" ml-20 fs-26" style="color: #1d1d1d;"
								v-if="item.detailedEvaluationResults == 5">疗效评估:&ensp;痊愈</span>
							</block>
							<block v-if="item.evaluationType == 2">
								<span class=" ml-20 fs-26" style="color: #1d1d1d;">问卷评估:&ensp;{{item.detailedEvaluationResults || 0}}分</span>
							</block>
						</view>
						<!-- <view class="l-icon"></view> -->

						<view class="l-content laioxiaoFu">
							<view class="laioxiaoZi">
								<!-- <text class="gui-icons Aios">&#xe624;</text> -->
								<view class="">
									<view class="fs-26">
										评估人:&ensp;{{item.createBy}}
									</view>
									<view class="fs-26" v-if="item.evaluationCycleStart && item.evaluationCycleEnd">
										评估周期:&ensp;{{item.evaluationCycleStart}} 至
										{{item.evaluationCycleEnd}}
									</view>
									<view class="fs-26" v-else>
										评估周期:&ensp;{{$common.parseTime(item.createTime,'{y}-{m}-{d}')}}
									</view>
									<view class="fs-26">
										评估描述:&ensp;{{item.resultDescription}}
									</view>
								</view>
							</view>
						</view>
						</view>
					</view>
					<gui-empty
						v-if="getEvaluationData.length == 0">
						<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
							<!-- 请根据您的项目要求制作并更换为空图片 -->
							<image class="gui-empty-img"
								src="https://images.weserv.nl/?url=https://upload-images.jianshu.io/upload_images/15372054-1f849183cebb80b1.png"></image>
						</view>
						<text slot="text"
							class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#9DABFF;">暂无数据 ......</text>
					</gui-empty>
				<!-- </view> -->
			</view>
		</view>
	</scroll-view>
</template>

<script>
	export default {
		props:{
			info:{
				type:Object,
				default:{}
			},
		},
		data(){
			return {
				getEvaluationData:[]
			}
		},
		mounted() {
			// this.getEvaluationList()
		},
		methods:{
			getEvaluationList(patientId) {
				this.$common.RequestData({
					url: this.$common.getEvaluationList,
					data: {
						patientId: patientId?patientId:this.info.patientId,
						// patientId: "452731198502041246",
						orderByColumn: "create_time",
						isAsc: "desc"
					},
					method: 'get',
				}, res => {
					this.getEvaluationData = res.rows

				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.item-row{
		flex:1;
		display: flex;
		justify-content: space-between;
	}
	.gui-accordion{
		background-color: #f5f6f8;
		padding:20rpx;
		border-radius: 10rpx;
	}
	.list-item-name{
		font-weight: bold;
	}
	.list-item{
		padding: 10rpx 0;

	}
	.time-line{
		display: flex;
		flex-direction: column;
		width: 30rpx;
		position: relative;
		justify-content: center;
		align-items: center;
		.time-line-i{
			width: 2rpx;
			background-color: #aaa;
			height: 100%;
		}
	}
	.gui-rows-title{
		flex:1
	}
	.visitDate-row{
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}
	.navscroll{
		white-space: nowrap;
		// height: 100rpx;
		::v-deep ::-webkit-scrollbar {
			width: 4px !important;
			height: 1px !important;
			overflow: auto !important;
			background: transparent !important;
			-webkit-appearance: auto !important;
			display: block;
		}
		.garce-items{
			display: inline-block; // 设置为行内块
		}
	}
	.prescription{
		display: flex;
		flex-direction: row;
		flex-flow:row wrap;
	}
	.prescription-item{
		width: 30%;
		line-height: 26px;
		text-align: center;
	}
	>>>.gui-scroll-x-items {
		align-items: center;
	}

	.demo-nav {
		padding: 15rpx 30rpx;
	}

	.demo-text {
		line-height: 200rpx;
		padding-bottom: 3000px;
	}

	.grace-box-banner .garce-items .line2 {
		font-size: 28rpx;
		color: #008AFF;
		display: inline-block; // 设置为行内块
		border: 1px solid #62dbff;
		padding: 0 15rpx;
		margin: 0 10rpx;
		border-radius: 20rpx;
	}

	.line2.active {
		color: #ffffff !important;
		/* font-weight:bold; */
		background-color: #008AFF;
	}

	.l-timeline-l {
		display: flex;
		flex-direction: row;
		// border-left: 1px solid #aaaaaa;
	}

	.l-timeline-b {
		/* border-bottom: 2px solid #008AFF; */
	}

	.l-time {
		// position: relative;
		// top: -15rpx;
	}

	.acolor {
		background-color: #7784eb;

	}

	.bcolor {
		background-color: #7784eb;
		height: 40rpx;
		line-height: 40rpx;
		width: 190rpx;
	}

	.tagicon {
		margin-right: 10rpx;
		height: 40rpx;
		width: 6rpx;
		border-radius: 5rpx;
		background: #008AFF;
		display: block;
	}

	.l-icon {
		background: #008AFF;
		width: 25rpx;
		height: 25rpx;
		border-radius: 25rpx;
		position: absolute;
		top: 0;
		// position: relative;
		// top: -50rpx;
		// left: -15rpx;
	}

	.l-content {
		padding-bottom: 30px;
		margin-top: 30rpx;
		margin-bottom: 30rpx;
		// position: relative;
		// top: -25rpx;
	}

	.gui-accordion-icon {
		width: 50rpx;
		// height: 80rpx;
		// line-height: 80rpx;
		font-size: 32rpx;
	}

	.gui-flex-direction-row {
		flex-direction: row-reverse;
	}

	.gui-accordion-title-text {
		// width: 200rpx;
		flex: 1;
	}

	>>>.gui-block-text {
		font-size: 30rpx !important;
	}

	.resuimg {
		width: 24px;
		height: 24px;
		font-size: 24px;
		line-height: 24px;
		vertical-align: middle;
		color: rgba(0, 186, 173, 1);
	}

	.resutext {
		width: 70px;
		height: 21px;
		font-size: 14px;
		text-align: left;
		font-weight: bold;
		line-height: 24px;
		padding-left: 8rpx;
		color: rgba(80, 80, 80, 1);
	}

	.propose_net {
		width: 100%;
		height: auto;
		margin-top: 5rpx;
		padding: 0 10rpx;
		text-indent: 2em;
		line-height: 52rpx;
		overflow-x: scroll;
	}

	.end_text {
		width: 325px;
		height: 42px;
		font-size: 14px;
		text-align: center;
		margin: 20% auto 0;
		color: rgba(212, 48, 48, 1);
	}

	.propose {
		width: 92%;

	}


	/* 干预 */
	.ganyuFu {
		background-color: #f5f6f8;
		line-height: 50rpx;
		padding-top: 10rpx;
		width: 85%;
		max-width: 85%;
		border-radius: 10rpx;
		padding: 5rpx 10rpx 5rpx 5rpx;
	}

	.ios {
		font-size: 35rpx;
		margin: 10rpx 10rpx;
		color: #1aca0d;
	}

	/* 疗效评估 */
	.laioxiaoFu {
		/* display: flex; */
		background-color: #f5f6f8;
		// line-height: 50rpx;
		padding-top: 10rpx;
		// width: 85%;
		// max-width: 85%;
		border-radius: 10rpx;
		padding: 5rpx 10rpx 5rpx 5rpx;
	}

	.laioxiaoZi {
		display: flex;
		align-items: center;
		line-height: 50rpx;
		padding: 10rpx 0rpx 10rpx 15rpx;
	}

	.Aios {
		font-size: 50rpx;
		margin: 10rpx 20rpx 10rpx 10rpx;
		color: #1aca0d;
		/* height: 100% !important; */
		display: flex;
		align-items: center;
		float: left;
	}

	.yuandian {
		float: left;
		display: block;
		line-height: 50rpx;
		top: 10px;
		width: 8px;
		height: 8px;
		border-radius: 20px;
		/* background: #cbd0db; */
		background: #6f6f6f;
		margin-top: 8px !important;
		margin: 8px;

	}

	/* 舌面详情按钮 */
	.sm_xq {
		float: right;
		margin-right: 30rpx;
	}

	/* 头像 */
	.head_img {
		border-radius: 10rpx;
		border: 1px solid #cdcdcd;
	}

	>>>.gui-list-title-text {
		font-size: 28rpx !important;
	}
</style>
