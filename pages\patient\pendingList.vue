<template>
	<view>
		<gui-page ref="guipage" :isLoading="pageLoading" :refresh="true" @reload="reload">
			<view slot="gBody" class="gui-flex1 gui-flex gui-columns" style="position: relative; width: 100%; height: 100%;">
				<!-- 选项卡导航 - 统一样式 -->
				<view class="demo-nav gui-flex">
					<view class="demo-nav-my" @tap="switchTab('manual')">
						<view :class="activeTab === 'manual' ? 'demo-nav-my-name-i' : 'demo-nav-my-name'">手工建档</view>
						<view :class="activeTab === 'manual' ? 'nav-active-line' : 'nav-active-line-i'"></view>
					</view>
					<view class="demo-nav-my" @tap="switchTab('recommended')">
						<view :class="activeTab === 'recommended' ? 'demo-nav-my-name-i' : 'demo-nav-my-name'">推荐建档</view>
						<view :class="activeTab === 'recommended' ? 'nav-active-line' : 'nav-active-line-i'"></view>
					</view>
				</view>

				<!-- 推荐建档内容 - 患者列表 -->
				<view v-if="activeTab === 'recommended'" class="tab-content recommended-tab" style="    background: rgb(248, 249, 250);">
					<!-- 搜索框 -->
					<view class="search-container" style="padding: 20rpx; background: #f8f9fa;">
						<gui-search placeholder="输入姓名/就诊卡号/住院号/诊断查询" @inputting="inputting" @confirm="inputConfirm()" :kwd="queryParams.patientName" class="search-input" @clear="clearSearch" style="border: 0.5px solid #a6a6a7;"></gui-search>
					</view>

					<!-- 内容区域 - 使用fixed定位撑满空间 -->
					<view class="content-container">
						<!-- 患者列表 -->
						<scroll-view
							class="patient-scroll-view"
							:scroll-y="true"
							:style="{height: mainHeight + 'px'}"
							@scrolltolower="scrolltolowerFun"
							@scroll="onScroll"
							:lower-threshold="30"
							:refresher-threshold="100"
							:enable-back-to-top="true">
							<view class="gui-flex gui-columns mx-20 mt-20" v-if="list.length > 0">
								<view class="patient-item" v-for="(item, index) in list" :key="item.id" @click="toggleSelection(item.id)">
									<!-- 单选框 -->
									<view class="checkbox-container">
										<radio :checked="selectedPatient === item.id" color="#7784eb" style="pointer-events: none;"></radio>
									</view>

									<!-- 患者信息 -->
									<view class="patient-info">
										<!-- 第一行：姓名、性别、年龄、手机号 -->
										<view class="patient-basic-info">
											<text class="patient-name">{{item.patientName}}</text>
											<text class="patient-details">{{sexText(item.sex)}} {{getAge(item.birthday)}}岁 {{item.phone && item.phone.replace(/(\d{3})\d*(\d{4})/, "$1****$2")}}</text>
										</view>

										<!-- 第二行：就诊卡号 -->
										<view class="card-info">
											<text class="card-label">住院号</text>
											<text class="card-number">{{item.visitCardNum && item.visitCardNum.length > 15 ? item.visitCardNum.replace(/(\d{6})\d*([0-9a-zA-Z]{2})/, "$1******$2") : item.visitCardNum}}</text>
										</view>

										<!-- 第三行：门诊诊断 -->
										<view class="diagnosis-info" v-if="item.diagnosis">
											<text class="diagnosis-label">门诊诊断：</text>
											<text class="diagnosis-text">{{getDiagnosisText(item.diagnosis) || '-'}}</text>
										</view>
									</view>
								</view>
								
								<view class="mt-20 text-center">{{loadingStatus}}</view>
								
								<!-- 底部安全距离，避免被操作按钮遮挡 -->
								<view class="bottom-safe-area"></view>
								
								<!-- 增加触底检测区域 -->
								<view class="load-more-trigger"></view>
							</view>

							<!-- 空状态 -->
							<view v-else-if="!pageLoading" style="padding: 100rpx 0;">
								<gui-empty>
									<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
										<image class="gui-empty-img" src="https://img.starup.net.cn/xmkj/zwb/img/kong.png"></image>
									</view>
									<text slot="text" class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#9DABFF;">暂无待纳入患者</text>
								</gui-empty>
							</view>
						</scroll-view>
					</view>

					<!-- 底部按钮 - 使用固定定位放在页面底部 -->
					<view class="bottom-buttons">
						<view class="button-container">
							<button class="confirm-button" @tap="confirmSelection" :disabled="!selectedPatient">确定选择</button>
						</view>
					</view>
				</view>

				<!-- 手工建档内容 -->
				<view v-if="activeTab === 'manual'" class="tab-content manual-tab">
					<view class="content-container">
						<scroll-view
							class="manual-scroll-view"
							:scroll-y="true"
							:style="{height: mainHeight + 'px'}">
							<view class="manual-form gui-bg-white">
								<!-- <view class="form-title">手工建档</view> -->
								<view class="pb-40 px-30">
									<view class="search-warp gui-border-box gui-flex gui-space-between gui-align-items-center">
										<gui-search @inputting="inputFile" placeholder="就诊卡号/身份证号" :kwd="hisIsitCardNum"
											class="modl-border" @clear="clearinit(2)"></gui-search>
										<text @tap="hisPatient"
											class="modl-button gui-icons mygui-sbutton-loading gui-border-radius-large"
											slot="default">HIS获取</text>
									</view>
									<view class="mt-30 fs-32">
										<view class="gui-flex gui-border-b py-20">
											<view class="gui-flex w-60 gui-align-items-center">
												<view class="gui-flex1 w-30 mr-15" style="text-align-last:justify;"><text class="gui-color-red">*</text>姓名</view>
												<view class="w-60 gui-bg-gray py-20 px-15">
													<input type="text" style="font-size: 32rpx;" class="" maxlength="10"
														disabled v-model="hisData.name" name="userName"
														placeholder="HIS获取" />
												</view>
											</view>
											<view class="gui-flex w-40 gui-align-items-center pl-15">
												<view class="gui-flex1 w-30">性 别</view>
												<view class="gui-bg-gray py-20 px-15" style="width: 64%;">
													<input type="text" style="font-size: 32rpx;" maxlength="10" disabled
														v-model="hisData.sex==1?'男':hisData.sex==2?'女':'未知'"
														name="sex" placeholder="HIS获取" />
												</view>
											</view>
										</view>
										<!-- 身份证号 -->
										<view class="gui-flex gui-border-b py-20">
											<view class="gui-flex1 gui-flex gui-align-items-center">
												<view class="gui-flex1"><text class="gui-color-red">*</text>身份证号</view>
												<view class="gui-bg-gray py-20 px-15" style="width: 76%;">
													<input type="text" style="font-size: 32rpx;" maxlength="18" :disabled="true"
														v-model="hisData.idCard" name="idCard" placeholder="HIS获取" />
												</view>
											</view>
										</view>
										<view class="gui-flex gui-border-b py-20">
											<view class="gui-flex1 gui-flex w-60 gui-align-items-center">
												<view class="gui-flex1 w-40">出生日期</view>
												<view class="w-60 gui-bg-gray py-20 px-15">
													<input type="text" style="font-size: 32rpx;" maxlength="20" disabled
														v-model="hisData.birthday" name="birthday" placeholder="HIS获取" />
												</view>
											</view>
											<view class="gui-flex w-40 gui-align-items-center pl-15">
												<view class="gui-flex1">年 龄</view>
												<view class="gui-bg-gray py-20 px-15" style="width: 64%;">
													<input type="number" style="font-size: 32rpx;" maxlength="3" disabled
														v-model="hisData.age" name="age" placeholder="HIS获取" />
												</view>
											</view>
										</view>
										<view class="gui-flex gui-border-b py-20">
											<view class="gui-flex1 gui-flex gui-align-items-center">
												<view class="gui-flex1"><text class="gui-color-red">*</text>本人电话</view>
												<view class="py-20 px-15" style="width: 76%;">
													<input @blur=" val =>{ phoneInput(val,1)}" type="text" style="font-size: 32rpx;" maxlength="11"
														v-model="hisData.tel" name="tel" placeholder="手机号" />
												</view>
											</view>
										</view>
										<view class="gui-flex gui-border-b py-20">
											<view class="gui-flex1 gui-flex gui-align-items-center">
												<view class="gui-flex1">家属电话</view>
												<view class="py-20 px-15" style="width: 76%;">
													<input @blur=" val =>{ phoneInput(val,2)}" type="text" style="font-size: 32rpx;" maxlength="11"
														v-model="hisData.contactsTel" name="contactsTel" placeholder="手机号" />
												</view>
											</view>
										</view>
										<view class="gui-form-item gui-border-b" @tap="fileOpen">
											<text class="gui-form-label fs-32" style="text-align-last:justify;">现地址</text>
											<view class="gui-form-body" style="margin-left: 30rpx;">
												<input type="text" class="gui-form-input" :value="hisData.district" disabled
													placeholder="请选择" style="font-size: 32rpx;"/>
											</view>
											<text class="gui-icons gui-form-icon gui-color-gray gui-text-right px-20">&#xe603;</text>
										</view>
										<view class="gui-flex gui-border-b py-20">
											<view class="gui-flex1 gui-flex gui-align-items-center">
												<view class="gui-flex1">详细地址</view>
												<view class="py-20 px-15" style="width: 76%;">
													<textarea auto-height="true" maxlength="280" type="text"
														style="width: 100%; font-size: 32rpx;" v-model="hisData.address" name="address"
														placeholder="请输入详细地址" />
												</view>
											</view>
										</view>
										<!-- <view class="gui-flex gui-border-b py-20">
											<view class="gui-flex1 gui-flex gui-align-items-center">
												<view class="gui-flex1"><text class="gui-color-red">*</text>责任医生</view>
												<view v-if="doctorData.length>0" style="width: 76%;padding-left: 15rpx;">
													<view @tap="openDoctorPicker" class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
														<text class="gui-text fs-32">{{doctorIndex == -1?'请选择':doctorData[doctorIndex].nickName}}</text>
														<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
													</view>
												</view>
											</view>
										</view>
										<view class="gui-flex gui-border-b py-20">
											<view  class="gui-flex1 gui-flex gui-align-items-center">
												<view class="gui-flex1"><text class="gui-color-red">*</text>责任护士</view>
												<view v-if="nurseData.length>0" style="width: 76%;padding-left: 15rpx;">
													<view @tap="openNursePicker" class="gui-flex gui-rows gui-nowrap gui-space-between gui-align-items-center">
														<text class="gui-text fs-32">{{nurseIndex == -1?'请选择':nurseData[nurseIndex].nickName}}</text>
														<text class="gui-form-icon gui-icons gui-text-center gui-color-gray">&#xe603;</text>
													</view>
												</view>
											</view>
										</view> -->
									</view>
								</view>
							</view>
							<!-- 底部安全距离，避免被操作按钮遮挡 -->
							<view class="bottom-safe-area"></view>
						</scroll-view>
					</view>

					<!-- 底部按钮 -->
					<view class="bottom-buttons">
						<view class="button-container">
							<button class="confirm-button" @tap="saveManualRecord">确定选择</button>
						</view>
					</view>
				</view>
			</view>
		</gui-page>

		<!-- 人群标签底部弹出 -->
    <gui-popup ref="rqguipopup" position="bottom" :zIndex='999' @close="onPopupClose">
      <patientRenQunFz
          :genderArr="genderArr"
          @rqclose="rqclose"
          @renqSubmit="renqSubmit"
          :openType="1"
          :doctorList="doctorData"
          :nurseList="nurseData"
          :initialDoctorId="selectedDoctorId"
          :initialNurseId="selectedNurseId"
          :patientInfo="archivalPatientData"
          :patientId="patientIdFZ"
      ></patientRenQunFz>
    </gui-popup>

		<!-- 删除不再需要的modal -->
		
		<!-- 地址选择器 -->
		<gui-shexiang-area-picker ref="graceAddressPicker" @confirm="fileConfirm" :value="default2" v-if="default2 != null"></gui-shexiang-area-picker>
		
		<!-- 责任医生选择器 -->
		<gui-popup ref="doctorPicker" position="bottom" :zIndex="999999999">
			<view class="gap-body gui-bg-white" @tap.stop.prevent="stopfun">
				<view class="gap-header gui-flex gui-rows gui-space-between">
					<text class="gap-header-btn gui-block-text gui-color-gray" @tap="closeDoctorPicker">取消</text>
					<text class="gap-header-btn gui-block-text gui-primary-color" style="text-align:right;" @tap="confirmDoctorPicker">确定</text>
				</view>
				<picker-view :indicator-style="'height:35px'" class="gap-main" :value="[doctorPickerIndex]" @change="changeDoctorPicker">
					<picker-view-column>
						<text class="gap-item gui-block-text" v-for="(item, index) in doctorData" :key="index">{{item.nickName}}</text>
					</picker-view-column>
				</picker-view>
			</view>
		</gui-popup>

		<!-- 责任护士选择器 -->
		<gui-popup ref="nursePicker" position="bottom" :zIndex="999999999">
			<view class="gap-body gui-bg-white" @tap.stop.prevent="stopfun">
				<view class="gap-header gui-flex gui-rows gui-space-between">
					<text class="gap-header-btn gui-block-text gui-color-gray" @tap="closeNursePicker">取消</text>
					<text class="gap-header-btn gui-block-text gui-primary-color" style="text-align:right;" @tap="confirmNursePicker">确定</text>
				</view>
				<picker-view :indicator-style="'height:35px'" class="gap-main" :value="[nursePickerIndex]" @change="changeNursePicker">
					<picker-view-column>
						<text class="gap-item gui-block-text" v-for="(item, index) in nurseData" :key="index">{{item.nickName}}</text>
					</picker-view-column>
				</picker-view>
			</view>
		</gui-popup>
	</view>
</template>

<script>
import patientRenQunFz from './component/patientRenQunFz.vue'
import {
	addArchival,
	getAppointPatientlist,
	getDutyUserList,
	getConfigKey
} from '@/api/patient.js'

export default {
	components: {
		patientRenQunFz
	},
	data() {
		return {
			pageLoading: true,
			loding: false, // 防重复加载标志
			list: [], // 患者列表
			selectedPatient: null, // 选中的患者ID（单选）
			loadingStatus: '',
			hasNextPage: false,
			mainHeight: 600, // scroll-view 高度
			scrollTimer: null, // 滚动防抖定时器
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				patientName: '' // 搜索关键词（姓名/就诊卡号/住院号/身份证号）
			},
			userInfo: {},
			// 手工建档相关数据
			doctorData: [],
			doctorIndex: -1,
			doctorPickerIndex: 0, // 医生选择器当前索引
			nurseData: [],
			nurseIndex: -1,
			nursePickerIndex: 0, // 护士选择器当前索引
			hisData: {
				district: '',
			},
			hisIsitCardNum: '',
			default2: null,
			padStart: false,
			signSmsSwitch: '',
			idCardRules: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))((0[1-9])|([1-2][0-9])|30|31)\d{3}[\dX]$/i,
			// 人群分组相关数据
			patientIdFZ: '',
			genderArr: [],
			crowdTypeData: null, // 存储建档成功后的患者数据，用于人群分组设置
			activeTab: 'manual', // 当前激活的选项卡
			selectedDoctorId: '', // 用于传递给 patientRenQunFz 的初始医生ID
			selectedNurseId: '', // 用于传递给 patientRenQunFz 的初始护士ID
			archivalPatientData: null, // 存储建档接口返回的患者数据，用于回显
			debugMode: true, // 调试模式
			currentArchivalType: '', // 当前建档类型：'manual'(手动建档) 或 'recommended'(推荐建档)
		}
	},
	onLoad: function() {
		this.init();
	},
	onShow() {
		// 加载配置信息
		this.$set(this, 'userInfo', uni.getStorageSync('mbuserInfo'));
		getConfigKey("sign_sms_switch").then(res => {
			this.signSmsSwitch = res.msg
		})
		getConfigKey("padStart").then(res => {
			this.padStart = res.msg === 'true'
		})
		
		// 每次页面显示时重新加载患者列表
		this.reloadPatientList();
		
		// 确保每次页面显示时都重新计算高度
		setTimeout(() => {
			this.recalculateHeight();
		}, 100);
	},
	onReady() {
		// 初始计算高度
		this.recalculateHeight();
	},
	methods: {
		init() {
			// 获取用户信息
			this.$set(this, 'userInfo', uni.getStorageSync('mbuserInfo'));
			
			// 重置分页参数
			this.queryParams.pageNum = 1;
			this.loadingStatus = '';
			this.hasNextPage = false;
			this.list = [];
			this.loding = false;
			this.pageLoading = true;
			this.selectedPatient = null;
			
			// 初始加载时先重置页面状态
			this.reloadPatientList();
			
			// 获取医生护士数据 - 在页面初始化时就加载医生和护士数据
			this.loadDoctorAndNurseData();
			
			// 初始化地址数据
			setTimeout(() => {
				this.default2 = ['广西', '南宁市', '兴宁区'];
				// 设置默认地址显示值
				this.hisData.province = '广西';
				this.hisData.city = '南宁市';
				this.hisData.county = '兴宁区';
				this.hisData.district = '广西,南宁市,兴宁区';
			}, 1000);
		},
		
		// 重新加载患者列表数据
		reloadPatientList() {
			this.pageLoading = true;
			this.list = [];
			this.queryParams.pageNum = 1;
			this.getList(true);
		},
		
		// 加载医生和护士数据
		loadDoctorAndNurseData() {
			getDutyUserList({
				deptId: this.userInfo.deptId,
				appId: this.userInfo.deptAppIds
			}).then(res => {
				console.log('医护人员数据:', res);
				if (res.data) {
					this.doctorData = res.data.doctor || [];
					this.nurseData = res.data.nurse || [];
					
					// 根据当前登录用户类型自动设置默认医生或护士
					if (res.data?.type) {
						if (res.data.type == 2) {
							// 当前用户是护士
							this.nurseIndex = this.nurseData.findIndex(item => item.nickName == this.userInfo.nickName);
						} else {
							// 当前用户是医生
							this.doctorIndex = this.doctorData.findIndex(item => item.nickName == this.userInfo.nickName);
						}
					}
				}
			}).catch(err => {
				console.error('获取医护人员信息失败', err);
			});
		},
		// 获取未建档患者列表
		getList(isReload) {
			// 设置加载状态
			if (isReload) {
				this.pageLoading = true;
			}
			
			// 防重复加载
			if (this.loding) {
				return;
			}
			this.loding = true;

			if (isReload) {
				this.list = [];
				this.queryParams.pageNum = 1;
			}

			// 构建查询参数
			let params = {
				pageNum: this.queryParams.pageNum,
				pageSize: this.queryParams.pageSize,
				deptId: this.userInfo.deptId
			};

			// 添加搜索条件 - 统一使用patientName参数
			if (this.queryParams.patientName) {
				// 后端SQL会在patient_name,id_card,visit_card_num字段中搜索，所以统一传递patientName
				params.patientName = this.queryParams.patientName.trim();
			}

			if (this.debugMode) {
				console.log('获取患者列表参数:', params);
			}
			
			// 确认API路径
			const apiUrl = this.$common.getUnrecordedPatient || '/doctor/patient/getUnrecordedPatient';

			this.$common.RequestData({
				url: apiUrl,
				data: params,
				method: 'get'
			}, res => {
				// 请求成功，关闭加载状态
				this.pageLoading = false;
				this.loding = false; // 重置加载标志
				
				if (this.debugMode) {
					console.log('获取患者列表结果:', res);
				}
				
				if (res.code == 200) {
					if (isReload) {
						this.list = res.rows || [];
						// 结束下拉刷新状态
						if (this.$refs.guipage) {
							this.$refs.guipage.endReload();
						}
					} else {
						// 追加数据到列表
						this.list = this.list.concat(res.rows || []);
					}

					// 计算是否有下一页
					const totalPages = Math.ceil(res.total / this.queryParams.pageSize);
					this.hasNextPage = this.queryParams.pageNum < totalPages;

					if (this.debugMode) {
						console.log('分页信息', {
							total: res.total,
							pageSize: this.queryParams.pageSize,
							currentPage: this.queryParams.pageNum,
							totalPages: totalPages,
							hasNextPage: this.hasNextPage,
							currentListLength: this.list.length
						});
					}

					// 更新加载状态文本
					this.loadingStatus = this.hasNextPage ? "上拉加载更多" : "已加载全部";
				} else {
					this.$common.msg(res.msg || '获取数据失败');
					this.loadingStatus = "加载失败";
					if (isReload && this.$refs.guipage) {
						// 即使失败也要结束下拉刷新状态
						this.$refs.guipage.endReload();
					}
				}
			}, true, (error) => {
				// 请求失败，关闭加载状态
				this.pageLoading = false;
				this.loding = false; // 重置加载标志
				this.loadingStatus = "加载失败";
				
				if (this.debugMode) {
					console.error('请求失败:', error);
				}
				
				if (isReload && this.$refs.guipage) {
					// 请求失败时也要结束下拉刷新状态
					this.$refs.guipage.endReload();
				}
			});
		},
		// 搜索输入
		inputting(e) {
			this.queryParams.patientName = e;
		},
		// 搜索确认
		inputConfirm() {
			this.list = [];
			this.queryParams.pageNum = 1;
			this.getList();
		},
		// 清除搜索
		clearSearch() {
			this.queryParams.patientName = '';
			this.inputConfirm();
		},
		// 下拉刷新
		reload() {
			this.selectedPatient = null;
			this.getList(true);
		},
		// 上拉加载更多
		scrolltolowerFun() {
			if (this.debugMode) {
				console.log('触底事件触发', {
					activeTab: this.activeTab,
					hasNextPage: this.hasNextPage,
					currentPage: this.queryParams.pageNum,
					listLength: this.list.length,
					mainHeight: this.mainHeight,
					scrollViewHeight: this.mainHeight,
					listActualLength: this.$el && this.$el.querySelector('.gui-flex.gui-columns') ? 
						this.$el.querySelector('.gui-flex.gui-columns').getBoundingClientRect().height : 'unknown'
				});
			}

			// 防止重复触发
			if (this.loding) {
				if (this.debugMode) console.log('正在加载中，忽略触底事件');
				return;
			}
			
			// 添加防抖，避免短时间内多次触发
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer);
			}
			
			this.scrollTimer = setTimeout(() => {
				if (this.hasNextPage) {
					this.loadingStatus = "加载中...";
					this.queryParams.pageNum = this.queryParams.pageNum + 1;
					
					if (this.debugMode) {
						console.log('开始加载第', this.queryParams.pageNum, '页');
					}
					
					this.getList();
				} else {
					this.$common.msg("已加载全部", 'success', 1000);
					this.loadingStatus = "已加载全部";
				}
			}, 200); // 200ms防抖
		},
		// 监听滚动事件
		onScroll(e) {
			if (this.debugMode) {
				// 降低日志频率，只在滚动位置是100的倍数时打印
				const scrollTop = e.detail.scrollTop;
				if (scrollTop % 100 < 10) {
					console.log('滚动位置:', {
						scrollTop: scrollTop,
						scrollHeight: e.detail.scrollHeight,
						deltaY: e.detail.deltaY
					});
				}
			}
		},
		// 切换选择状态（单选）
		toggleSelection(patientId) {
			console.log('toggleSelection called with patientId:', patientId);
			if (this.selectedPatient === patientId) {
				// 如果点击的是已选中的患者，则取消选择
				this.selectedPatient = null;
				console.log('Patient deselected');
			} else {
				// 否则选择该患者
				this.selectedPatient = patientId;
				console.log('Patient selected:', patientId);
			}
		},
		// 确定选择（单个建档）
		confirmSelection() {
			if (!this.selectedPatient) {
				this.$common.msg('请选择要建档的患者');
				return;
			}

			// 直接进行建档，移除确认提示框
			this.singleArchival();
		},
		// 单个建档
		singleArchival() {
			// 设置当前建档类型为推荐建档
			this.currentArchivalType = 'recommended';

			uni.showLoading({
				title: '建档中...'
			});

			this.$common.RequestData({
				url: this.$common.addArchivalByRecordId + '/' + this.selectedPatient,
				method: 'get'
			}, res => {
				uni.hideLoading();
				if (res.code == 200) {
					this.$common.msg('建档成功');

					// 保存建档返回的患者数据，用于回显
					this.archivalPatientData = res.data;
					console.log('Archival patient data saved:', this.archivalPatientData);

					// 对于推荐建档的情况，确保医护人员已加载
					if (!this.doctorData || this.doctorData.length === 0) {
						// 如果医护人员数据未加载，先加载再传递
						this.loadDoctorAndNurseData();
						setTimeout(() => {
							// 设置医生护士ID（从接口返回数据中获取）
							this.setDoctorAndNurseIdsFromArchival();
							// 建档成功后直接进入人群分组设置，使用建档接口返回的patientId
							this.showCrowdTypeSetting(res.data.patientId);
						}, 500);
					} else {
						// 设置医生护士ID（从接口返回数据中获取）
						this.setDoctorAndNurseIdsFromArchival();
						// 建档成功后直接进入人群分组设置，使用建档接口返回的patientId
						this.showCrowdTypeSetting(res.data.patientId);
					}
				} else {
					this.$common.msg(res.msg || '建档失败');
				}
			}, true, () => {
				uni.hideLoading();
				this.$common.msg('网络请求失败');
			});
		},
		
		// 设置医生和护士ID
		setDoctorAndNurseIds() {
			// 尝试查找当前用户是否是医生或护士
			const currentUserIsDoctorIndex = this.doctorData.findIndex(item => item.userName === this.userInfo.userName);
			if (currentUserIsDoctorIndex !== -1) {
				this.selectedDoctorId = String(this.doctorData[currentUserIsDoctorIndex].userId);
					} else {
				// 如果不是医生，设置第一个医生
				if (this.doctorData.length > 0) {
					this.selectedDoctorId = String(this.doctorData[0].userId);
				}
			}

			const currentUserIsNurseIndex = this.nurseData.findIndex(item => item.userName === this.userInfo.userName);
			if (currentUserIsNurseIndex !== -1) {
				this.selectedNurseId = String(this.nurseData[currentUserIsNurseIndex].userId);
			} else {
				// 如果不是护士，设置第一个护士
				if (this.nurseData.length > 0) {
					this.selectedNurseId = String(this.nurseData[0].userId);
				}
			}
		},

		// 从建档接口返回的数据中设置医生护士ID
		setDoctorAndNurseIdsFromArchival() {
			console.log('setDoctorAndNurseIdsFromArchival called');
			console.log('archivalPatientData:', this.archivalPatientData);

			if (this.archivalPatientData) {
				// 优先使用接口返回的医生ID和护士ID
				if (this.archivalPatientData.doctorId) {
					this.selectedDoctorId = String(this.archivalPatientData.doctorId);
					console.log('Set selectedDoctorId to:', this.selectedDoctorId);
				}
				if (this.archivalPatientData.nurseId) {
					this.selectedNurseId = String(this.archivalPatientData.nurseId);
					console.log('Set selectedNurseId to:', this.selectedNurseId);
				}
			}

			// 如果接口没有返回医生护士ID，则使用默认逻辑
			if (!this.selectedDoctorId || !this.selectedNurseId) {
				console.log('Using default logic for missing IDs');
				this.setDoctorAndNurseIds();
			}

			console.log('Final selectedDoctorId:', this.selectedDoctorId);
			console.log('Final selectedNurseId:', this.selectedNurseId);
		},
		// 显示人群分组设置
		showCrowdTypeSetting(patientId) {
			console.log('showCrowdTypeSetting called with patientId:', patientId);
			this.$set(this, 'patientIdFZ', patientId);
			console.log('patientIdFZ set to:', this.patientIdFZ);

			// 确保医生和护士数据已加载
			if (!this.doctorData || this.doctorData.length === 0 || !this.nurseData || this.nurseData.length === 0) {
				uni.showLoading({ title: '加载医护人员信息...' });
				this.loadDoctorAndNurseData();
				
				// 加载后一段时间再加载人群类型数据，确保医护数据已经加载完成
				setTimeout(() => {
					uni.hideLoading();
					this.loadCrowdTypeAndOpenPopup();
				}, 500);
			} else {
				this.loadCrowdTypeAndOpenPopup();
			}
		},
		
		// 加载人群类型数据并打开弹窗
		loadCrowdTypeAndOpenPopup() {
			this.$common.RequestData({
				url: this.$common.getCrowdType + 'people_type',
				data: {},
				method: 'get',
			}, res => {
				if (res.code == 200) {
					this.$set(this, 'genderArr', res.data);
					this.$refs.rqguipopup.open();
				} else {
					this.$common.msg(res.msg);
				}
			});
		},
		// 人群分组关闭
		rqclose() {
			this.$refs.rqguipopup.close();

			// 根据建档类型决定跳转行为
			if (this.currentArchivalType === 'manual') {
				// 手动建档：跳转到患者列表页面
				this.selectedPatient = null;
				// 清空之前的筛选条件，避免新建档患者被筛选掉
				uni.removeStorageSync('patientParam');
				this.$common.navTab("/pages/patient/patientIndex");
			} else if (this.currentArchivalType === 'recommended') {
				// 推荐建档：停留在当前页面，刷新推荐建档列表
				this.selectedPatient = null;
				this.reload(); // 刷新推荐建档列表
			}

			// 重置建档类型
			this.currentArchivalType = '';
		},
		// 人群分组提交成功
		renqSubmit() {
			// 延迟关闭弹窗和跳转，确保用户能看到组件内部的loading过程和成功提示
			setTimeout(() => {
				this.$refs.rqguipopup.close();

				// 根据建档类型决定跳转行为
				setTimeout(() => {
					this.selectedPatient = null;

					if (this.currentArchivalType === 'manual') {
						// 手动建档：跳转到患者列表页面
						// 清空之前的筛选条件，避免新建档患者被筛选掉
						uni.removeStorageSync('patientParam');
						this.$common.navTab("/pages/patient/patientIndex");
					} else if (this.currentArchivalType === 'recommended') {
						// 推荐建档：停留在当前页面，刷新推荐建档列表
						this.reload(); // 刷新推荐建档列表
					}

					// 重置建档类型
					this.currentArchivalType = '';
				}, 1500); // 延迟1.5秒跳转，让用户看到组件内部的成功提示
			}, 800); // 延迟800ms关闭弹窗，确保用户看到组件内部的loading和成功提示
		},
		// 弹窗关闭事件处理（点击弹窗其他区域关闭时触发）
		onPopupClose() {
			console.log('Popup closed by clicking outside');
			this.selectedPatient = null;

			// 根据建档类型决定跳转行为
			if (this.currentArchivalType === 'manual') {
				// 手动建档：跳转到患者列表页面
				// 清空之前的筛选条件，避免新建档患者被筛选掉
				uni.removeStorageSync('patientParam');
				this.$common.navTab("/pages/patient/patientIndex");
			} else if (this.currentArchivalType === 'recommended') {
				// 推荐建档：停留在当前页面，刷新推荐建档列表
				this.reload(); // 刷新推荐建档列表
			}

			// 重置建档类型
			this.currentArchivalType = '';
		},
		// 获取性别文本
		sexText(sex) {
			switch(sex) {
				case '1': return '男';
				case '2': return '女';
				default: return '未知';
			}
		},
		// 计算年龄
		getAge(birthday) {
			if (!birthday) return '未知';
			const birth = new Date(birthday);
			const now = new Date();
			let age = now.getFullYear() - birth.getFullYear();
			const monthDiff = now.getMonth() - birth.getMonth();
			if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < birth.getDate())) {
				age--;
			}
			return age;
		},
		// 获取诊断文本
		getDiagnosisText(diagnosis) {
			if (!diagnosis) return '';
			try {
				const diagArray = JSON.parse(diagnosis);
				if (Array.isArray(diagArray) && diagArray.length > 0) {
					// 只显示ZDLX为"门急诊断"的诊断信息
					const emergencyDiagnosis = diagArray.filter(item => item.ZDLX === '门急诊断');

					if (emergencyDiagnosis.length > 0) {
						return emergencyDiagnosis.map(item => item.DIAGNOSE).filter(Boolean).join('、');
					}

					// 如果没有门急诊断，返回空字符串
					return '';
				}
			} catch (e) {
				// 如果解析失败，尝试作为普通字符串处理
				console.log('诊断解析失败:', e);
				return diagnosis;
			}
			return diagnosis;
		},
		// 手工建档相关方法
		closeFile() {
			this.switchTab('recommended');
		},
		phoneInput(e, type, site) {
			let phone = site ? e : e.detail.value;
			const regex = /^1[3-9]\d{9}$/;
			if (!regex.test(phone)) {
				if (type == 1) {
					this.hisData.tel = '';
				} else {
					this.hisData.contactsTel = '';
				}
				this.$common.msg('请输入正确的手机号！');
				return false;
			} else {
				return true;
			}
		},
		confirmFile() {
			if (this.activeTab === 'recommended') {
				// 推荐建档逻辑
				if (!this.selectedPatient) {
					return this.$common.msg('请先选择一个患者');
				}
				this.singleArchival();
				return;
			}
			
			// 手工建档逻辑
			// 设置当前建档类型为手动建档
			this.currentArchivalType = 'manual';

			if (!this.hisData.idCard || !this.hisData.name) {
				return this.$common.msg('请点击HIS获取到用户信息再保存！');
			}
			if (this.doctorIndex == -1) {
				return this.$common.msg('请选择责任医生！');
			}
			if (this.nurseIndex == -1) {
				return this.$common.msg('请选择责任护士！');
			}

			uni.showLoading({
				title: '保存中'
			});

			if (!this.phoneInput(this.hisData.tel, 1, 3)) {
				return;
			}
			if (this.hisData.contactsTel && this.hisData.contactsTel.length > 1 && !this.phoneInput(this.hisData.contactsTel, 2, 3)) {
				return;
			}

			addArchival({
				...this.hisData,
				deptId: this.userInfo.deptId,
				appId: this.userInfo.deptAppIds,
				dutyDoctorId: this.doctorData[this.doctorIndex].userId,
				dutyNurseId: this.nurseData[this.nurseIndex].userId,
				birthday: this.hisData.birthday + ' 00:00:00'
			}).then(res => {
				uni.hideLoading();
				if (res.code == 200) {
					if (this.signSmsSwitch === '1') {
						this.$common.msg('建档成功，邀请短信已发送');
					} else {
						this.$common.msg('保存成功');
					}
					this.$refs.modalFile.close();
					this.hisData = {};
					this.hisIsitCardNum = '';
					// 手工建档成功后进入人群分组设置
					this.showCrowdTypeSetting(res.data.patientId);
				} else {
					this.$common.msg('保存失败，请重新获取。');
					this.hisData = {};
				}
			});
		},
		fileOpen() {
			this.$refs.graceAddressPicker.open();
		},
		inputFile(e) {
			this.hisIsitCardNum = e;
		},
		clearinit(type) {
			if (type === 2) {
				this.hisIsitCardNum = '';
			}
		},
		hisPatient() {
			var open = true;
			if (!this.hisIsitCardNum) {
				return this.$common.msg('请输入就诊卡号或身份证号再查询。');
			}

			let searchCardNum = this.hisIsitCardNum;
			if (this.padStart && searchCardNum && searchCardNum.length < 12 && searchCardNum.length !== 8) {
				searchCardNum = searchCardNum.padStart(12, '0');
				this.hisIsitCardNum = searchCardNum;
			}

			uni.showLoading({
				title: '加载中'
			});
			this.nurseIndex = -1;
			this.doctorIndex = -1;
			if (open) {
				open = false;
				getAppointPatientlist({
					visitCardNum: searchCardNum,
					deptId: this.userInfo.deptId,
					appId: this.userInfo.deptAppIds
				}).then(res => {
					if (res.data && res.data.patientId) {
						this.hisData = res.data;
						this.hisData.sex = res.data.sex ? res.data.sex : '';
						if (res.data.idCard && this.idCardRules.test(res.data.idCard)) {
							this.hisData.birthday = this.toIdCard(res.data.idCard, 1);
							this.hisData.age = this.toIdCard(res.data.idCard, 3);
							this.hisData.sex = this.toIdCard(res.data.idCard, 2);
						}
						var strings = [];
						strings[0] = res.data.province ? res.data.province : '';
						strings[1] = res.data.city ? res.data.city : '';
						strings[2] = res.data.county ? res.data.county : '';
						this.hisData.district = strings.filter(str => str !== '' && str != 'null').join(',') || '';
						if (res.data.nurseId) {
							this.nurseIndex = this.nurseData.findIndex(item => item.userId == res.data.nurseId);
						}
						if (res.data.doctorId) {
							this.doctorIndex = this.doctorData.findIndex(item => item.userId == res.data.doctorId);
						}
						uni.hideLoading();
						this.$forceUpdate();
						setTimeout(function() {
							open = true;
						}, 1000);
					} else {
						uni.hideLoading();
						this.$common.msg('未查询到该患者，请检查。');
						setTimeout(function() {
							open = true;
						}, 1000);
					}
				});
			}
		},
		toIdCard(userCard, num) {
			if (!this.idCardRules.test(userCard)) {
				this.$common.msg('请输入正确的身份证');
				return;
			}
			if (num == 1) {
				var birth = '';
				birth = userCard.substr(6, 8).replace(/(.{4})(.{2})/, "$1-$2-");
				return birth;
			}
			if (num == 2) {
				if (userCard.length < 17) {
					return "3";
				}
				if (parseInt(userCard.substr(16, 1)) % 2 == 1) {
					return "1";
				} else {
					return "2";
				}
			}
			if (num == 3) {
				var myDate = new Date();
				var month = myDate.getMonth() + 1;
				var day = myDate.getDate();
				var age = myDate.getFullYear() - userCard.substring(6, 10) - 1;
				if (userCard.substring(10, 12) < month || userCard.substring(10, 12) == month && userCard.substring(12, 14) <= day) {
					age++;
				}
				return age;
			}
		},
		fileConfirm(e) {
			this.hisData.province = e.names[0];
			this.hisData.city = e.names[1];
			this.hisData.county = e.names[2];
			this.hisData.district = e.names[0] + ',' + e.names[1] + ',' + e.names[2];
			this.$forceUpdate();
		},
		// 医生选择器相关方法
		openDoctorPicker() {
			this.doctorPickerIndex = this.doctorIndex >= 0 ? this.doctorIndex : 0;
			this.$refs.doctorPicker.open();
		},
		closeDoctorPicker() {
			this.$refs.doctorPicker.close();
		},
		changeDoctorPicker(e) {
			this.doctorPickerIndex = e.detail.value[0];
		},
		confirmDoctorPicker() {
			this.doctorIndex = this.doctorPickerIndex;
			this.hisData.doctor = this.doctorData[this.doctorIndex];
			this.$refs.doctorPicker.close();
		},
		// 护士选择器相关方法
		openNursePicker() {
			this.nursePickerIndex = this.nurseIndex >= 0 ? this.nurseIndex : 0;
			this.$refs.nursePicker.open();
		},
		closeNursePicker() {
			this.$refs.nursePicker.close();
		},
		changeNursePicker(e) {
			this.nursePickerIndex = e.detail.value[0];
		},
		confirmNursePicker() {
			this.nurseIndex = this.nursePickerIndex;
			this.hisData.nurse = this.nurseData[this.nurseIndex];
			this.$refs.nursePicker.close();
		},
		// 阻止事件冒泡
		stopfun(e) {
			e.stopPropagation();
			return null;
		},
		// 切换选项卡
		switchTab(tab) {
			this.activeTab = tab;
			
			// 当切换到手工建档时，确保医护人员数据已加载
			if (tab === 'manual' && (!this.doctorData || this.doctorData.length === 0 || !this.nurseData || this.nurseData.length === 0)) {
				this.loadDoctorAndNurseData();
			}
			
			// 切换选项卡时清空已选择的患者
			this.selectedPatient = null;
			
			// 切换选项卡时重新计算高度
			this.recalculateHeight();
		},
		// 获取已选择患者的姓名
		getSelectedPatientName() {
			if (this.selectedPatient) {
				const patient = this.list.find(item => item.id === this.selectedPatient);
				if (patient) {
					return patient.patientName;
				}
			}
			return '无';
		},
		// 获取已选择患者的详情
		getSelectedPatientDetails() {
			if (this.selectedPatient) {
				const patient = this.list.find(item => item.id === this.selectedPatient);
				if (patient) {
					return `${this.sexText(patient.sex)} ${this.getAge(patient.birthday)}岁 ${patient.phone && patient.phone.replace(/(\d{3})\d*(\d{4})/, "$1****$2")}`;
				}
			}
			return '请在列表中选择一个患者';
		},
		// 保存手工建档信息
		saveManualRecord() {
			// 手工建档逻辑
			// 设置当前建档类型为手动建档
			this.currentArchivalType = 'manual';

			if (!this.hisData.idCard || !this.hisData.name) {
				return this.$common.msg('请点击HIS获取到用户信息再保存！');
			}
			if (this.doctorIndex == -1) {
				return this.$common.msg('请选择责任医生！');
			}
			if (this.nurseIndex == -1) {
				return this.$common.msg('请选择责任护士！');
			}

			uni.showLoading({
				title: '保存中'
			});

			if (!this.phoneInput(this.hisData.tel, 1, 3)) {
				return;
			}
			if (this.hisData.contactsTel && this.hisData.contactsTel.length > 1 && !this.phoneInput(this.hisData.contactsTel, 2, 3)) {
				return;
			}

			addArchival({
				...this.hisData,
				deptId: this.userInfo.deptId,
				appId: this.userInfo.deptAppIds,
				dutyDoctorId: this.doctorData[this.doctorIndex].userId,
				dutyNurseId: this.nurseData[this.nurseIndex].userId,
				birthday: this.hisData.birthday + ' 00:00:00'
			}).then(res => {
				uni.hideLoading();
				if (res.code == 200) {
					if (this.signSmsSwitch === '1') {
						this.$common.msg('建档成功，邀请短信已发送');
					} else {
						this.$common.msg('保存成功');
					}
					this.hisData = {};
					this.hisIsitCardNum = '';
					
					// 保存选中的医生和护士ID，传递给人群分组组件
					this.selectedDoctorId = String(this.doctorData[this.doctorIndex].userId);
					this.selectedNurseId = String(this.nurseData[this.nurseIndex].userId);
					
					// 手工建档成功后进入人群分组设置
					this.showCrowdTypeSetting(res.data.patientId);
				} else {
					this.$common.msg('保存失败，请重新获取。');
					this.hisData = {};
				}
			});
		},
		
		// 重新计算滚动区域高度
		recalculateHeight() {
			uni.getSystemInfo({
				success: (res) => {
					// 获取页面内各部分元素高度
					let tabHeight = uni.upx2px(50);  // 选项卡高度 - 统一为patientIndex的高度
					let searchHeight = this.activeTab === 'recommended' ? uni.upx2px(120) : 0; // 搜索框高度
					let bottomHeight = uni.upx2px(140); // 底部按钮区域高度
					let safeAreaBottom = res.safeAreaInsets ? res.safeAreaInsets.bottom : 0;
					
					// 计算实际可用高度 = 窗口高度 - 顶部选项卡 - 搜索区域 - 底部按钮 - 安全区域
					let availableHeight = res.windowHeight - tabHeight - searchHeight - bottomHeight;
					
					// 最终高度 = 可用高度 - 额外边距
					this.mainHeight = availableHeight - 20;
					
					if (this.debugMode) {
						console.log('计算滚动区域高度', {
							windowHeight: res.windowHeight,
							tabHeight: tabHeight,
							searchHeight: searchHeight,
							bottomHeight: bottomHeight,
							safeAreaBottom: safeAreaBottom,
							availableHeight: availableHeight,
							finalHeight: this.mainHeight
						});
					}
				}
			});
		}
	}
}
</script>

<style scoped>
.search-container {
	position: sticky;
	top: 0;
	z-index: 100;
	background: #f8f9fa;
}

.content-container {
	flex: 1;
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.patient-scroll-view,
.manual-scroll-view {
	flex: 1;
	width: 100%;
	height: calc(100vh - 280rpx); /* 默认高度，会被JS动态调整 */
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
}

.ellipsis-2 {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.bottom-buttons {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	z-index: 999;
	box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
	padding-bottom: env(safe-area-inset-bottom);
}

.button-container {
	display: flex;
	padding: 20rpx 40rpx;
	gap: 20rpx;
}

.manual-button {
	flex: 1;
	height: 88rpx;
	line-height: 88rpx;
	background: white;
	border: 2rpx solid #7784EB;
	border-radius: 44rpx;
	color: #7784EB;
	font-size: 32rpx;
	text-align: center;
}

.manual-button::after {
	border: none;
}

.confirm-button {
	flex: 1;
	height: 88rpx;
	line-height: 88rpx;
	background: #7784EB;
	border: none;
	border-radius: 44rpx;
	color: white;
	font-size: 32rpx;
	text-align: center;
}

.confirm-button::after {
	border: none;
}

.confirm-button[disabled] {
	background: #cccccc;
	color: #999999;
}

/* 患者列表项样式 */
.patient-item {
	display: flex;
	align-items: flex-start;
	padding: 30rpx 20rpx;
	margin-bottom: 20rpx;
	background: white;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
	cursor: pointer;
	transition: all 0.2s ease;
}

/* .patient-item:active {
	transform: scale(0.98);
	background-color: #f8f9fa;
} */

.checkbox-container {
	margin-right: 20rpx;
	margin-top: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.patient-info {
	flex: 1;
}

.patient-basic-info {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.patient-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-right: 20rpx;
}

.patient-details {
	font-size: 28rpx;
	color: #666;
}

.card-info {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.card-label {
	font-size: 28rpx;
	color: #999;
	margin-right: 10rpx;
}

.card-number {
	font-size: 28rpx;
	color: #007AFF;
}

.diagnosis-info {
	display: flex;
	align-items: flex-start;
}

.diagnosis-label {
	font-size: 28rpx;
	color: #999;
	margin-right: 10rpx;
	white-space: nowrap;
}

.diagnosis-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;
	line-height: 1.4;
}

.modl-button {
	text-align: center;
	color: #fff;
	width: 160rpx;
	height: 70rpx;
	line-height: 70rpx;
	font-size: 28rpx;
}

.modl-border {
	width: 420rpx;
	border: 1rpx solid #a6a6a7;
}

.modal-btns {
	line-height: 88rpx;
	font-size: 26rpx;
	text-align: center;
	width: 200rpx;
}
/* 或者使用更具体的选择器 */
::v-deep .uni-scroll-view-refresher {
	padding: 0 !important;
}

/* 底部安全距离 */
.bottom-safe-area {
	/* height: calc(180rpx + env(safe-area-inset-bottom)); */
	width: 100%;
}

/* 选项卡样式 - 统一样式 */
.demo-nav-my {
	flex: 1;
	font-size: 30rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}

.demo-nav-my-name-i {
	color: #7784eb;
	height: 50px;
	font-weight: bold;
	display: flex;
	justify-content: center;
	align-items: center;
}

.demo-nav-my-name {
	height: 50px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.nav-active-line {
	width: 120rpx;
	height: 6rpx;
	background: linear-gradient(to right, #8190ff, #7784eb);
}

.nav-active-line-i {
	width: 80rpx;
	height: 6rpx;
	background: #fff
}

.demo-nav {
	display: flex;
	flex-direction: row;
	padding: 15rpx 30rpx;
}

.tab-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	position: relative;
}

.manual-form {
	background-color: white;
	border-radius: 10rpx;
	margin: 20rpx;
	padding: 20rpx 0;
}

.form-title {
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
	margin-bottom: 20rpx;
}

.manual-scroll-view {
	flex: 1;
	width: 100%;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
	position: relative;
}

/* 手工建档表单样式 */
.px-30 {
	padding-left: 30rpx;
	padding-right: 30rpx;
}

/* 选择器样式 */
.gap-body{height:500rpx;}
.gap-header{padding:25rpx;}
.gap-header-btn{width:200rpx; line-height:38rpx; height:38rpx; font-size:28rpx;}
.gap-main{width:750rpx; height:610rpx;}
.gap-item{height:35px; font-size:16px; line-height:35px; overflow:hidden; text-align:center;}



.recommended-list {
	padding: 20rpx 0;
}

.recommended-patient {
	background: #f0f0f0;
	padding: 20rpx;
	border-radius: 10rpx;
	margin-bottom: 10rpx;
}

.recommended-info {
	display: flex;
	align-items: center;
}

.recommended-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-right: 10rpx;
}

.recommended-details {
	font-size: 28rpx;
	color: #666;
}

.no-selection {
	text-align: center;
	padding: 40rpx 0;
	color: #999;
}

.load-more-trigger {
	height: 30px;
	width: 100%;
}
</style>
