<template>
  <gui-page :fullPage="true" :isLoading="pageLoading || isUploading" ref="guiPage">
    <view slot="gBody">

      <view class="upload-container">
        <!-- 分类列表 -->
        <view class="category-list">
          <!-- 病历 -->
          <view class="category-item" v-if="!editMode || editType === '1'">
            <view class="category-title">1、病历</view>
            <gui-upload-images ref="uploadImg1" :maxFileNumber="6" :uploadServerUrl="uploadServerUrl"
                               :progress="true" :header="header" fileName="file" :uploadType="true" borderRadius="4rpx"
                               @change="function(event) { onImagesChange(event, '1'); }"
                               @uploaded="function(event) { onImagesUploaded(event, '1'); }" @uploaderror="onUploadError"
                               @removeImg="function(event) { onImageRemoved(event, '1'); }" :delay="2000" />
          </view>

          <!-- 处方用药 -->
          <view class="category-item" v-if="!editMode || editType === '2'">
            <view class="category-title">2、处方用药</view>
            <gui-upload-images ref="uploadImg2" :maxFileNumber="6" :uploadServerUrl="uploadServerUrl"
                               :progress="true" :header="header" fileName="file" :uploadType="true" borderRadius="4rpx"
                               @change="function(event) { onImagesChange(event, '2'); }"
                               @uploaded="function(event) { onImagesUploaded(event, '2'); }" @uploaderror="onUploadError"
                               @removeImg="function(event) { onImageRemoved(event, '2'); }" :delay="2000" />
          </view>

          <!-- 检查 -->
          <view class="category-item" v-if="!editMode || editType === '3'">
            <view class="category-title">3、检查</view>
            <gui-upload-images ref="uploadImg3" :maxFileNumber="12" :uploadServerUrl="uploadServerUrl"
                               :progress="true" :header="header" fileName="file" :uploadType="true" borderRadius="4rpx"
                               @change="function(event) { onImagesChange(event, '3'); }"
                               @uploaded="function(event) { onImagesUploaded(event, '3'); }" @uploaderror="onUploadError"
                               @removeImg="function(event) { onImageRemoved(event, '3'); }" :delay="2000" />
          </view>

          <!-- 检验 -->
          <view class="category-item" v-if="!editMode || editType === '4'">
            <view class="category-title">4、检验</view>
            <gui-upload-images ref="uploadImg4" :maxFileNumber="12" :uploadServerUrl="uploadServerUrl"
                               :progress="true" :header="header" fileName="file" :uploadType="true" borderRadius="4rpx"
                               @change="function(event) { onImagesChange(event, '4'); }"
                               @uploaded="function(event) { onImagesUploaded(event, '4'); }" @uploaderror="onUploadError"
                               @removeImg="function(event) { onImageRemoved(event, '4'); }" :delay="2000" />
          </view>
        </view>

        <!-- 日期选择 -->
        <view class="date-picker-section">
          <view class="gui-form-item">
            <text class="gui-form-label gui-h6 gui-bold" style="width: 150rpx;">记录日期：</text>
            <view class="gui-form-body" style="flex: 1;">
              <gui-datetime @confirm="confirm4" :value="formattedDateTime" ref="timeBegin"
                            :units="['年', '月', '日','时','分']" :isSecond='false' :zIndex="1000" :end="maxDateTime">
                <view class="gui-flex gui-rows gui-space-between fs-32" style="width: 100%; min-width: 100rpx;">
                  <text class="demo gui-icons" style="flex: 1;width: 100%;">{{ formattedDateTime }}</text>
                  <text class="gui-icons">&#xe601;</text>
                </view>
              </gui-datetime>
            </view>
          </view>
        </view>

        <!-- 底部占位，确保内容不被固定按钮遮挡 -->
        <view class="bottom-placeholder"></view>

        <!-- 提交按钮 - 固定在底部 -->
        <view class="fixed-bottom-btn">
          <button class="submit-btn" :disabled="submitLoading || isUploading" @tap="submitUpload">
            {{getSubmitButtonText()}}
          </button>
          <!-- 上传进度提示 -->
          <view v-if="isUploading" class="upload-status-tip">
            <text class="upload-tip-text">图片上传中，请稍候...</text>
          </view>
        </view>
      </view>
    </view>
  </gui-page>
</template>

<script>
import {
  saveMedicalReport
} from '@/api/patient.js'
// Import the gui-upload-images component
import guiUploadImages from '@/GraceUI5/components/gui-upload-images.vue'

export default {
  components: {
    guiUploadImages
  },
  data() {
    return {
      pageLoading: false,
      patientId: '',
      uploadServerUrl: '',
      categoryImagesMap: {
        '1': [], // 病历
        '2': [], // 处方用药
        '3': [], // 检查
        '4': [] // 检验
      },
      formattedDateTime: this.formatCurrentDateTime(),
      isSubmitting: false,
      uploadProgress: 0,
      header: {
        "Authorization": 'Bearer ' + uni.getStorageSync('token')
      },
      editMode: false,
      editId: null,
      editType: null,
      existingImageUrls: [],
      maxDateTime: this.formatCurrentDateTime(),
      // 新增：图片上传状态管理
      submitLoading: false,
      // 新增：全局上传状态管理
      isUploading: false
    }
  },
  computed: {
    // 检查是否有任何图片正在上传
    hasUploadingImages() {
      for (let category in this.categoryImagesMap) {
        const images = this.categoryImagesMap[category];
        if (images.some(img => img.progress > 0 && img.progress < 100)) {
          return true;
        }
      }
      return false;
    }
  },
  onLoad(options) {
    // 获取患者ID
    if (options.patientId) {
      this.patientId = options.patientId;
    }

    // 设置上传地址
    this.uploadServerUrl = this.$common.commentImage;

    // 检查是否是编辑模式
    if (options.mode === 'edit' && options.id && options.type) {
      this.editMode = true;
      this.editId = options.id;
      this.editType = options.type;

      // 设置导航栏标题为"修改诊疗记录"
      uni.setNavigationBarTitle({
        title: '修改诊疗记录'
      });

      // 如果有传入图片数据，加载预览
      if (options.extraData) {
        this.existingImageUrls = decodeURIComponent(options.extraData).split(',').filter(url => url.trim());
        this.loadExistingData();
      }

      // 如果有传入访问日期，设置日期和时间
      if (options.visitDate) {
        try {
          const dateObj = new Date(options.visitDate);
          if (!isNaN(dateObj.getTime())) {
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const day = String(dateObj.getDate()).padStart(2, '0');
            const hour = String(dateObj.getHours()).padStart(2, '0');
            const minute = String(dateObj.getMinutes()).padStart(2, '0');
            this.formattedDateTime = `${year}-${month}-${day} ${hour}:${minute}`;
          }
        } catch (e) {
          console.error('解析日期时间失败:', e);
        }
      }
    } else {
      // 非编辑模式，设置标题为"上传诊疗记录"
      uni.setNavigationBarTitle({
        title: '上传诊疗记录'
      });
    }
  },
  methods: {
    // 加载现有的图片数据
    loadExistingData() {
      if (!this.existingImageUrls || this.existingImageUrls.length === 0) return;

      // 为编辑类型准备图片数组
      const imageList = [];
      this.existingImageUrls.forEach(url => {
        imageList.push({
          url: url,
          progress: 100,
          result: null
        });
      });

      // 只更新编辑类型的图片
      if (this.editType) {
        this.categoryImagesMap[this.editType] = imageList;

        // 使用 setItems 方法而不是直接绑定 :items
        this.$nextTick(() => {
          const refName = `uploadImg${this.editType}`;
          if (this.$refs[refName]) {
            this.$refs[refName].setItems(imageList);
          }
        });

        this.submitLoading = false;
      }
    },

    // 格式化当前日期时间
    formatCurrentDateTime(date = new Date()) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hour}:${minute}`;
    },

    // 日期时间确认回调
    confirm4(res) {
      const selectedDateTime = `${res[0]}-${res[1]}-${res[2]} ${res[3]}:${res[4]}`;

      // 校验是否选择了未来时间
      const selectedTimestamp = new Date(selectedDateTime).getTime();
      const currentTimestamp = new Date().getTime();

      if (selectedTimestamp > currentTimestamp) {
        uni.showToast({
          title: '不能选择未来时间',
          icon: 'none',
          duration: 2000
        });

        // 重置为当前时间
        this.formattedDateTime = this.formatCurrentDateTime();
        return;
      }

      this.formattedDateTime = selectedDateTime;
      this.$forceUpdate();
    },

    // 图片列表变化回调
    onImagesChange(images, category) {
      // 只有在编辑模式下且分类匹配时才处理，或者在新增模式下
      if (this.editMode && category !== this.editType) {
        return;
      }

      // 保存每个分类的图片列表
      const imageList = [];

      // 处理从gui-upload-images组件返回的图片数据
      images.forEach(function(img) {
        imageList.push({
          uid: img.uid, // 添加UID
          url: img.url,
          progress: img.progress || 0,
          result: img.result || null
        });
      });

      this.categoryImagesMap[category] = imageList;

      // 更新全局上传状态和提交按钮状态
      this.updateUploadingStatus();
    },

    // 图片上传完成回调
    onImagesUploaded(images, category) {
      // 只有在编辑模式下且分类匹配时才处理，或者在新增模式下
      if (this.editMode && category !== this.editType) {
        return;
      }

      const imageList = [];

      // 处理上传成功的图片数据
      images.forEach(function(img) {
        imageList.push({
          url: img.url,
          progress: 100,
          result: img.result || null
        });
      });

      this.categoryImagesMap[category] = imageList;

      // 更新全局上传状态和提交按钮状态
      this.updateUploadingStatus();

      // 显示上传成功提示
      uni.showToast({
        title: '图片上传成功',
        icon: 'success',
        duration: 2000
      });
    },

    // 图片上传错误回调
    onUploadError() {
      // 更新全局上传状态
      this.updateUploadingStatus();
    },

    // 图片移除回调
    onImageRemoved() {
      // 更新全局上传状态
      this.updateUploadingStatus();
    },

    // 获取提交按钮文本
    getSubmitButtonText() {
      if (this.isUploading) {
        return '图片上传中...';
      }
      if (this.submitLoading) {
        return '处理中...';
      }
      return this.editMode ? '保存修改' : '提交完成';
    },

    // 更新上传状态的统一方法
    updateUploadingStatus() {
      const hasUploading = this.hasUploadingImages;
      const wasUploading = this.isUploading;

      this.isUploading = hasUploading;
      this.submitLoading = hasUploading;

      // 如果从上传状态变为非上传状态，说明所有图片上传完成
      if (wasUploading && !hasUploading) {
        // 延迟一下再显示提示，确保UI更新完成
        this.$nextTick(() => {
          console.log('所有图片上传完成，可以提交了');
        });
      }
    },

    // 整理并提交医疗报告数据
    submitUpload() {
      // 检查是否有图片正在上传
      if (this.isUploading || this.submitLoading) {
        uni.showToast({
          title: '图片正在上传中，请稍候',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 双重检查：再次验证所有分类的图片上传状态
      if (this.hasUploadingImages) {
        uni.showToast({
          title: '请等待所有图片上传完成',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      let dataList = [];

      if (this.editMode) {
        // 编辑模式：只处理当前编辑的类型
        const categoryImages = this.categoryImagesMap[this.editType];

        if (categoryImages && categoryImages.length > 0) {
          // 收集该分类下所有已上传成功的图片URL
          const uploadedUrls = [];
          categoryImages.forEach(function(img) {
            if (img.progress === 100 && img.url) {
              uploadedUrls.push(img.url);
            }
          });

          // 如果有图片，创建一个对象，extraData用逗号分隔
          if (uploadedUrls.length > 0) {
            dataList.push({
              id: this.editId,
              type: this.editType,
              visitDate: this.formattedDateTime + ':00',
              extraData: uploadedUrls.join(','),
              extraType: "1"
            });
          }
        }
      } else {
        // 新增模式：处理所有类型
        for (let key in this.categoryImagesMap) {
          let item = this.categoryImagesMap[key];
          if (item.length) {
            let visitDate = this.formattedDateTime + ':00';
            let extraData = this.categoryImagesMap[key]
                .filter(img => img.progress === 100)
                .map(img => img.url);

            if (extraData.length > 0) {
              dataList.push({
                type: key,
                visitDate: visitDate,
                extraData: extraData.join(','),
                extraType: "1"
              });
            }
          }
        }
      }

      if (dataList.length === 0) {
        uni.showToast({
          title: '请先上传图片',
          icon: 'none'
        });
        return;
      }

      let data = {
        patientId: this.patientId,
        dataList: dataList
      };

      uni.showLoading({
        title: this.editMode ? '保存中...' : '提交中...'
      });

      saveMedicalReport(data).then(res => {
        uni.hideLoading();
        if (res.code === 200) {
          uni.showToast({
            title: this.editMode ? '修改成功' : '上传成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                uni.navigateBack({
                  delta: 1,
                  success: () => {
                    let type = this.editType;
                    if (type == 2) {
                      type = 1;
                    } else if (type == 1) {
                      type = 2;
                    } else if (type == 3) {
                      type = 4;
                    } else if (type == 4) {
                      type = 5;
                    }
                    uni.$emit('refreshMedicalData', {
                      categoryId: type || 2
                    });
                  }
                });
              }, 1500);
            }
          });
        } else {
          uni.showToast({
            title: res.msg || (this.editMode ? '修改失败' : '上传失败'),
            icon: 'none'
          });
        }
      }).catch(() => {
        uni.hideLoading();
        uni.showToast({
          title: this.editMode ? '修改失败' : '上传失败',
          icon: 'none'
        });
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1px solid #eee;
  background-color: #fff;
}

.upload-header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.upload-container {
  padding: 40rpx;
  background-color: #fff;
}

.category-list {
  margin-bottom: 30rpx;
}

.category-item {
  margin-bottom: 50rpx;

  /* Deep selector to override child component styles */
  ::v-deep .gui-add-list-items {
    width: 202rpx;
    height: 202rpx;
    overflow: hidden;
    margin: 9rpx;
    background-color: #F8F8F8;
    font-size: 0;
    position: relative;
  }

  ::v-deep .gui-add-list-remove {
    width: 60rpx;
    height: 60rpx;
    line-height: 60rpx;
    text-align: center;
    font-size: 44rpx;
    position: absolute;
    z-index: 5;
    right: 0;
    top: 0;
  }

  ::v-deep .gui-add-list-img {
    width: 202rpx;
    height: 202rpx;
    object-fit: cover;
  }

}

.category-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.date-picker-section {
  margin: 30rpx 0;

  .gui-form-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 20rpx 0;
  }

  .gui-form-label {
    flex-shrink: 0;
  }

  .gui-form-body {
    flex: 1;
    margin-left: 20rpx;
  }
}

.date-picker-title {
  font-size: 32rpx;
  color: #333;
  margin-right: 10rpx;
  white-space: nowrap;
}

.date-picker-value {
  flex: 1;
  display: flex;
  flex-direction: row;
  margin-top: 5rpx;
  align-items: center;
}

.picker-view {
  padding: 10rpx 0;
  min-width: 180rpx;
  font-size: 32rpx;
  color: #333;
}

.date-time-separator {
  margin: 0 8rpx;
}

.submit-btn-section {
  margin-top: 80rpx;
  padding-bottom: 40rpx;
}

.submit-btn {
  height: 90rpx;
  line-height: 90rpx;
  background-color: #7784eb;
  color: #fff;
  border-radius: 10rpx;
  font-size: 32rpx;
  width: 100%;
}

.submit-btn[disabled] {
  background-color: #999;
  color: rgba(255, 255, 255, 0.6);
}

/* 固定在底部的按钮容器 */
.fixed-bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx 40rpx;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 998;
}

/* 上传状态提示 */
.upload-status-tip {
  margin-top: 20rpx;
  text-align: center;
}

.upload-tip-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部占位，确保内容不被固定按钮遮挡 */
.bottom-placeholder {
  height: 160rpx;
}

/* 确保日期选择器弹窗层级更高 */
::v-deep .gui-dateBT-shade {
  z-index: 1000 !important;
}

/* 日期选择区域样式优化 */
.date-picker-section {
  margin: 30rpx 0;
}

.gui-form-body {
  flex: 1;
  min-width: 0;
}
</style>
