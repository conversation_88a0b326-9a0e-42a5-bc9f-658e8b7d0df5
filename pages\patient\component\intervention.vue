<template>
	<scroll-view :scroll-y="true" style="height:100%;padding: 20rpx;">
		<view class="">
			<!-- <view class=""> -->
				<view class="l-timeline-l" v-for="(value, key, index) in getTaskData">
					<view class="list-in" v-for="(i,d) in  value">
						<view class="time-line">
							<view class="l-icon"></view>
							<view class="time-line-i"></view>
						</view>
						<view style="flex: 1;padding-left: 20rpx;">
						<view class="l-time fs-26" style="color: rgb(144, 147, 153);">{{`${String(d).substring(0, 10)}`}} <span
								class=" ml-20 fs-26" style="color: #1d1d1d;">{{`${String(d).substring(10, d.length)}`}} </span>
						</view>
						<!-- <view class="l-icon"></view> -->
						<view class="l-content ganyuFu">
							<view class="">
								<view class="gui-accordion mb-10" v-for="(val1,key1) in i">
									<view @click="toItem(val1[0])" class="gui-bold" v-if="val1.length>0 && getProgrammeTypeName(val1)">
										{{ getProgrammeTypeName(val1) }}
									</view>
									<view class="fs-26 gui-accordion-row" v-if="a.taskType!==null" v-for="(a,b) in val1" @click="toItem(a)">
										<view style="width: 25rpx;" v-show="a.questionnaireName">
											<text class="yuandian"></text>
										</view>
										<view class="">
											<view >
												<span class="gui-bold pr-15" v-if="a.taskType === 20">复诊 </span>
												<span :class="(a.taskType === 1 || a.taskType === 2 )?'gui-color-blue gui-underline':''"
													v-if="a.taskType === 1 || a.taskType === 2 || a.taskType === 3 || a.taskType === 4 || a.taskType === 5 || a.taskType === 12 || a.taskType === 20">
													{{a.questionnaireName}}
												</span>
												<span v-if="a.taskType === 6">
													情志调理：{{ a.questionnaireName }}
												</span>
												<span v-if="a.taskType === 7">
													起居调理：{{ a.questionnaireName }}
												</span>
												<span v-if="a.taskType === 8">
													饮食调理：{{ a.questionnaireName }}
												</span>
												<span v-if="a.taskType === 9">
													运动调理：{{ a.questionnaireName }}
												</span>
												<span v-if="a.taskType === 10">
													非药物调理：{{ a.questionnaireName }}
												</span>
												<span v-if="a.taskType === 11">
													膳食调理：{{ a.questionnaireName }}
												</span>
												<span style="color:#6f6f6f;padding-left: 10px;font-size: 26rpx;"
													v-if="a.taskType === 3">
													每{{ (a.frequencyUnit === 1 && '天') || (a.frequencyUnit === 2 && '周') || (a.frequencyUnit === 3 && '月') }}
													{{ (a.frequencyDays && `${a.frequencyDays}次`) || (a.frequencyDays === 1 && '1次' || '') }}
													{{ (a.frequencyDates && `(第${a.frequencyDates.replace(/,/g,'/')}天执行)`) || '' }}
												</span>
												<span style=" color:#6f6f6f;padding-left: 10px;font-size: 26rpx;"
													v-if="a.taskType === 4">
													每{{ (a.frequencyUnit === 1 && '天') || (a.frequencyUnit === 2 && '周') || (a.frequencyUnit === 3 && '月') }}
													{{ (a.frequencyDays && `${a.frequencyDays}次`) || (a.frequencyDays === 1 && '1次' || '') }}
													{{ (a.frequencyDates && `(第${a.frequencyDates.replace(/,/g,'/')}天执行)`) || '' }}
												</span>
												<span style="color:#6f6f6f;padding-left: 10px;font-size: 26rpx;"
													v-if="a.taskType === 5">
													<!-- <span
														style="padding-right: 5px">{{ setGoaFangUse(a.frequencyUse) }}</span> -->
													<!-- 每次{{ a.singleQuantity && a.singleQuantity || 0 }}{{ setGoaFangSingleUnit(a.singleUnit) }} -->
													每{{ (a.frequencyUnit === 1 && '天') || (a.frequencyUnit === 2 && '周') || (a.frequencyUnit === 3 && '月') }}{{ (a.frequencyDays && `${a.frequencyDays}次`) || (a.frequencyDays === 1 && '1次' || '') }}{{ (a.frequencyDates && `(第${a.frequencyDates.replace(/,/g,'/')}天执行)`) || '' }}
												</span>
												<span style=" color:#6f6f6f; padding-left: 10px;font-size: 26rpx;"
													v-if="a.times">
													执行时间：{{a.times}}
												</span>
											</view>
											<view style=" color:#6f6f6f; padding-left: 10px;font-size: 26rpx;"
												v-if="a.remark">
												医生嘱托：{{ a.remark}}
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						</view>
					</view>
				</view>
				<!-- <gui-empty
					v-if="getTaskData.length == 0 ">
					<view slot="img" class="gui-flex gui-rows gui-justify-content-center">
						<image class="gui-empty-img"
							src="https://images.weserv.nl/?url=https://upload-images.jianshu.io/upload_images/15372054-1f849183cebb80b1.png"></image>
					</view>
					<text slot="text"
						class="gui-text-small gui-block-text gui-text-center gui-margin-top" style="color:#9DABFF;">暂无数据 ......</text>
				</gui-empty> -->
			<!-- </view> -->
		</view>

	</scroll-view>
</template>

<script>
	export default {
		props:{
			info:{
				type:Object,
				default:{}
			},
		},
		data(){
			return {
				getTaskData:[],
				goafangUse: [],
				singleUnit: [],
			}
		},
		mounted(){
			// this.getTaskList()
		},
		methods:{
			getProgrammeTypeName(val) {
			  // console.log(val,'5555')
			  return val?.find(item => item.programmeTypeName)?.programmeTypeName || ''
			},
			// 查看干预
			getTaskList(info) {
				this.$common.RequestData({
					url: this.$common.getTaskList,
					data: {
						patientId: info.patientId?info.patientId:this.info.patientId,
						// patientId: "452731198502041246",
						orderByColumn: "create_time",
						isAsc: "desc"
					},
					method: 'get',
				}, res => {
					this.getTaskData = res.rows
					this.getDict()
				})
			},
			setGoaFangUse(value) {
				return this.goafangUse?.find(item => item.dictValue === value)?.dictLabel || ''
			},
			setGoaFangSingleUnit(value) {
				return this.singleUnit?.find(item => item.dictValue === value)?.dictLabel || ''
			},
			toItem(item){
				console.log('查看详情===',item)
				if (item.taskType == 2 ) {
							// 宣教
							let taskId = item.taskId?item.taskId:item.questionnaireId;
							let taskType = item.taskId?item.taskType:('groupMsg&createBy='+item.createBy+'&createTime='+item.createTime);
							this.$common.navTo('/pages/tasks/missionary?id='+taskId+'&taskType='+taskType)
				} else if(item.taskType == 1) {
							this.$common.navTo('/pages/tasks/questionnaire?id='+item.taskId+'&taskType='+item.taskType+'&openType=3')
							// 问卷
				}else if(item.taskType == 12) {
						// 宣教方案
						// this.$common.navTo('/pages/tasks/missionary?id='+item.questionnaireId+'&taskType='+item.taskType)
				}
			},
			getDict() {
				// 获取膏方用法的字典
				this.$common.RequestData({
					url: `${this.$common.getDicts}cream_formula`,
					method: 'get',
				}, res => this.goafangUse = res.data)
				// 膏方单量单位的字典
				this.$common.RequestData({
					url: `${this.$common.getDicts}plaster_single_unit`,
					method: 'get',
				}, res => this.singleUnit = res.data)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.gui-accordion-row{
		display: flex;
		flex-direction: row;
		// align-items: center;
		margin: 10rpx 0;
	}
	.list-in{
		display: flex;
		flex-direction: row;
	}
	.item-row{
		flex:1;
		display: flex;
		justify-content: space-between;
	}
	.gui-accordion{
		display: flex;
		flex-direction: column;
		background-color: #f5f6f8;
		padding:10rpx 20rpx;
		border-radius: 10rpx;
	}
	.list-item-name{
		font-weight: bold;
	}
	.list-item{
		padding: 10rpx 0;

	}
	.time-line{
		display: flex;
		flex-direction: column;
		width: 30rpx;
		position: relative;
		justify-content: center;
		align-items: center;
		.time-line-i{
			width: 2rpx;
			background-color: #aaa;
			height: 100%;
		}
	}
	.gui-rows-title{
		flex:1
	}
	.visitDate-row{
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}
	.navscroll{
		white-space: nowrap;
		// height: 100rpx;
		::v-deep ::-webkit-scrollbar {
			width: 4px !important;
			height: 1px !important;
			overflow: auto !important;
			background: transparent !important;
			-webkit-appearance: auto !important;
			display: block;
		}
		.garce-items{
			display: inline-block; // 设置为行内块
		}
	}
	.prescription{
		display: flex;
		flex-direction: row;
		flex-flow:row wrap;
	}
	.prescription-item{
		width: 30%;
		line-height: 26px;
		text-align: center;
	}
	>>>.gui-scroll-x-items {
		align-items: center;
	}

	.demo-nav {
		padding: 15rpx 30rpx;
	}

	.demo-text {
		line-height: 200rpx;
		padding-bottom: 3000px;
	}

	.grace-box-banner .garce-items .line2 {
		font-size: 28rpx;
		color: #008AFF;
		display: inline-block; // 设置为行内块
		border: 1px solid #62dbff;
		padding: 0 15rpx;
		margin: 0 10rpx;
		border-radius: 20rpx;
	}

	.line2.active {
		color: #ffffff !important;
		/* font-weight:bold; */
		background-color: #008AFF;
	}

	.l-timeline-l {
		// display: flex;
		// flex-direction: row;
		// border-left: 1px solid #aaaaaa;
	}

	.l-timeline-b {
		/* border-bottom: 2px solid #008AFF; */
	}

	.l-time {
		// position: relative;
		// top: -15rpx;
	}

	.acolor {
		background-color: #7784eb;

	}

	.bcolor {
		background-color: #7784eb;
		height: 40rpx;
		line-height: 40rpx;
		width: 190rpx;
	}

	.tagicon {
		margin-right: 10rpx;
		height: 40rpx;
		width: 6rpx;
		border-radius: 5rpx;
		background: #008AFF;
		display: block;
	}

	.l-icon {
		background: #008AFF;
		width: 25rpx;
		height: 25rpx;
		border-radius: 25rpx;
		position: absolute;
		top: 0;
		// position: relative;
		// top: -50rpx;
		// left: -15rpx;
	}

	.l-content {
		// padding-bottom: 30px;
		margin-top: 30rpx;
		margin-bottom: 30rpx;
		// position: relative;
		// top: -25rpx;
	}

	.gui-accordion-icon {
		width: 50rpx;
		// height: 80rpx;
		// line-height: 80rpx;
		font-size: 32rpx;
	}

	.gui-flex-direction-row {
		flex-direction: row-reverse;
	}

	.gui-accordion-title-text {
		// width: 200rpx;
		flex: 1;
	}

	>>>.gui-block-text {
		font-size: 30rpx !important;
	}

	.resuimg {
		width: 24px;
		height: 24px;
		font-size: 24px;
		line-height: 24px;
		vertical-align: middle;
		color: rgba(0, 186, 173, 1);
	}

	.resutext {
		width: 70px;
		height: 21px;
		font-size: 14px;
		text-align: left;
		font-weight: bold;
		line-height: 24px;
		padding-left: 8rpx;
		color: rgba(80, 80, 80, 1);
	}

	.propose_net {
		width: 100%;
		height: auto;
		margin-top: 5rpx;
		padding: 0 10rpx;
		text-indent: 2em;
		line-height: 52rpx;
		overflow-x: scroll;
	}

	.end_text {
		width: 325px;
		height: 42px;
		font-size: 14px;
		text-align: center;
		margin: 20% auto 0;
		color: rgba(212, 48, 48, 1);
	}

	.propose {
		width: 92%;

	}


	/* 干预 */
	.ganyuFu {
		// background-color: #f5f6f8;
		// line-height: 50rpx;
		// padding-top: 10rpx;
		// width: 85%;
		// max-width: 85%;
		border-radius: 10rpx;
		// padding: 5rpx 10rpx 5rpx 5rpx;
	}

	.ios {
		font-size: 35rpx;
		margin: 10rpx 10rpx;
		color: #1aca0d;
	}

	/* 疗效评估 */
	.laioxiaoFu {
		/* display: flex; */
		background-color: #f5f6f8;
		line-height: 50rpx;
		padding-top: 10rpx;
		width: 85%;
		max-width: 85%;
		border-radius: 10rpx;
		padding: 5rpx 10rpx 5rpx 5rpx;
	}

	.laioxiaoZi {
		display: flex;
		align-items: center;
		line-height: 50rpx;
		padding: 10rpx 0rpx 10rpx 15rpx;
	}

	.Aios {
		font-size: 50rpx;
		margin: 10rpx 20rpx 10rpx 10rpx;
		color: #1aca0d;
		/* height: 100% !important; */
		display: flex;
		align-items: center;
		float: left;
	}

	.yuandian {
		// float: left;
		display: block;
		// line-height: 50rpx;
		top: 10px;
		width: 8px;
		height: 8px;
		margin-right: 10rpx;
		border-radius: 20px;
		/* background: #cbd0db; */
		background: #6f6f6f;
		margin-top: 10rpx !important;
		// margin: 8px;

	}

	/* 舌面详情按钮 */
	.sm_xq {
		float: right;
		margin-right: 30rpx;
	}

	/* 头像 */
	.head_img {
		border-radius: 10rpx;
		border: 1px solid #cdcdcd;
	}

	>>>.gui-list-title-text {
		font-size: 28rpx !important;
	}
</style>
