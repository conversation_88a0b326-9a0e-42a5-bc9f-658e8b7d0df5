import Http from "../net/Http.js"
import noHttp from "../net/noHttp.js"

// 已读未读
export function isReadMsgI(data){
	return Http.post('/web/im/isReadMsg',data)
}
// 获取会话列表
export function mySessionList(data){
	return Http.post('/web/im/mySessionList',data)
}
// 获取未读消息总数
export function msgCount(data){
	return Http.post('/web/im/msgCount',data)
}
// 标记已读
export function msgReaded(data){
	return Http.post('/web/im/msgReaded',data)
}
// 会话消息历史记录
export function sessionRecord(data){
	return Http.post('/web/im/sessionRecord',data)
}
// 获取用户的im用户信息
export function getImUser(data){
	return Http.post('/web/im/getMiniImUser',data)
}

// 用户撤回消息
export function revokeMessage(data){
	return Http.post('/web/im/revokeMessage',data)
}

// 在线咨询 - 宣教分类-字典库  
export function missionaryType(data){
	return Http.get('/doctor/patient/type/missionary_type',data)
}
// 在线咨询 - 宣教分类-科室库  
export function channelList(data){
	return Http.get('/doctor/article/channel/list',data)
}
// 在线咨询 - 宣教列表  
export function mbArticleList(data){
	return Http.get('/doctor/article/list',data)
}
// 在线咨询 - 问卷分类  
export function questionnaireType(data){
	return Http.get('/doctor/patient/type/questionnaire_type',data)
}

// 在线咨询 - 宣教列表  
export function articleList(data){
	return Http.get('/zwb/article/list',data)
}

// 在线咨询 - 问卷列表  
export function questionnaireList(data){
	return Http.get('/zwb/questionnaire/list',data)
}
// 在线咨询 - 问卷列表  
export function questionnaireLists(data){
	return Http.get('/doctor/article/questionnaire/list',data)
}

// 在线咨询 - 问卷列表  
export function sandTask(data){
	return Http.post('/zwb/task',data)
}

// 在线咨询 - 查询干预数量  
export function getTaskIdsByBatchId(data){
	return Http.get('/zwb/task/getTaskIdsByBatchId',data)
}

// 在线咨询 - 刷新干预数据  
export function imFlush(data){
	return Http.post('/web/im/flush',data)
}

// 在线咨询 - 发送干预数据  
export function imSendMsg(data){
	return Http.post('/wx/im/sendMsg',data)
}

// 任务详情
export function getFollowTaskInfo(data){
	return Http.get('/wx/task/getFollowTaskInfo',data)
}
// 宣教、问卷任务id
export function taskDtail(data){
	return Http.get('/zwb/task/'+data)
}
// 问卷详情
export function getInfoDtail(data,patientId,id){
	return Http.get('/zwb/template/getInfo/'+data +'/'+patientId +'/'+id)
}
// 问卷详情
export function listDtail(data){
	return Http.get('/zwb/template/list',data)
}

// 宣教详情
export function articleDtail(data){
	return Http.get('/zwb/article/'+data)
}

// 宣教回复
export function readFollowTaskAirtcle(data){
	return Http.post('/wx/task/readFollowTaskAirtcle',data)
}
// 文章积分 status  1为转发  其他状态为空
export function getArticle(data){
	return Http.get('/wx/task/article',data)
}
// 问卷提交
export function submitQuestionnaire(data){
	return Http.post('/wx/task/submitQuestionnaire',data)
}

// 患者详情
export function patientGetInfo(data){
	return Http.get('/doctor/patient/getInfo/'+data)
}
