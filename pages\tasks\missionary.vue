<template>
	<view>
		<view v-if="obj.status === -1">
			<view style="padding:50rpx 0;">
				<text slot="text"class="gui-block-text gui-text-center gui-margin-top text-zhuti" >此内容已经被撤回...</text>
			</view>
		</view>
		<view v-else>
			<!-- 视频播放区域 -->
			<view v-show="obj.videoUrl">
				<video id="myVideo" 
				class="gui-course-video" 
				:src="obj.videoUrl" 
				:poster="obj.filePath" controls></video>
			</view>
	
			<view v-if="obj.content && obj.articleType != 3">
				<view class="gui-h3 fs-36 font-bold p-20">{{obj.articleName || '-'}}</view>
				<view class="d-flex jc-between px-20 fs-30 text-grey-74 my-15">
					<view>下发人：{{obj.createBy || '-'}}</view>
					<view>下发时间：{{obj.createTime || '-'}}</view>
				</view>
				
				<view v-if="obj.content && obj.articleType != 3" class="p-20" style="line-height: 50rpx;text-align: justify;word-wrap:break-word">
					<mp-html :content="obj.content" />
				</view>
			</view>
			<view v-if="obj.content && obj.articleType == 3">
				<web-view :src="obj.content.replace(/<[^>]+>/g, '')"></web-view>
			</view>
		</view>
	</view>
</template>

<script>
	import {getFollowTaskInfo,articleDtail,taskDtail} from '@/api/im.js'
	export default {
		// components:{uParse},
		data() {
			return {
				id:"",
				taskType:'',
				type:"",
				reson:"",
				obj:{
					articleInfo:{},
					replyDoubt:{},
					taskInfo:{},
				},
				textareaVal: '', // 文本内容
				maxWords: 50, // 最多字符数
				textareatimer: null, // 延迟记录
				fedList: [{
						options: '完全理解',
						img: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_ok.png',
						img2: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_okw.png',
						value:"1"
					},
					{
						options: '基本理解',
						img: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_lit.png',
						img2: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_litw.png',
						value:"2"
					},
					{
						options: '不理解',
						img: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_qu.png',
						img2: 'https://img.starup.net.cn/xmkj/zwb/img/zy_btn_quw.png',
						value:"3"
					},
				],
				fedIndex: '',
			}
		},
		onLoad(options) {
			this.id = options.id;
			this.taskType = options.taskType
			this.getDetail();
		},
		methods: {
			getDetail(){
				taskDtail(this.id).then(res=>{
					if (res.data.questionnaireId) {
						articleDtail(res.data.questionnaireId).then(res=>{
							console.log('文章内容==',res.data.content.replace(/<[^>]+>/g, ''))
							if (res.data.articleType == 3) {
								let src = res.data.content.replace(/<[^>]+>/g, '')
								window.location.href = src
								// window.location.href(src)
								return
							}
							this.obj = res.data
							this.type = res.data.isUnderstand ? res.data.isUnderstand : ""
							this.reson = res.data.incomReason ? res.data.incomReason : ""
						})
					}
				})
				
				// getFollowTaskInfo({
				// 	id:this.id,
				// 	taskType:this.taskType
				// }).then(res=>{
				// 	console.log('文章内容==',res.data.articleInfo.content.replace(/<[^>]+>/g, ''))
				// 	if (res.data.articleInfo.articleType == 3) {
				// 		let src = res.data.articleInfo.content.replace(/<[^>]+>/g, '')
				// 		window.location.href = src
				// 		// window.location.href(src)
				// 		return
				// 	}
				// 	this.obj = res.data
				// 	this.type = res.data.taskInfo.isUnderstand ? res.data.taskInfo.isUnderstand : ""
				// 	this.reson = res.data.taskInfo.incomReason ? res.data.taskInfo.incomReason : ""
				// })
			}
		}
	}
</script>

<style scoped>
	.gui-course-video{width:750rpx;}
	.modal-btns {
		line-height: 88rpx;
		font-size: 26rpx;
		text-align: center;
		width: 200rpx;
	}

	.submit {
		width: 50%;
		line-height: 70rpx;
		position: fixed;
		bottom: 10rpx;
		left: 25%;
	}
	
	.blj-box{
		flex: 1;
		text-align: center;
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx 0;
		margin:30rpx 10rpx;
	}
	
	.blj-box2{
		flex: 1;
		text-align: center;
		background-color: #8FC17B;
		border-radius: 10rpx;
		padding: 30rpx 0;
		margin:30rpx 10rpx;
	}
</style>
